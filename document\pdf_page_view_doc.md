# PDFPageView 模块文档

## 模块简介

PDFPageView 是 PDF.js 库中负责单个 PDF 页面渲染和展示的组件。它继承自 BasePDFPageView 基础类，处理页面的缩放、旋转、注释、文本层和交互功能。PDFPageView 是 PDFViewer 中的核心渲染单元，负责将 PDF 页面转换为可视化的 HTML 元素。

## 常量

PDFPageView 使用以下常量：

- `DEFAULT_SCALE`: 默认缩放比例
- `LAYERS_ORDER`: 图层顺序映射，定义了各个图层的 z-index 顺序
  - `canvasWrapper`: 0
  - `textLayer`: 1
  - `annotationLayer`: 2
  - `annotationEditorLayer`: 3
  - `xfaLayer`: 3
- `TextLayerMode`: 文本层模式枚举
  - `DISABLE`: 禁用文本层
  - `ENABLE`: 启用文本层
  - `ENABLE_PERMISSIONS`: 启用带权限的文本层
- `AnnotationMode`: 注释模式枚举
  - `DISABLE`: 禁用注释
  - `ENABLE`: 启用注释
  - `ENABLE_FORMS`: 启用表单注释
  - `ENABLE_STORAGE`: 启用注释存储
- `RenderingStates`: 渲染状态枚举
  - `INITIAL`: 初始状态
  - `RUNNING`: 运行中
  - `PAUSED`: 暂停
  - `FINISHED`: 完成

## 属性

PDFPageView 类包含以下主要属性：

### 公共属性

- `div`: 页面的 DOM 容器元素
- `pdfPage`: 与此视图关联的 PDF 页面对象
- `id`: 页面 ID (通常是页码)
- `renderingId`: 渲染 ID
- `scale`: 缩放比例
- `viewport`: 页面视口对象
- `rotation`: 旋转角度
- `pdfPageRotate`: PDF 页面自身的旋转角度
- `pageLabel`: 页面标签
- `imageResourcesPath`: 图像资源路径
- `eventBus`: 事件总线
- `renderingQueue`: 渲染队列
- `textLayer`: 文本层
- `annotationLayer`: 注释层
- `annotationEditorLayer`: 注释编辑器层
- `xfaLayer`: XFA 层
- `structTreeLayer`: 结构树层
- `drawLayer`: 绘图层
- `detailView`: 详细视图
- `l10n`: 本地化对象
- `maxCanvasPixels`: 最大画布像素数
- `maxCanvasDim`: 最大画布尺寸
- `enableDetailCanvas`: 是否启用详细画布

### 私有属性

- `#canvasWrapper`: 画布包装器
- `#annotationMode`: 注释模式
- `#textLayerMode`: 文本层模式
- `#layerProperties`: 图层属性
- `#enableAutoLinking`: 是否启用自动链接
- `#hasRestrictedScaling`: 是否有受限缩放
- `#needsRestrictedScaling`: 是否需要受限缩放
- `#originalViewport`: 原始视口
- `#previousRotation`: 前一个旋转角度
- `#userUnit`: 用户单位
- `#useThumbnailCanvas`: 缩略图画布使用选项
- `#layers`: 图层数组
- `#isEditing`: 是否正在编辑

## 方法

### 构造函数

```javascript
constructor(options)
```

创建一个新的 PDFPageView 实例。

**参数:**
- `options`: 配置选项对象
  - `container`: 容器元素
  - `defaultViewport`: 默认视口
  - `id`: 页面 ID
  - `scale`: 缩放比例 (可选，默认为 DEFAULT_SCALE)
  - `textLayerMode`: 文本层模式 (可选，默认为 TextLayerMode.ENABLE)
  - `annotationMode`: 注释模式 (可选，默认为 AnnotationMode.ENABLE_FORMS)
  - `imageResourcesPath`: 图像资源路径 (可选)
  - `maxCanvasPixels`: 最大画布像素数 (可选)
  - `eventBus`: 事件总线
  - `renderingQueue`: 渲染队列 (可选)
  - `layerProperties`: 图层属性 (可选)
  - `l10n`: 本地化对象 (可选)
  - `enableDetailCanvas`: 是否启用详细画布 (可选，默认为 true)
  - `pageColors`: 页面颜色 (可选)

### 核心方法

- `setPdfPage(pdfPage)`: 设置 PDF 页面对象
- `destroy()`: 销毁页面视图并释放资源
- `reset()`: 重置页面视图到初始状态
- `update(scale, rotation, optionalContentConfigPromise)`: 更新页面视图的缩放和旋转
- `draw()`: 绘制页面内容
- `paint(canvasWrapper, paintTask)`: 绘制页面到画布
- `render(canvasContext, viewport, intent, transform, imageLayer, canvasFactory, annotationMode)`: 渲染页面到画布上下文
- `getOperatorList()`: 获取页面的操作符列表
- `buildSvg()`: 构建 SVG 版本的页面
- `hasEditableAnnotations()`: 检查页面是否有可编辑的注释
- `setPageLabel(label)`: 设置页面标签

### 私有方法

- `#addLayer(div, name)`: 添加图层到页面
- `#setDimensions()`: 设置页面尺寸
- `#dispatchLayerRendered(name, error)`: 分发图层渲染完成事件
- `#renderAnnotationLayer()`: 渲染注释图层
- `#renderAnnotationEditorLayer()`: 渲染注释编辑器图层
- `#renderDrawLayer()`: 渲染绘图图层
- `#renderXfaLayer()`: 渲染 XFA 图层
- `#renderTextLayer()`: 渲染文本图层
- `#buildXfaTextContentItems()`: 构建 XFA 文本内容项
- `#finishPaintTask(paintTask, error)`: 完成绘制任务

## 工作流程

### 页面初始化与渲染流程

1. 创建 PDFPageView 实例
2. 调用 `setPdfPage()` 方法设置 PDF 页面对象
3. 调用 `draw()` 方法开始渲染过程
4. 创建各种图层 (画布、文本、注释等)
5. 渲染 PDF 内容到画布
6. 渲染辅助图层 (文本、注释等)
7. 分发渲染完成事件

```mermaid
graph TD
    A[创建 PDFPageView 实例] --> B[设置 PDF 页面对象]
    B --> C[调用 draw 方法]
    C --> D[创建画布图层]
    C --> E[创建文本图层]
    C --> F[创建注释图层]
    D --> G[渲染 PDF 内容到画布]
    E --> H[渲染文本内容]
    F --> I[渲染注释]
    G --> J[分发页面渲染完成事件]
    H --> J
    I --> J
```

### 页面更新流程

1. 调用 `update()` 方法更新缩放或旋转
2. 更新视口对象
3. 设置新尺寸
4. 重置页面视图
5. 重新绘制页面内容

```mermaid
graph TD
    A[调用 update 方法] --> B[更新视口对象]
    B --> C[设置新尺寸]
    C --> D[重置页面视图]
    D --> E[重新绘制页面内容]
```

### 文本内容处理流程

1. 获取页面文本内容
2. 创建文本图层
3. 渲染文本内容
4. 应用文本高亮 (如果有)
5. 分发文本图层渲染完成事件

```mermaid
graph TD
    A[获取页面文本内容] --> B[创建文本图层]
    B --> C[渲染文本内容]
    C --> D[应用文本高亮]
    D --> E[分发文本图层渲染完成事件]
```

## 使用示例

### 基本初始化与渲染

```javascript
// 创建必要的组件
const eventBus = new EventBus();
const viewport = new PageViewport({
  viewBox: [0, 0, 595.28, 841.89],
  scale: 1,
  rotation: 0
});

// 创建 PDFPageView 实例
const pageView = new PDFPageView({
  container: document.getElementById('pageContainer'),
  id: 1,
  scale: 1.0,
  defaultViewport: viewport,
  eventBus,
  textLayerMode: TextLayerMode.ENABLE,
  annotationMode: AnnotationMode.ENABLE_FORMS,
  imageResourcesPath: '/images/'
});

// 加载 PDF 页面并渲染
pdfDocument.getPage(1).then(pdfPage => {
  pageView.setPdfPage(pdfPage);
  return pageView.draw();
});
```

### 更新页面缩放与旋转

```javascript
// 更新页面缩放比例
pageView.update(2.0, 0);  // 缩放比例为 2.0，旋转角度保持不变

// 更新页面旋转角度
pageView.update(pageView.scale, 90);  // 保持当前缩放比例，旋转 90 度

// 同时更新缩放和旋转
pageView.update(1.5, 180);  // 缩放比例为 1.5，旋转 180 度
```

### 销毁页面视图

```javascript
// 在不再需要页面视图时销毁它，释放资源
pageView.destroy();
```

## 注意事项

1. PDFPageView 是重资源组件，使用完毕后应调用 `destroy()` 方法释放资源。
2. 过大的缩放比例可能导致性能问题，特别是在移动设备上。
3. 文本层渲染取决于 PDF 文档中的文本内容，有些 PDF 可能没有可选择的文本。
4. 注释渲染受到 PDF 文档权限和注释模式设置的影响。
5. 页面渲染是异步过程，应使用事件监听器来处理渲染完成事件。 