<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Native.js 简化版测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            text-align: center;
        }

        h1 {
            color: #333;
            margin-bottom: 30px;
        }

        #flipbook {
            margin: 50px auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            border-radius: 10px;
            overflow: hidden;
            background: white;
        }

        .page {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            cursor: pointer;
            user-select: none;
        }

        .page:nth-child(even) {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .page:nth-child(3n) {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .page:nth-child(4n) {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .page-number {
            position: absolute;
            bottom: 20px;
            right: 20px;
            font-size: 18px;
            opacity: 0.8;
        }

        .page-content {
            text-align: center;
        }

        .page-title {
            font-size: 32px;
            margin-bottom: 20px;
        }

        .page-text {
            font-size: 16px;
            max-width: 300px;
            line-height: 1.6;
            opacity: 0.9;
        }

        .controls {
            margin: 30px 0;
        }

        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 0 10px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .info {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .status {
            font-weight: bold;
            color: #667eea;
        }

        .instructions {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }

        .instructions h3 {
            margin-top: 0;
            color: #667eea;
        }

        .instructions ul {
            text-align: left;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Native.js 简化版翻页效果测试</h1>
        
        <div class="instructions">
            <h3>使用说明</h3>
            <ul>
                <li>点击页面左侧：上一页</li>
                <li>点击页面右侧：下一页</li>
                <li>使用下方按钮控制翻页</li>
                <li>支持键盘左右箭头键翻页</li>
            </ul>
        </div>

        <div id="flipbook">
            <div class="page">
                <div class="page-content">
                    <div class="page-title">欢迎</div>
                    <div class="page-text">这是使用原生JavaScript实现的简化版翻页效果</div>
                </div>
                <div class="page-number">1</div>
            </div>
            <div class="page">
                <div class="page-content">
                    <div class="page-title">特性</div>
                    <div class="page-text">✓ 无jQuery依赖<br>✓ 简洁的API<br>✓ 流畅的动画<br>✓ 事件支持</div>
                </div>
                <div class="page-number">2</div>
            </div>
            <div class="page">
                <div class="page-content">
                    <div class="page-title">兼容性</div>
                    <div class="page-text">支持现代浏览器<br>Chrome, Firefox, Safari, Edge</div>
                </div>
                <div class="page-number">3</div>
            </div>
            <div class="page">
                <div class="page-content">
                    <div class="page-title">性能</div>
                    <div class="page-text">使用CSS3变换<br>流畅的动画效果<br>轻量级实现</div>
                </div>
                <div class="page-number">4</div>
            </div>
            <div class="page">
                <div class="page-content">
                    <div class="page-title">API</div>
                    <div class="page-text">简洁的API设计<br>易于使用<br>完整的事件支持</div>
                </div>
                <div class="page-number">5</div>
            </div>
            <div class="page">
                <div class="page-content">
                    <div class="page-title">感谢</div>
                    <div class="page-text">感谢使用Native.js<br>原生JavaScript翻页库</div>
                </div>
                <div class="page-number">6</div>
            </div>
        </div>

        <div class="controls">
            <button class="btn" onclick="previousPage()">← 上一页</button>
            <button class="btn" onclick="nextPage()">下一页 →</button>
            <button class="btn" onclick="goToPage(1)">首页</button>
            <button class="btn" onclick="goToPage(3)">第3页</button>
            <button class="btn" onclick="toggleDisplay()">切换显示模式</button>
        </div>

        <div class="info">
            <div class="status" id="status">正在初始化...</div>
        </div>
    </div>

    <script src="Native_Simple.js"></script>
    <script>
        let flipbook;
        let currentPage = 1;
        let isDouble = true;

        // 更新状态
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }

        // 初始化翻页书
        function initFlipbook() {
            try {
                flipbook = document.getElementById('flipbook');
                
                // 初始化翻页效果
                flipbook.turn({
                    width: 800,
                    height: 600,
                    display: 'double',
                    duration: 600,
                    when: {
                        turning: function(event) {
                            var page = event.detail[0];
                            updateStatus(`正在翻到第 ${page} 页...`);
                        },
                        turned: function(event) {
                            var page = event.detail[0];
                            currentPage = page;
                            updateStatus(`当前页面：第 ${page} 页`);
                        }
                    }
                });

                updateStatus('翻页书初始化成功！当前第 1 页');

            } catch (error) {
                console.error('初始化错误:', error);
                updateStatus('初始化失败: ' + error.message);
            }
        }

        // 翻页控制函数
        function nextPage() {
            try {
                if (flipbook && flipbook.turn) {
                    flipbook.turn('next');
                }
            } catch (error) {
                updateStatus('翻页失败: ' + error.message);
            }
        }

        function previousPage() {
            try {
                if (flipbook && flipbook.turn) {
                    flipbook.turn('previous');
                }
            } catch (error) {
                updateStatus('翻页失败: ' + error.message);
            }
        }

        function goToPage(page) {
            try {
                if (flipbook && flipbook.turn) {
                    flipbook.turn('page', page);
                }
            } catch (error) {
                updateStatus('跳转失败: ' + error.message);
            }
        }

        function toggleDisplay() {
            try {
                if (flipbook && flipbook.turn) {
                    isDouble = !isDouble;
                    var newDisplay = isDouble ? 'double' : 'single';
                    var newWidth = isDouble ? 800 : 400;
                    
                    flipbook.turn('size', newWidth, 600);
                    updateStatus(`切换到${isDouble ? '双页' : '单页'}模式`);
                }
            } catch (error) {
                updateStatus('切换失败: ' + error.message);
            }
        }

        // 键盘控制
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') {
                previousPage();
            } else if (e.key === 'ArrowRight') {
                nextPage();
            }
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(initFlipbook, 100);
        });
    </script>
</body>
</html>
