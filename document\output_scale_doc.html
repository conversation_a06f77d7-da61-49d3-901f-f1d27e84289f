<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>OutputScale 文档 - PDF.js</title>
  <link rel="stylesheet" href="doc_styles.css">
</head>
<body>
  <header>
    <h1>OutputScale</h1>
    <p class="module-description">输出缩放，处理屏幕与PDF缩放比例的转换</p>
    <a href="index.html" class="back-link">返回模块列表</a>
  </header>

  <div class="content">
    <section class="module-intro">
      <h2>模块介绍</h2>
      <p>OutputScale 是 PDF.js 中用于处理设备像素比(Device Pixel Ratio)的工具类，它负责管理高分辨率显示器（如视网膜屏幕）上的PDF渲染缩放。该类主要解决在不同设备像素密度下，确保PDF内容清晰渲染的问题，同时提供对大型页面的缩放限制以防止浏览器性能问题。</p>
      
      <div class="module-diagram">
        <h3>OutputScale 在渲染流程中的位置</h3>
        <div class="mermaid">
          graph TD
            PDFPageView[PDFPageView] --> OutputScale[OutputScale]
            OutputScale -->|设置缩放因子| Canvas[Canvas渲染]
            OutputScale -->|影响| TextLayer[文本层缩放]
            DevicePixelRatio[设备像素比] --> OutputScale
            MaxCanvasPixels[最大画布像素数] --> OutputScale
            OutputScale -->|调整| ActualScale[实际渲染缩放比例]
        </div>
      </div>
    </section>

    <section class="properties">
      <h2>属性</h2>
      
      <h3>实例属性</h3>
      <div class="property">
        <h4>sx</h4>
        <p>水平方向的缩放因子，初始化为设备像素比。</p>
      </div>
      
      <div class="property">
        <h4>sy</h4>
        <p>垂直方向的缩放因子，初始化为设备像素比。</p>
      </div>
      
      <h3>静态属性</h3>
      <div class="property">
        <h4>pixelRatio</h4>
        <p>静态获取器，返回当前环境的设备像素比（window.devicePixelRatio），如果不可用则返回1。</p>
      </div>
    </section>

    <section class="methods">
      <h2>方法</h2>

      <div class="method">
        <h3>constructor()</h3>
        <p>创建 OutputScale 实例，初始化水平和垂直缩放因子为当前设备像素比。</p>
        
        <pre><code>// 创建新的OutputScale实例
const outputScale = new OutputScale();</code></pre>
      </div>

      <div class="method">
        <h3>get scaled()</h3>
        <p>获取器，检查当前是否有任何缩放（sx或sy不等于1）。</p>
        <h4>返回：</h4>
        <p>布尔值，表示是否应用了缩放</p>
        
        <pre><code>// 检查是否应用了缩放
if (outputScale.scaled) {
  // 处理缩放后的渲染逻辑
}</code></pre>
      </div>

      <div class="method">
        <h3>get symmetric()</h3>
        <p>获取器，检查水平和垂直缩放是否相同。</p>
        <h4>返回：</h4>
        <p>布尔值，表示水平和垂直缩放是否相同</p>
        
        <pre><code>// 检查缩放是否对称
if (outputScale.symmetric) {
  // 处理对称缩放的情况
}</code></pre>
      </div>

      <div class="method">
        <h3>limitCanvas(width, height, maxPixels, maxDim, capAreaFactor = -1)</h3>
        <p>根据最大像素数和最大尺寸限制调整缩放因子，防止创建过大的画布导致性能问题。</p>
        <h4>参数：</h4>
        <ul>
          <li><code>width</code> - 画布宽度</li>
          <li><code>height</code> - 画布高度</li>
          <li><code>maxPixels</code> - 最大像素数限制</li>
          <li><code>maxDim</code> - 最大尺寸限制（宽度或高度）</li>
          <li><code>capAreaFactor</code> - 画布区域因子（可选，默认为-1）</li>
        </ul>
        <h4>返回：</h4>
        <p>布尔值，表示是否应用了缩放限制</p>
        
        <pre><code>// 限制画布大小
const needsRestrictedScaling = outputScale.limitCanvas(
  1000, // 宽度
  800,  // 高度
  16777216, // 最大像素数（例如：4096*4096）
  4096,     // 最大尺寸
  100       // 画布区域因子
);</code></pre>

        <div class="mermaid">
          flowchart TD
            A[开始] --> B[计算最大面积缩放]
            B --> C[计算最大宽度缩放]
            C --> D[计算最大高度缩放]
            D --> E[取三者中的最小值]
            E --> F{当前缩放是否超过最大缩放?}
            F -->|是| G[调整sx和sy为最大缩放值]
            F -->|否| H[保持当前缩放不变]
            G --> I[返回true]
            H --> J[返回false]
        </div>
      </div>

      <div class="method">
        <h3>static capPixels(maxPixels, capAreaFactor)</h3>
        <p>根据画布区域因子和屏幕尺寸计算实际的最大像素数。</p>
        <h4>参数：</h4>
        <ul>
          <li><code>maxPixels</code> - 配置的最大像素数</li>
          <li><code>capAreaFactor</code> - 画布区域因子</li>
        </ul>
        <h4>返回：</h4>
        <p>实际可用的最大像素数</p>
        
        <pre><code>// 根据画布区域因子计算实际最大像素数
const actualMaxPixels = OutputScale.capPixels(16777216, 100);</code></pre>
      </div>
    </section>

    <section class="usage">
      <h2>使用示例</h2>

      <div class="example">
        <h3>在 PDFPageView 中的基本用法</h3>
        <pre><code>// PDFPageView 内部使用 OutputScale 的示例
#computeScale() {
  const { width, height } = this.viewport;
  const outputScale = this.outputScale = new OutputScale();
  
  if (this.maxCanvasPixels === 0) {
    // 如果没有最大像素限制，根据需要调整缩放
    const invScale = 1 / this.scale;
    outputScale.sx *= invScale;
    outputScale.sy *= invScale;
    this.#needsRestrictedScaling = true;
  } else {
    // 根据最大像素限制调整画布大小
    this.#needsRestrictedScaling = outputScale.limitCanvas(
      width,
      height,
      this.maxCanvasPixels,
      this.maxCanvasDim,
      this.capCanvasAreaFactor
    );
  }
}</code></pre>
      </div>

      <div class="example">
        <h3>在 Canvas 渲染中应用缩放</h3>
        <pre><code>// 在画布渲染时应用输出缩放
function setupCanvas() {
  const viewport = page.getViewport({ scale });
  const outputScale = new OutputScale();
  
  // 根据输出缩放调整画布尺寸
  const canvas = document.createElement("canvas");
  const width = Math.floor(viewport.width);
  const height = Math.floor(viewport.height);
  
  // 应用设备像素比缩放
  canvas.width = Math.floor(width * outputScale.sx);
  canvas.height = Math.floor(height * outputScale.sy);
  
  // 调整画布显示尺寸
  canvas.style.width = `${Math.floor(width)}px`;
  canvas.style.height = `${Math.floor(height)}px`;
  
  // 获取绘图上下文并应用缩放
  const ctx = canvas.getContext("2d");
  
  // 如果缩放不是1，需要调整渲染上下文
  if (outputScale.scaled) {
    ctx.scale(outputScale.sx, outputScale.sy);
  }
  
  return { canvas, ctx };
}</code></pre>
      </div>

      <div class="example">
        <h3>在 TextLayer 中使用 OutputScale</h3>
        <pre><code>// 在文本层构造中使用 OutputScale
constructor({textContentSource, container, viewport}) {
  // ...其他初始化代码...
  
  // 将文本层缩放设置为视口缩放与设备像素比的乘积
  this.#scale = viewport.scale * OutputScale.pixelRatio;
  
  // ...其他初始化代码...
}

// 在更新文本层时应用缩放
update({viewport, onBefore = null}) {
  const scale = viewport.scale * OutputScale.pixelRatio;
  
  if (scale !== this.#scale) {
    onBefore?.();
    this.#scale = scale;
    
    // 使用新的缩放因子重新布局文本元素
    // ...布局代码...
  }
}</code></pre>
      </div>
    </section>

    <section class="implementation-notes">
      <h2>实现说明</h2>
      <div class="note">
        <h3>设备像素比处理</h3>
        <p>OutputScale 类主要解决高分辨率屏幕（如Retina显示器）上的渲染问题。在这些设备上，CSS像素与实际设备像素不是1:1的关系。例如，在设备像素比为2的屏幕上，1个CSS像素对应2×2个物理像素。OutputScale 通过 window.devicePixelRatio 获取这个比率，并在渲染时应用，确保PDF内容在高分辨率屏幕上清晰显示。</p>
      </div>
      
      <div class="note">
        <h3>画布大小限制</h3>
        <p>浏览器对Canvas元素的大小有限制（通常在16777216像素，即4096×4096左右）。当渲染大型PDF页面时，特别是在高设备像素比的屏幕上，很容易超过这个限制。OutputScale 的 limitCanvas 方法会根据最大像素数和最大尺寸限制调整缩放因子，确保不会创建过大的画布导致渲染失败。</p>
      </div>
      
      <div class="note">
        <h3>动态缩放策略</h3>
        <p>OutputScale 实现了动态缩放策略，通过 capPixels 方法考虑屏幕可用面积和设备像素比，计算出最佳的最大像素数。这样可以在不同设备上自适应调整缩放，在保证渲染质量的同时兼顾性能。</p>
      </div>
      
      <div class="note">
        <h3>非对称缩放支持</h3>
        <p>虽然大多数情况下水平和垂直缩放因子相同（symmetric 为 true），但 OutputScale 类支持非对称缩放，允许水平和垂直方向使用不同的缩放因子。这在某些特殊渲染场景下非常有用。</p>
      </div>
    </section>

    <section class="best-practices">
      <h2>最佳实践</h2>
      <div class="practice">
        <h3>考虑设备特性</h3>
        <p>在不同设备上使用 OutputScale 时，应考虑设备的特性：</p>
        <ul>
          <li>高分辨率显示器上，保持原始的设备像素比可以提供最清晰的渲染效果</li>
          <li>低端设备上，可能需要降低缩放因子以提高性能</li>
          <li>设备像素比不是整数的设备（如某些Windows设备），可能需要特殊处理</li>
        </ul>
      </div>
      
      <div class="practice">
        <h3>合理设置最大画布像素数</h3>
        <p>设置最大画布像素数时应考虑以下因素：</p>
        <ul>
          <li>太低的值会导致大型页面显示不清晰</li>
          <li>太高的值会消耗过多内存并可能导致浏览器崩溃</li>
          <li>对于移动设备，建议使用较低的限制（如8-12百万像素）</li>
          <li>对于现代桌面浏览器，16-25百万像素通常是安全的</li>
        </ul>
      </div>
      
      <div class="practice">
        <h3>处理缩放后的事件坐标</h3>
        <p>当应用 OutputScale 后，需要特别注意事件坐标的处理：</p>
        <pre><code>// 在画布上处理点击事件
canvas.addEventListener("click", (event) => {
  const rect = canvas.getBoundingClientRect();
  const x = event.clientX - rect.left;
  const y = event.clientY - rect.top;
  
  // 将事件坐标从CSS像素转换为画布像素
  const scaledX = x * outputScale.sx;
  const scaledY = y * outputScale.sy;
  
  // 使用转换后的坐标
  handleCanvasClick(scaledX, scaledY);
});</code></pre>
      </div>
      
      <div class="practice">
        <h3>使用画布区域因子优化内存使用</h3>
        <p>通过适当设置 capAreaFactor 参数，可以根据设备屏幕大小自动调整最大画布像素数：</p>
        <ul>
          <li>负值表示不使用屏幕面积限制</li>
          <li>0表示严格限制为屏幕面积</li>
          <li>正值表示允许超出屏幕面积的百分比（如100表示允许使用2倍于屏幕面积的画布）</li>
        </ul>
        <pre><code>// 允许画布面积超出屏幕面积50%
const options = {
  maxCanvasPixels: 16777216,  // 默认最大像素数
  capCanvasAreaFactor: 50     // 允许超出50%
};

// 在PDFPageView中使用
const pageView = new PDFPageView(options);</code></pre>
      </div>
    </section>
  </div>

  <script src="doc_script.js"></script>
  <script src="js/mermaid.js"></script>
  <script>
    mermaid.initialize({ startOnLoad: true, theme: 'neutral' });
  </script>
</body>
</html> 