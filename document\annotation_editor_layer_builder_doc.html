<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AnnotationEditorLayerBuilder 文档 - PDF.js</title>
  <link rel="stylesheet" href="doc_styles.css">
</head>
<body>
  <header>
    <h1>AnnotationEditorLayerBuilder</h1>
    <p class="module-description">注释编辑器层构建器，提供注释编辑功能</p>
    <a href="index.html" class="back-link">返回模块列表</a>
  </header>

  <div class="content">
    <section class="module-intro">
      <h2>模块介绍</h2>
      <p>AnnotationEditorLayerBuilder 是 PDF.js 中负责创建和管理注释编辑器层的组件。该组件允许用户在 PDF 文档中添加、编辑和删除注释，包括文本注释、墨迹注释、高亮注释等。它作为 PDF 查看器与底层注释编辑器层实现之间的桥梁，处理编辑器层的生命周期、渲染和交互逻辑。</p>
      
      <div class="module-diagram">
        <h3>组件位置</h3>
        <div class="mermaid">
          graph TD
            PDFViewer[PDFViewer] --> PDFPageView[PDFPageView]
            PDFPageView --> AnnotationEditorLayerBuilder[AnnotationEditorLayerBuilder]
            AnnotationEditorLayerBuilder --> AnnotationEditorLayer[AnnotationEditorLayer]
            AnnotationEditorLayer --> AnnotationEditor[AnnotationEditor]
            AnnotationEditor --> TextEditor[TextEditor]
            AnnotationEditor --> InkEditor[InkEditor]
            AnnotationEditor --> HighlightEditor[HighlightEditor]
            AnnotationEditorLayer -.-> AnnotationEditorUIManager[AnnotationEditorUIManager]
        </div>
      </div>
    </section>

    <section class="properties">
      <h2>属性</h2>
      
      <h3>公共属性</h3>
      <div class="property">
        <h4>div</h4>
        <p>包含注释编辑器层的 DOM 元素。初始为 null，在 render 方法调用后被设置。</p>
        <pre><code>// 获取注释编辑器层的 DOM 元素
const editorLayerElement = annotationEditorLayerBuilder.div;</code></pre>
      </div>

      <div class="property">
        <h4>annotationEditorLayer</h4>
        <p>底层的 AnnotationEditorLayer 实例。初始为 null，在初始化后被设置。</p>
        <pre><code>// 访问底层编辑器层
const editorLayer = annotationEditorLayerBuilder.annotationEditorLayer;</code></pre>
      </div>

      <div class="property">
        <h4>pdfPage</h4>
        <p>关联的 PDF 页面对象。</p>
      </div>

      <div class="property">
        <h4>accessibilityManager</h4>
        <p>可访问性管理器，提供无障碍功能支持。</p>
      </div>

      <div class="property">
        <h4>l10n</h4>
        <p>本地化对象，处理界面文本的翻译。</p>
      </div>

      <h3>私有属性</h3>
      <div class="property">
        <h4>#uiManager</h4>
        <p>注释编辑器 UI 管理器，协调所有页面的注释编辑器操作。</p>
      </div>

      <div class="property">
        <h4>#annotationLayer</h4>
        <p>关联的注释层，用于与现有注释交互。</p>
      </div>

      <div class="property">
        <h4>#textLayer</h4>
        <p>文本层，用于文本选择和高亮注释。</p>
      </div>

      <div class="property">
        <h4>#drawLayer</h4>
        <p>绘图层，用于渲染某些类型的注释。</p>
      </div>

      <div class="property">
        <h4>#onAppend</h4>
        <p>当注释编辑器层添加到 DOM 时调用的回调函数。</p>
      </div>

      <div class="property">
        <h4>#structTreeLayer</h4>
        <p>结构树层，提供文档的结构信息。</p>
      </div>

      <div class="property">
        <h4>_cancelled</h4>
        <p>标记渲染过程是否被取消。</p>
      </div>
    </section>

    <section class="methods">
      <h2>方法</h2>

      <div class="method">
        <h3>constructor(options)</h3>
        <p>创建 AnnotationEditorLayerBuilder 实例。</p>
        <h4>参数：</h4>
        <ul>
          <li><code>options.pdfPage</code> - 关联的 PDF 页面</li>
          <li><code>options.uiManager</code> - 注释编辑器 UI 管理器</li>
          <li><code>options.accessibilityManager</code> - 可访问性管理器（可选）</li>
          <li><code>options.l10n</code> - 本地化对象（可选）</li>
          <li><code>options.annotationLayer</code> - 注释层（可选）</li>
          <li><code>options.textLayer</code> - 文本层（可选）</li>
          <li><code>options.drawLayer</code> - 绘图层（可选）</li>
          <li><code>options.onAppend</code> - 添加回调（可选）</li>
          <li><code>options.structTreeLayer</code> - 结构树层（可选）</li>
        </ul>
        
        <pre><code>// 创建注释编辑器层构建器
const annotationEditorLayerBuilder = new AnnotationEditorLayerBuilder({
  pdfPage,
  uiManager,
  accessibilityManager,
  l10n,
  annotationLayer,
  textLayer
});</code></pre>
      </div>

      <div class="method">
        <h3>async render({ viewport, intent = "display" })</h3>
        <p>渲染注释编辑器层。</p>
        <h4>参数：</h4>
        <ul>
          <li><code>viewport</code> - 视口对象，定义页面的缩放和旋转</li>
          <li><code>intent</code> - 渲染意图，默认为 "display"</li>
        </ul>
        
        <pre><code>// 渲染注释编辑器层
await annotationEditorLayerBuilder.render({ viewport });
console.log('注释编辑器层渲染完成');</code></pre>

        <div class="mermaid">
          sequenceDiagram
            participant PDFPageView
            participant AnnotationEditorLayerBuilder
            participant AnnotationEditorLayer
            
            PDFPageView->>AnnotationEditorLayerBuilder: render({viewport})
            alt div 已存在
              AnnotationEditorLayerBuilder->>AnnotationEditorLayer: update({viewport})
              AnnotationEditorLayerBuilder->>AnnotationEditorLayerBuilder: show()
            else div 不存在
              AnnotationEditorLayerBuilder->>AnnotationEditorLayerBuilder: 创建 div
              AnnotationEditorLayerBuilder->>AnnotationEditorLayer: new AnnotationEditorLayer(...)
              AnnotationEditorLayerBuilder->>AnnotationEditorLayer: render(parameters)
              AnnotationEditorLayerBuilder->>AnnotationEditorLayerBuilder: show()
            end
        </div>
      </div>

      <div class="method">
        <h3>cancel()</h3>
        <p>取消正在进行的渲染过程并销毁注释编辑器层。</p>
        <pre><code>// 取消渲染并销毁编辑器层
annotationEditorLayerBuilder.cancel();</code></pre>
      </div>

      <div class="method">
        <h3>hide()</h3>
        <p>隐藏注释编辑器层并暂停其功能。</p>
        <pre><code>// 隐藏注释编辑器层
annotationEditorLayerBuilder.hide();</code></pre>
      </div>

      <div class="method">
        <h3>show()</h3>
        <p>显示注释编辑器层并恢复其功能。</p>
        <pre><code>// 显示注释编辑器层
annotationEditorLayerBuilder.show();</code></pre>
      </div>
    </section>

    <section class="usage">
      <h2>使用示例</h2>

      <div class="example">
        <h3>基本用法</h3>
        <pre><code>// 创建注释编辑器层构建器
const annotationEditorLayerBuilder = new AnnotationEditorLayerBuilder({
  pdfPage,
  uiManager: PDFViewerApplication.annotationEditorUIManager,
  accessibilityManager,
  l10n: PDFViewerApplication.l10n,
  annotationLayer,
  textLayer
});

// 渲染注释编辑器层
annotationEditorLayerBuilder.render({ viewport }).then(() => {
  console.log("注释编辑器层渲染完成");
});

// 页面缩放或旋转时重新渲染
function onPageViewportChange(newViewport) {
  annotationEditorLayerBuilder.render({ viewport: newViewport });
}

// 隐藏/显示注释编辑器层
function toggleAnnotationEditor(visible) {
  if (visible) {
    annotationEditorLayerBuilder.show();
  } else {
    annotationEditorLayerBuilder.hide();
  }
}

// 销毁注释编辑器层
function destroyAnnotationEditor() {
  annotationEditorLayerBuilder.cancel();
}</code></pre>
      </div>

      <div class="example">
        <h3>与 PDFPageView 结合使用</h3>
        <pre><code>// 在 PDFPageView 中使用 AnnotationEditorLayerBuilder
class PDFPageView {
  constructor(options) {
    // ...其他初始化代码...
    
    this.annotationEditorLayerBuilder = new AnnotationEditorLayerBuilder({
      pdfPage: this.pdfPage,
      uiManager: options.annotationEditorUIManager,
      accessibilityManager: options.accessibilityManager,
      l10n: options.l10n,
      annotationLayer: this.annotationLayer,
      textLayer: this.textLayer,
      onAppend: div => {
        this.div.append(div);
      }
    });
  }
  
  render() {
    // ...其他渲染代码...
    
    // 渲染注释编辑器层
    if (this.annotationEditorLayerBuilder) {
      this._renderAnnotationEditorLayer();
    }
  }
  
  async _renderAnnotationEditorLayer() {
    const { viewport } = this;
    try {
      await this.annotationEditorLayerBuilder.render({ viewport });
    } catch (error) {
      console.error(`无法渲染注释编辑器层: ${error}`);
    }
  }
}</code></pre>
      </div>
    </section>

    <section class="implementation-notes">
      <h2>实现说明</h2>
      <div class="note">
        <h3>注释编辑器层的生命周期</h3>
        <p>AnnotationEditorLayerBuilder 管理注释编辑器层的整个生命周期，包括创建、渲染、更新和销毁。在页面加载时，它创建注释编辑器层；当视口改变（如缩放或旋转）时，它更新注释编辑器层；当页面被移除时，它负责销毁相关资源。</p>
      </div>
      
      <div class="note">
        <h3>与 UI 管理器的协作</h3>
        <p>AnnotationEditorLayerBuilder 与 AnnotationEditorUIManager 紧密协作。UI 管理器协调所有页面的注释编辑器，处理全局操作如模式切换（文本、墨迹、高亮等）、编辑器选择、属性变更等。构建器则负责单个页面上的注释编辑器层的创建和生命周期管理。</p>
      </div>
      
      <div class="note">
        <h3>渲染过程</h3>
        <p>注释编辑器层的渲染过程分为两种情况：</p>
        <ol>
          <li>如果 div 不存在（首次渲染），构建器会创建 div 元素，初始化 AnnotationEditorLayer，然后调用其 render 方法</li>
          <li>如果 div 已存在（重新渲染），构建器会调用 AnnotationEditorLayer 的 update 方法，更新视口和位置</li>
        </ol>
      </div>
      
      <div class="note">
        <h3>注释编辑器类型</h3>
        <p>AnnotationEditorLayerBuilder 支持多种类型的注释编辑器：</p>
        <ul>
          <li><strong>TextEditor</strong>：用于添加文本注释</li>
          <li><strong>InkEditor</strong>：用于添加手写或绘图注释</li>
          <li><strong>HighlightEditor</strong>：用于高亮文本</li>
          <li><strong>StampEditor</strong>：用于添加印章注释</li>
        </ul>
        <p>编辑器类型的切换和管理由 AnnotationEditorUIManager 控制，而 AnnotationEditorLayerBuilder 负责在特定页面上创建和渲染这些编辑器。</p>
      </div>
    </section>

    <section class="best-practices">
      <h2>最佳实践</h2>
      <div class="practice">
        <h3>性能优化</h3>
        <p>注释编辑器层可能包含复杂的交互逻辑和大量 DOM 元素，尤其是在有大量注释的情况下，建议：</p>
        <ul>
          <li>仅在可见页面上渲染注释编辑器层</li>
          <li>在页面滚动时考虑暂时隐藏注释编辑器层以提高滚动性能</li>
          <li>对于大文档，仅在需要编辑时启用注释编辑器</li>
        </ul>
      </div>
      
      <div class="practice">
        <h3>错误处理</h3>
        <p>注释编辑器层渲染过程可能因各种原因失败，应该使用 try-catch 或 Promise 的 catch 方法处理潜在错误：</p>
        <pre><code>try {
  await annotationEditorLayerBuilder.render({ viewport });
  console.log('渲染成功');
} catch (error) {
  console.error('注释编辑器层渲染失败:', error);
  // 实施适当的后备机制
}</code></pre>
      </div>
      
      <div class="practice">
        <h3>协调多个层</h3>
        <p>在 PDF 页面上，注释编辑器层需要与多个其他层协同工作：</p>
        <ul>
          <li>确保注释编辑器层与注释层正确协调，以便编辑现有注释</li>
          <li>文本层对于高亮注释至关重要，确保它们正确关联</li>
          <li>绘图层用于某些类型的注释渲染，应确保它正确初始化</li>
        </ul>
        <p>创建 AnnotationEditorLayerBuilder 时提供这些层的引用，以确保它们能够协同工作。</p>
      </div>
      
      <div class="practice">
        <h3>可访问性</h3>
        <p>为了确保注释编辑功能对所有用户可访问：</p>
        <ul>
          <li>始终提供 AccessibilityManager 以支持屏幕阅读器用户</li>
          <li>确保注释编辑器提供键盘导航和操作支持</li>
          <li>使用合适的 ARIA 属性增强编辑器的无障碍性</li>
        </ul>
      </div>
    </section>
  </div>

  <script src="doc_script.js"></script>
  <script src="js/mermaid.js"></script>
  <script>
    mermaid.initialize({ startOnLoad: true, theme: 'neutral' });
  </script>
</body>
</html> 