<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PDFLinkService - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#constants">常量</a>
    <a href="#properties">属性列表</a>
    <a href="#methods">方法列表</a>
    <a href="#flowcharts">流程图</a>
  </div>

  <h1>PDFLinkService</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>PDFLinkService 是一个用于处理 PDF 文档中内部和外部链接的服务类。它负责管理 PDF 文档中的各种导航功能，包括页面跳转、命名目标导航、外部链接处理等。</p>
  </div>

  <!-- 常量列表 -->
  <div id="constants">
    <h2>常量</h2>
    
    <div class="method-block">
      <div class="method-header">
        <h3 id="DEFAULT_LINK_REL" class="method-name">DEFAULT_LINK_REL</h3>
      </div>
      <div class="method-content">
        <p>默认链接关系属性，设置安全属性以防止钓鱼攻击。</p>
        <pre><code class="language-javascript">const DEFAULT_LINK_REL = "noopener noreferrer nofollow";</code></pre>
      </div>
    </div>
    
    <div class="method-block">
      <div class="method-header">
        <h3 id="LinkTarget" class="method-name">LinkTarget</h3>
      </div>
      <div class="method-content">
        <p>链接目标枚举，定义了链接打开的目标位置。</p>
        <pre><code class="language-javascript">const LinkTarget = {
  NONE: 0,  // 无目标
  SELF: 1,  // 当前窗口/标签页
  BLANK: 2, // 新窗口/标签页
  PARENT: 3, // 父窗口/标签页
  TOP: 4    // 顶层窗口
};</code></pre>
      </div>
    </div>
  </div>

  <!-- 属性列表 -->
  <div id="properties">
    <h2>属性列表</h2>
    <ul>
      <li><code>externalLinkEnabled</code>: 是否启用外部链接，默认为 true</li>
      <li><code>eventBus</code>: 事件总线，用于分发事件</li>
      <li><code>externalLinkTarget</code>: 外部链接目标</li>
      <li><code>externalLinkRel</code>: 外部链接关系属性</li>
      <li><code>_ignoreDestinationZoom</code>: 是否忽略目标缩放</li>
      <li><code>baseUrl</code>: 基础 URL</li>
      <li><code>pdfDocument</code>: PDF 文档对象</li>
      <li><code>pdfViewer</code>: PDF 查看器对象</li>
      <li><code>pdfHistory</code>: PDF 历史记录对象</li>
    </ul>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法列表</h2>
    
    <!-- 构造方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="constructor" class="method-name">constructor({eventBus, externalLinkTarget = null, externalLinkRel = null, ignoreDestinationZoom = false} = {})</h3>
      </div>
      <div class="method-content">
        <p>创建新的 PDFLinkService 实例。</p>
        <pre><code class="language-javascript">/**
 * 创建新的PDFLinkService实例
 * @param {Object} options - 配置选项
 * @param {EventBus} options.eventBus - 事件总线
 * @param {number} options.externalLinkTarget - 外部链接目标
 * @param {string} options.externalLinkRel - 外部链接关系属性
 * @param {boolean} options.ignoreDestinationZoom - 是否忽略目标缩放
 */
constructor({
  eventBus, // 事件总线
  externalLinkTarget = null, // 外部链接目标
  externalLinkRel = null, // 外部链接关系
  ignoreDestinationZoom = false // 是否忽略目标缩放
} = {}) {
  this.eventBus = eventBus; // 设置事件总线
  this.externalLinkTarget = externalLinkTarget; // 设置外部链接目标
  this.externalLinkRel = externalLinkRel; // 设置外部链接关系
  this._ignoreDestinationZoom = ignoreDestinationZoom; // 设置是否忽略目标缩放
  this.baseUrl = null; // 基础URL
  this.pdfDocument = null; // PDF文档
  this.pdfViewer = null; // PDF查看器
  this.pdfHistory = null; // PDF历史记录
}</code></pre>
      </div>
    </div>
    
    <!-- setDocument方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="setDocument" class="method-name">setDocument(pdfDocument, baseUrl = null)</h3>
      </div>
      <div class="method-content">
        <p>设置当前 PDF 文档和基础 URL。</p>
        <pre><code class="language-javascript">/**
 * 设置当前PDF文档
 * @param {PDFDocumentProxy} pdfDocument - PDF文档
 * @param {string} baseUrl - 基础URL
 */
setDocument(pdfDocument, baseUrl = null) { // 设置PDF文档
  this.baseUrl = baseUrl; // 设置基础URL
  this.pdfDocument = pdfDocument; // 设置PDF文档
}</code></pre>
      </div>
    </div>
    
    <!-- setViewer方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="setViewer" class="method-name">setViewer(pdfViewer)</h3>
      </div>
      <div class="method-content">
        <p>设置 PDF 查看器实例。</p>
        <pre><code class="language-javascript">/**
 * 设置PDF查看器
 * @param {PDFViewer} pdfViewer - PDF查看器实例
 */
setViewer(pdfViewer) { // 设置PDF查看器
  this.pdfViewer = pdfViewer; // 设置PDF查看器
}</code></pre>
      </div>
    </div>
    
    <!-- setHistory方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="setHistory" class="method-name">setHistory(pdfHistory)</h3>
      </div>
      <div class="method-content">
        <p>设置 PDF 历史记录实例。</p>
        <pre><code class="language-javascript">/**
 * 设置PDF历史记录
 * @param {PDFHistory} pdfHistory - PDF历史记录实例
 */
setHistory(pdfHistory) { // 设置PDF历史记录
  this.pdfHistory = pdfHistory; // 设置PDF历史记录
}</code></pre>
      </div>
    </div>
    
    <!-- pagesCount方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="pagesCount" class="method-name">get pagesCount()</h3>
      </div>
      <div class="method-content">
        <p>获取 PDF 文档页面总数。</p>
        <pre><code class="language-javascript">/**
 * 获取PDF文档页面总数
 * @returns {number} 页面数量
 */
get pagesCount() { // 获取页面数量
  return this.pdfDocument ? this.pdfDocument.numPages : 0; // 如果有PDF文档返回页面数，否则返回0
}</code></pre>
      </div>
    </div>
    
    <!-- page getter方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="page-get" class="method-name">get page()</h3>
      </div>
      <div class="method-content">
        <p>获取当前页码。</p>
        <pre><code class="language-javascript">/**
 * 获取当前页码
 * @returns {number} 当前页码
 */
get page() { // 获取当前页码
  return this.pdfDocument ? this.pdfViewer.currentPageNumber : 1; // 如果有PDF文档返回当前页码，否则返回1
}</code></pre>
      </div>
    </div>
    
    <!-- page setter方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="page-set" class="method-name">set page(value)</h3>
      </div>
      <div class="method-content">
        <p>设置当前页码。</p>
        <pre><code class="language-javascript">/**
 * 设置当前页码
 * @param {number} value - 要设置的页码
 */
set page(value) { // 设置当前页码
  if (this.pdfDocument) { // 如果有PDF文档
    this.pdfViewer.currentPageNumber = value; // 设置当前页码
  }
}</code></pre>
      </div>
    </div>
    
    <!-- rotation getter方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="rotation-get" class="method-name">get rotation()</h3>
      </div>
      <div class="method-content">
        <p>获取页面旋转角度。</p>
        <pre><code class="language-javascript">/**
 * 获取页面旋转角度
 * @returns {number} 旋转角度，0、90、180或270度
 */
get rotation() { // 获取页面旋转角度
  return this.pdfDocument ? this.pdfViewer.pagesRotation : 0; // 如果有PDF文档返回旋转角度，否则返回0
}</code></pre>
      </div>
    </div>
    
    <!-- rotation setter方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="rotation-set" class="method-name">set rotation(value)</h3>
      </div>
      <div class="method-content">
        <p>设置页面旋转角度。</p>
        <pre><code class="language-javascript">/**
 * 设置页面旋转角度
 * @param {number} value - 旋转角度，应为0、90、180或270
 */
set rotation(value) { // 设置页面旋转角度
  if (this.pdfDocument) { // 如果有PDF文档
    this.pdfViewer.pagesRotation = value; // 设置旋转角度
  }
}</code></pre>
      </div>
    </div>
    
    <!-- isInPresentationMode方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="isInPresentationMode" class="method-name">get isInPresentationMode()</h3>
      </div>
      <div class="method-content">
        <p>检查是否处于演示模式。</p>
        <pre><code class="language-javascript">/**
 * 检查是否处于演示模式
 * @returns {boolean} 是否处于演示模式
 */
get isInPresentationMode() { // 是否处于演示模式
  return this.pdfDocument ? this.pdfViewer.isInPresentationMode : false; // 如果有PDF文档返回是否处于演示模式，否则返回false
}</code></pre>
      </div>
    </div>
    
    <!-- goToDestination方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="goToDestination" class="method-name">async goToDestination(dest)</h3>
      </div>
      <div class="method-content">
        <p>跳转到指定目标位置。</p>
        <pre><code class="language-javascript">/**
 * 跳转到指定目标位置
 * @param {string|Promise} dest - 目标位置，可以是命名目标或显式目标数组
 * @returns {Promise<void>} - 无返回值的Promise
 */
async goToDestination(dest) {
  if (!this.pdfDocument) { // 如果没有PDF文档
    return; // 直接返回
  }
  let namedDest, explicitDest, pageNumber; // 声明命名目标、显式目标和页码变量
  if (typeof dest === "string") { // 如果目标是字符串（命名目标）
    namedDest = dest; // 设置命名目标
    explicitDest = await this.pdfDocument.getDestination(dest); // 获取对应的显式目标
  } else { // 如果目标不是字符串
    namedDest = null; // 命名目标为null
    explicitDest = await dest; // 等待目标解析
  }
  if (!Array.isArray(explicitDest)) { // 如果显式目标不是数组
    console.error(`goToDestination: "${explicitDest}" is not a valid destination array, for dest="${dest}".`); // 输出错误信息
    return; // 直接返回
  }
  const [destRef] = explicitDest; // 从显式目标中获取引用（第一个元素）
  if (destRef && typeof destRef === "object") { // 如果引用是对象（页面引用）
    pageNumber = this.pdfDocument.cachedPageNumber(destRef); // 尝试从缓存获取页码
    if (!pageNumber) { // 如果缓存中没有找到页码
      try {
        pageNumber = (await this.pdfDocument.getPageIndex(destRef)) + 1; // 获取页索引并转换为页码
      } catch { // 如果出错
        console.error(`goToDestination: "${destRef}" is not a valid page reference, for dest="${dest}".`); // 输出错误信息
        return; // 直接返回
      }
    }
  } else if (Number.isInteger(destRef)) { // 如果引用是整数（页索引）
    pageNumber = destRef + 1; // 将页索引转换为页码
  }
  if (!pageNumber || pageNumber < 1 || pageNumber > this.pagesCount) { // 如果页码无效
    console.error(`goToDestination: "${pageNumber}" is not a valid page number, for dest="${dest}".`); // 输出错误信息
    return; // 直接返回
  }
  if (this.pdfHistory) { // 如果有历史记录
    this.pdfHistory.pushCurrentPosition(); // 保存当前位置
    this.pdfHistory.push({ // 添加新位置到历史记录
      namedDest, // 命名目标
      explicitDest, // 显式目标
      pageNumber // 页码
    });
  }
  this.pdfViewer.scrollPageIntoView({ // 滚动到指定页面
    pageNumber, // 页码
    destArray: explicitDest, // 目标数组
    ignoreDestinationZoom: this._ignoreDestinationZoom // 是否忽略目标缩放
  });
}</code></pre>
      </div>
    </div>
    
    <!-- goToPage方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="goToPage" class="method-name">goToPage(val)</h3>
      </div>
      <div class="method-content">
        <p>跳转到指定页码。</p>
        <pre><code class="language-javascript">/**
 * 跳转到指定页码
 * @param {string|number} val - 页码或页面标签
 */
goToPage(val) {
  if (!this.pdfDocument) { // 如果没有PDF文档
    return; // 直接返回
  }
  // 将页面标签转换为页码，或者将字符串页码转换为数字
  const pageNumber = typeof val === "string" && this.pdfViewer.pageLabelToPageNumber(val) || val | 0;
  if (!(Number.isInteger(pageNumber) && pageNumber > 0 && pageNumber <= this.pagesCount)) { // 检查页码是否有效
    console.error(`PDFLinkService.goToPage: "${val}" is not a valid page.`); // 输出错误信息
    return; // 直接返回
  }
  if (this.pdfHistory) { // 如果有历史记录
    this.pdfHistory.pushCurrentPosition(); // 保存当前位置
    this.pdfHistory.pushPage(pageNumber); // 添加页面到历史记录
  }
  this.pdfViewer.scrollPageIntoView({ // 滚动到指定页面
    pageNumber // 页码
  });
}</code></pre>
      </div>
    </div>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <h3>跳转到目标位置流程</h3>
    <div class="mermaid">
      graph TD
        A[调用goToDestination] --> B{检查PDF文档是否存在}
        B -->|不存在| C[返回]
        B -->|存在| D{检查目标类型}
        D -->|字符串| E[获取命名目标]
        D -->|其他| F[等待目标解析]
        E --> G[获取显式目标]
        F --> H{检查显式目标是否为数组}
        G --> H
        H -->|不是数组| I[记录错误并返回]
        H -->|是数组| J[获取目标引用]
        J --> K{检查引用类型}
        K -->|对象| L[尝试从缓存获取页码]
        K -->|整数| M[页码=引用+1]
        L -->|缓存中有| O{验证页码是否有效}
        L -->|缓存中没有| N[获取页面索引并转换为页码]
        N --> O
        M --> O
        O -->|无效| P[记录错误并返回]
        O -->|有效| Q{检查是否有历史记录}
        Q -->|有| R[保存当前位置到历史记录]
        Q -->|无| S[滚动到指定页面]
        R --> S
        S --> T[结束]
    </div>
    
    <h3>跳转到页码流程</h3>
    <div class="mermaid">
      graph TD
        A[调用goToPage] --> B{检查PDF文档是否存在}
        B -->|不存在| C[返回]
        B -->|存在| D[转换页码格式]
        D --> E{验证页码是否有效}
        E -->|无效| F[记录错误并返回]
        E -->|有效| G{检查是否有历史记录}
        G -->|有| H[保存当前位置到历史记录]
        G -->|无| I[滚动到指定页面]
        H --> I
        I --> J[结束]
    </div>
  </div>

  <!-- 返回顶部按钮 -->
  <button class="back-to-top">↑</button>

  <script>
    // 创建目录
    createTableOfContents();
  </script>
</body>
</html> 