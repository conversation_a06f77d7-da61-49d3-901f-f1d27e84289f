<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PDFPresentationMode - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#constants">常量</a>
    <a href="#properties">属性</a>
    <a href="#methods">方法</a>
    <a href="#flowcharts">流程图</a>
    <a href="#examples">示例</a>
    <a href="#notes">注意事项</a>
  </div>

  <h1>PDFPresentationMode 模块文档</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>PDFPresentationMode 是 PDF.js 库中提供全屏查看和演示功能的组件。它允许用户以全屏模式浏览 PDF 文档，提供更专注的阅读体验和演示环境。该组件处理全屏模式的切换、页面导航、自动滚动以及键盘快捷键，使 PDF 文档能够以幻灯片方式展示。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 常量 -->
  <div id="constants">
    <h2>常量</h2>
    
    <p>PDFPresentationMode 使用以下常量：</p>
    
    <ul>
      <li>
        <code>DELAY_BEFORE_RESETTING_SWITCH_IN_PROGRESS</code>: 重置切换进度前的延迟时间（毫秒）
      </li>
      <li>
        <code>DELAY_AFTER_FULLSCREEN_CHANGE</code>: 全屏变化后的延迟时间（毫秒）
      </li>
      <li>
        <code>ACTIVE_SELECTOR</code>: 活动元素选择器
      </li>
      <li>
        <code>MOUSE_SCROLL_COOLDOWN_TIME</code>: 鼠标滚动冷却时间（毫秒）
      </li>
      <li>
        <code>MOUSE_AUTO_HIDE_TIMEOUT</code>: 鼠标自动隐藏超时时间（毫秒）
      </li>
      <li>
        <code>PAGE_SWITCH_THRESHOLD</code>: 页面切换阈值（像素）
      </li>
    </ul>
  </div>

  <!-- 属性 -->
  <div id="properties">
    <h2>属性</h2>
    
    <h3>公共属性</h3>
    <ul>
      <li><code>isFullscreen</code>: 是否处于全屏模式</li>
      <li><code>active</code>: 演示模式是否处于活动状态</li>
    </ul>

    <h3>私有属性</h3>
    <ul>
      <li><code>#container</code>: 容器元素</li>
      <li><code>#viewer</code>: 查看器元素</li>
      <li><code>#pdfViewer</code>: PDF 查看器实例</li>
      <li><code>#pdfThumbnailViewer</code>: PDF 缩略图查看器实例</li>
      <li><code>#eventBus</code>: 事件总线</li>
      <li><code>#contextMenuItems</code>: 上下文菜单项</li>
      <li><code>#initialPageNumber</code>: 初始页码</li>
      <li><code>#isChangingPresentationMode</code>: 是否正在切换演示模式</li>
      <li><code>#presentationModeArgs</code>: 演示模式参数</li>
      <li><code>#boundEvents</code>: 绑定的事件处理函数</li>
      <li><code>#state</code>: 当前状态</li>
      <li><code>#args</code>: 参数</li>
      <li><code>#mouseTimeout</code>: 鼠标超时计时器</li>
      <li><code>#mouseScrollTimeStamp</code>: 鼠标滚动时间戳</li>
      <li><code>#mouseScrollDelta</code>: 鼠标滚动增量</li>
    </ul>
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法</h2>
    
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">构造函数</h3>
      </div>
      <div class="method-content">
        <p>创建一个新的 PDFPresentationMode 实例。</p>
        <pre><code class="language-javascript">
constructor({
  container,
  pdfViewer,
  eventBus,
  contextMenuItems = null,
})
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>container</code>: 容器元素</li>
          <li><code>pdfViewer</code>: PDF 查看器实例</li>
          <li><code>eventBus</code>: 事件总线</li>
          <li><code>contextMenuItems</code>: 上下文菜单项 (可选)</li>
        </ul>
      </div>
    </div>

    <h3>核心方法</h3>
    <ul>
      <li><code>request()</code>: 请求进入演示模式</li>
      <li><code>enter()</code>: 进入演示模式</li>
      <li><code>exit()</code>: 退出演示模式</li>
      <li><code>toggleFullScreen()</code>: 切换全屏模式</li>
      <li><code>isEnabled</code>: 检查演示模式是否启用</li>
      <li><code>previousPage()</code>: 切换到上一页</li>
      <li><code>nextPage()</code>: 切换到下一页</li>
      <li><code>mouseScroll(delta)</code>: 处理鼠标滚动</li>
    </ul>
    
    <h3>私有方法</h3>
    <ul>
      <li><code>#addFullscreenChangeListeners()</code>: 添加全屏变化监听器</li>
      <li><code>#removeFullscreenChangeListeners()</code>: 移除全屏变化监听器</li>
      <li><code>#bindEvents()</code>: 绑定事件处理函数</li>
      <li><code>#unbindEvents()</code>: 解绑事件处理函数</li>
      <li><code>#resetMouseScrollState()</code>: 重置鼠标滚动状态</li>
      <li><code>#updateViewerClasses(fullscreen)</code>: 更新查看器类名</li>
      <li><code>#mousemove(evt)</code>: 处理鼠标移动事件</li>
      <li><code>#showControls()</code>: 显示控制元素</li>
      <li><code>#hideControls()</code>: 隐藏控制元素</li>
      <li><code>#resetMouseAutoHideTimer()</code>: 重置鼠标自动隐藏计时器</li>
      <li><code>#keyDown(evt)</code>: 处理键盘按下事件</li>
      <li><code>#fullscreenChange()</code>: 处理全屏变化事件</li>
      <li><code>#contextMenu()</code>: 处理上下文菜单事件</li>
      <li><code>#scrollHandler()</code>: 处理滚动事件</li>
    </ul>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <h3>演示模式进入流程</h3>
    <p>1. 用户请求进入演示模式<br>
    2. 检查是否支持全屏<br>
    3. 保存当前状态<br>
    4. 添加全屏事件监听器<br>
    5. 进入全屏模式<br>
    6. 更新查看器样式<br>
    7. 绑定控制事件</p>
    
    <div class="mermaid">
      graph TD
          A[用户请求演示模式] --> B[调用 request 方法]
          B --> C[检查浏览器全屏支持]
          C --> D[保存当前状态]
          D --> E[添加全屏事件监听器]
          E --> F[切换为全屏]
          F --> G[更新查看器样式]
          G --> H[绑定控制事件]
          H --> I[显示控制元素]
    </div>
    
    <h3>演示模式导航流程</h3>
    <p>1. 用户触发导航事件（键盘、鼠标或触摸）<br>
    2. 事件处理函数捕获导航意图<br>
    3. 调用相应的导航方法<br>
    4. 切换到目标页面<br>
    5. 重置控制计时器</p>
    
    <div class="mermaid">
      graph TD
          A[用户触发导航事件] --> B{事件类型?}
          B -->|键盘方向键| C[调用 keyDown 方法]
          B -->|鼠标滚动| D[调用 mouseScroll 方法]
          B -->|手动点击| E[调用 previousPage/nextPage 方法]
          C --> F[确定导航方向]
          D --> F
          E --> F
          F --> G[切换到目标页面]
          G --> H[重置控制计时器]
    </div>
    
    <h3>演示模式退出流程</h3>
    <p>1. 用户请求退出演示模式<br>
    2. 移除全屏事件监听器<br>
    3. 退出全屏模式<br>
    4. 恢复原始状态<br>
    5. 解绑控制事件<br>
    6. 重置查看器样式</p>
    
    <div class="mermaid">
      graph TD
          A[用户请求退出演示模式] --> B[调用 exit 方法]
          B --> C[移除全屏事件监听器]
          C --> D[退出浏览器全屏]
          D --> E[恢复原始查看器状态]
          E --> F[解绑控制事件]
          F --> G[重置查看器样式]
    </div>
  </div>

  <!-- 示例 -->
  <div id="examples">
    <h2>使用示例</h2>
    
    <h3>基本用法</h3>
    <pre><code class="language-javascript">
// 创建 PDFPresentationMode 实例
const presentationMode = new PDFPresentationMode({
  container: document.getElementById('viewerContainer'),
  pdfViewer: pdfViewer,
  eventBus: eventBus
});

// 添加演示模式按钮点击事件
document.getElementById('presentationMode').addEventListener('click', function() {
  presentationMode.request();
});

// 监听演示模式变化事件
eventBus.on('presentationmodechanged', function(evt) {
  if (evt.active) {
    console.log('演示模式已启动');
  } else {
    console.log('演示模式已退出');
  }
});
    </code></pre>
    
    <h3>自定义键盘快捷键</h3>
    <pre><code class="language-javascript">
// 创建带有自定义上下文菜单的演示模式
const presentationMode = new PDFPresentationMode({
  container: document.getElementById('viewerContainer'),
  pdfViewer: pdfViewer,
  eventBus: eventBus,
  contextMenuItems: [
    {
      id: 'firstPage',
      label: '第一页',
      handler() {
        pdfViewer.currentPageNumber = 1;
      }
    },
    {
      id: 'lastPage',
      label: '最后一页',
      handler() {
        pdfViewer.currentPageNumber = pdfViewer.pagesCount;
      }
    }
  ]
});

// 添加自定义键盘快捷键处理
document.addEventListener('keydown', function(event) {
  // 仅在演示模式下处理
  if (!presentationMode.active) {
    return;
  }
  
  // 添加自定义快捷键
  switch (event.key) {
    case 'h': // 按 'h' 键显示帮助
      showPresentationHelp();
      break;
    case 'f': // 按 'f' 键切换全屏
      presentationMode.toggleFullScreen();
      break;
    case '1': // 按 '1' 键跳转到第一页
      pdfViewer.currentPageNumber = 1;
      break;
    case 'End': // 按 'End' 键跳转到最后一页
      pdfViewer.currentPageNumber = pdfViewer.pagesCount;
      break;
  }
});
    </code></pre>
    
    <h3>自动播放演示</h3>
    <pre><code class="language-javascript">
// 创建自动播放功能
let autoPlayInterval;
const autoPlayDelay = 5000; // 5秒

function startAutoPlay() {
  // 确保已进入演示模式
  if (!presentationMode.active) {
    presentationMode.request();
  }
  
  // 启动自动播放
  autoPlayInterval = setInterval(() => {
    // 如果已经是最后一页，停止自动播放
    if (pdfViewer.currentPageNumber >= pdfViewer.pagesCount) {
      stopAutoPlay();
      return;
    }
    
    // 前进到下一页
    presentationMode.nextPage();
  }, autoPlayDelay);
  
  console.log('自动播放已启动');
}

function stopAutoPlay() {
  clearInterval(autoPlayInterval);
  console.log('自动播放已停止');
}

// 添加自动播放控制按钮
document.getElementById('startAutoPlay').addEventListener('click', startAutoPlay);
document.getElementById('stopAutoPlay').addEventListener('click', stopAutoPlay);

// 在演示模式退出时停止自动播放
eventBus.on('presentationmodechanged', function(evt) {
  if (!evt.active) {
    stopAutoPlay();
  }
});
    </code></pre>
  </div>

  <!-- 注意事项 -->
  <div id="notes">
    <h2>注意事项</h2>
    
    <ul>
      <li>全屏功能依赖于浏览器的全屏 API 支持。不同浏览器和平台的全屏 API 有细微差异，PDFPresentationMode 尝试处理这些差异。</li>
      <li>移动设备上的全屏体验可能与桌面设备不同，尤其是在处理触摸事件和控制元素显示方面。</li>
      <li>某些浏览器在全屏模式下有安全限制，例如，需要用户交互才能进入全屏，或在特定操作后自动退出全屏。</li>
      <li>演示模式通常会暂时禁用一些功能，如文本选择、表单填写等，以提供更专注的演示体验。</li>
      <li>长时间的演示可能会影响设备的电池寿命和性能，特别是在渲染复杂的 PDF 文档时。</li>
      <li>对于包含复杂动画或过渡效果的演示文稿，PDF 格式可能无法完全呈现原始效果。</li>
      <li>自动播放功能应提供明确的控制选项，并在用户退出演示模式时自动停止。</li>
      <li>在实现自定义键盘快捷键时，应避免与浏览器的默认快捷键冲突，尤其是在全屏模式下。</li>
    </ul>
  </div>

  <script>
    // 在页面加载完成后初始化 Mermaid
    document.addEventListener('DOMContentLoaded', function() {
      mermaid.initialize({ startOnLoad: true });
      
      // 生成目录
      generateTOC();
    });
  </script>
</body>
</html> 