<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PDFThumbnailViewer - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#constants">常量</a>
    <a href="#properties">属性列表</a>
    <a href="#methods">方法列表</a>
    <a href="#flowcharts">流程图</a>
  </div>

  <h1>PDFThumbnailViewer</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>PDFThumbnailViewer 是一个用于管理和显示 PDF 文档缩略图的组件。它负责创建、渲染和管理 PDF 页面的缩略图，支持页面选择、滚动同步等功能。</p>
  </div>

  <!-- 常量列表 -->
  <div id="constants">
    <h2>常量</h2>
    
    <div class="method-block">
      <div class="method-header">
        <h3 id="THUMBNAIL_SCROLL_MARGIN" class="method-name">THUMBNAIL_SCROLL_MARGIN</h3>
      </div>
      <div class="method-content">
        <p>缩略图滚动边距，用于控制缩略图滚动到视图中的位置偏移。</p>
        <pre><code class="language-javascript">const THUMBNAIL_SCROLL_MARGIN = -19;</code></pre>
      </div>
    </div>
    
    <div class="method-block">
      <div class="method-header">
        <h3 id="THUMBNAIL_SELECTED_CLASS" class="method-name">THUMBNAIL_SELECTED_CLASS</h3>
      </div>
      <div class="method-content">
        <p>缩略图选中状态的 CSS 类名，用于标记当前选中的缩略图。</p>
        <pre><code class="language-javascript">const THUMBNAIL_SELECTED_CLASS = "selected";</code></pre>
      </div>
    </div>
  </div>

  <!-- 属性列表 -->
  <div id="properties">
    <h2>属性列表</h2>
    <ul>
      <li><code>container</code>: 缩略图容器元素</li>
      <li><code>eventBus</code>: 事件总线，用于事件处理</li>
      <li><code>linkService</code>: 链接服务，用于页面导航</li>
      <li><code>renderingQueue</code>: 渲染队列，管理渲染优先级</li>
      <li><code>maxCanvasPixels</code>: 最大画布像素数</li>
      <li><code>maxCanvasDim</code>: 最大画布尺寸</li>
      <li><code>pageColors</code>: 页面颜色配置</li>
      <li><code>enableHWA</code>: 是否启用硬件加速</li>
      <li><code>_thumbnails</code>: 缩略图数组，存储所有页面的缩略图</li>
      <li><code>_currentPageNumber</code>: 当前页码</li>
      <li><code>_pageLabels</code>: 页面标签数组</li>
      <li><code>_pagesRotation</code>: 页面旋转角度</li>
    </ul>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法列表</h2>
    
    <!-- 构造方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="constructor" class="method-name">constructor({container, eventBus, linkService, renderingQueue, maxCanvasPixels, maxCanvasDim, pageColors, abortSignal, enableHWA})</h3>
      </div>
      <div class="method-content">
        <p>创建新的 PDFThumbnailViewer 实例。</p>
        <pre><code class="language-javascript">constructor({
  container, // 容器元素
  eventBus, // 事件总线
  linkService, // 链接服务
  renderingQueue, // 渲染队列
  maxCanvasPixels, // 最大画布像素数
  maxCanvasDim, // 最大画布尺寸
  pageColors, // 页面颜色
  abortSignal, // 中止信号
  enableHWA // 是否启用硬件加速
}) {
  this.container = container; // 设置容器
  this.eventBus = eventBus; // 设置事件总线
  this.linkService = linkService; // 设置链接服务
  this.renderingQueue = renderingQueue; // 设置渲染队列
  this.maxCanvasPixels = maxCanvasPixels; // 设置最大画布像素数
  this.maxCanvasDim = maxCanvasDim; // 设置最大画布尺寸
  this.pageColors = pageColors || null; // 设置页面颜色，默认为null
  this.enableHWA = enableHWA || false; // 设置是否启用硬件加速，默认为false
  this.scroll = watchScroll(this.container, this.#scrollUpdated.bind(this), abortSignal); // 监听容器的滚动事件
  this.#resetView(); // 重置视图
}</code></pre>
      </div>
    </div>
    
    <!-- #scrollUpdated方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="scrollUpdated" class="method-name">#scrollUpdated()</h3>
      </div>
      <div class="method-content">
        <p>私有方法，处理滚动更新事件。</p>
        <pre><code class="language-javascript">#scrollUpdated() { // 滚动更新的私有方法
  this.renderingQueue.renderHighestPriority(); // 渲染最高优先级的缩略图
}</code></pre>
      </div>
    </div>
    
    <!-- getThumbnail方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="getThumbnail" class="method-name">getThumbnail(index)</h3>
      </div>
      <div class="method-content">
        <p>获取指定索引的缩略图。</p>
        <pre><code class="language-javascript">/**
 * 获取指定索引的缩略图
 * @param {number} index - 缩略图索引
 * @returns {PDFThumbnailView} 缩略图视图对象
 */
getThumbnail(index) { // 获取指定索引的缩略图方法
  return this._thumbnails[index]; // 返回对应索引的缩略图
}</code></pre>
      </div>
    </div>
    
    <!-- #getVisibleThumbs方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="getVisibleThumbs" class="method-name">#getVisibleThumbs()</h3>
      </div>
      <div class="method-content">
        <p>私有方法，获取当前可见的缩略图。</p>
        <pre><code class="language-javascript">#getVisibleThumbs() { // 获取可见缩略图的私有方法
  return getVisibleElements({ // 调用获取可见元素的函数
    scrollEl: this.container, // 滚动容器
    views: this._thumbnails // 缩略图数组
  });
}</code></pre>
      </div>
    </div>
    
    <!-- scrollThumbnailIntoView方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="scrollThumbnailIntoView" class="method-name">scrollThumbnailIntoView(pageNumber)</h3>
      </div>
      <div class="method-content">
        <p>将指定页码的缩略图滚动到可见区域。</p>
        <pre><code class="language-javascript">/**
 * 将缩略图滚动到可见区域
 * @param {number} pageNumber - 页码
 */
scrollThumbnailIntoView(pageNumber) { // 将缩略图滚动到可见区域的方法
  if (!this.pdfDocument) { // 如果没有PDF文档
    return; // 直接返回
  }
  const thumbnailView = this._thumbnails[pageNumber - 1]; // 获取指定页码的缩略图视图
  if (!thumbnailView) { // 如果缩略图不存在
    console.error('scrollThumbnailIntoView: Invalid "pageNumber" parameter.'); // 输出错误信息
    return; // 直接返回
  }
  if (pageNumber !== this._currentPageNumber) { // 如果请求的页码与当前页码不同
    const prevThumbnailView = this._thumbnails[this._currentPageNumber - 1]; // 获取当前页码的缩略图视图
    prevThumbnailView.div.classList.remove(THUMBNAIL_SELECTED_CLASS); // 移除当前缩略图的选中样式
    thumbnailView.div.classList.add(THUMBNAIL_SELECTED_CLASS); // 为新缩略图添加选中样式
  }
  const {
    first, // 第一个可见缩略图
    last, // 最后一个可见缩略图
    views // 所有可见缩略图
  } = this.#getVisibleThumbs(); // 获取可见缩略图
  if (views.length > 0) { // 如果有可见缩略图
    let shouldScroll = false; // 初始化是否需要滚动的标志
    if (pageNumber <= first.id || pageNumber >= last.id) { // 如果请求的页码在可见区域之外
      shouldScroll = true; // 需要滚动
    } else { // 如果请求的页码可能在可见区域内
      for (const {
        id, // 缩略图ID
        percent // 可见百分比
      } of views) {
        if (id !== pageNumber) { // 如果不是请求的页码
          continue; // 继续下一个
        }
        shouldScroll = percent < 100; // 如果可见百分比小于100%，则需要滚动
        break; // 找到了请求的页码，跳出循环
      }
    }
    if (shouldScroll) { // 如果需要滚动
      scrollIntoView(thumbnailView.div, { // 滚动缩略图到可见区域
        top: THUMBNAIL_SCROLL_MARGIN // 设置顶部边距
      });
    }
  }
  this._currentPageNumber = pageNumber; // 更新当前页码
}</code></pre>
      </div>
    </div>
    
    <!-- pagesRotation getter方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="pagesRotation-get" class="method-name">get pagesRotation()</h3>
      </div>
      <div class="method-content">
        <p>获取页面旋转角度。</p>
        <pre><code class="language-javascript">/**
 * 获取页面旋转角度
 * @returns {number} 旋转角度
 */
get pagesRotation() { // 获取页面旋转角度的getter方法
  return this._pagesRotation; // 返回当前页面的旋转角度
}</code></pre>
      </div>
    </div>
    
    <!-- page setter方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="page-set" class="method-name">set page(value)</h3>
      </div>
      <div class="method-content">
        <p>设置当前页码。</p>
        <pre><code class="language-javascript">/**
 * 设置当前页码
 * @param {number} value - 要设置的页码
 */
set page(value) { // 设置当前页码
  if (this.pdfDocument) { // 如果有PDF文档
    this.pdfViewer.currentPageNumber = value; // 设置当前页码
  }
}</code></pre>
      </div>
    </div>
    
    <!-- rotation getter方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="rotation-get" class="method-name">get rotation()</h3>
      </div>
      <div class="method-content">
        <p>获取页面旋转角度。</p>
        <pre><code class="language-javascript">/**
 * 获取页面旋转角度
 * @returns {number} 旋转角度，0、90、180或270度
 */
get rotation() { // 获取页面旋转角度
  return this.pdfDocument ? this.pdfViewer.pagesRotation : 0; // 如果有PDF文档返回旋转角度，否则返回0
}</code></pre>
      </div>
    </div>
    
    <!-- rotation setter方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="rotation-set" class="method-name">set rotation(value)</h3>
      </div>
      <div class="method-content">
        <p>设置页面旋转角度。</p>
        <pre><code class="language-javascript">/**
 * 设置页面旋转角度
 * @param {number} value - 旋转角度，应为0、90、180或270
 */
set rotation(value) { // 设置页面旋转角度
  if (this.pdfDocument) { // 如果有PDF文档
    this.pdfViewer.pagesRotation = value; // 设置旋转角度
  }
}</code></pre>
      </div>
    </div>
    
    <!-- isInPresentationMode方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="isInPresentationMode" class="method-name">get isInPresentationMode()</h3>
      </div>
      <div class="method-content">
        <p>检查是否处于演示模式。</p>
        <pre><code class="language-javascript">/**
 * 检查是否处于演示模式
 * @returns {boolean} 是否处于演示模式
 */
get isInPresentationMode() { // 是否处于演示模式
  return this.pdfDocument ? this.pdfViewer.isInPresentationMode : false; // 如果有PDF文档返回是否处于演示模式，否则返回false
}</code></pre>
      </div>
    </div>
    
    <!-- goToDestination方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="goToDestination" class="method-name">async goToDestination(dest)</h3>
      </div>
      <div class="method-content">
        <p>跳转到指定目标位置。</p>
        <pre><code class="language-javascript">/**
 * 跳转到指定目标位置
 * @param {string|Promise} dest - 目标位置，可以是命名目标或显式目标数组
 * @returns {Promise<void>} - 无返回值的Promise
 */
async goToDestination(dest) {
  if (!this.pdfDocument) { // 如果没有PDF文档
    return; // 直接返回
  }
  let namedDest, explicitDest, pageNumber; // 声明命名目标、显式目标和页码变量
  if (typeof dest === "string") { // 如果目标是字符串（命名目标）
    namedDest = dest; // 设置命名目标
    explicitDest = await this.pdfDocument.getDestination(dest); // 获取对应的显式目标
  } else { // 如果目标不是字符串
    namedDest = null; // 命名目标为null
    explicitDest = await dest; // 等待目标解析
  }
  if (!Array.isArray(explicitDest)) { // 如果显式目标不是数组
    console.error(`goToDestination: "${explicitDest}" is not a valid destination array, for dest="${dest}".`); // 输出错误信息
    return; // 直接返回
  }
  const [destRef] = explicitDest; // 从显式目标中获取引用（第一个元素）
  if (destRef && typeof destRef === "object") { // 如果引用是对象（页面引用）
    pageNumber = this.pdfDocument.cachedPageNumber(destRef); // 尝试从缓存获取页码
    if (!pageNumber) { // 如果缓存中没有找到页码
      try {
        pageNumber = (await this.pdfDocument.getPageIndex(destRef)) + 1; // 获取页索引并转换为页码
      } catch { // 如果出错
        console.error(`goToDestination: "${destRef}" is not a valid page reference, for dest="${dest}".`); // 输出错误信息
        return; // 直接返回
      }
    }
  } else if (Number.isInteger(destRef)) { // 如果引用是整数（页索引）
    pageNumber = destRef + 1; // 将页索引转换为页码
  }
  if (!pageNumber || pageNumber < 1 || pageNumber > this.pagesCount) { // 如果页码无效
    console.error(`goToDestination: "${pageNumber}" is not a valid page number, for dest="${dest}".`); // 输出错误信息
    return; // 直接返回
  }
  if (this.pdfHistory) { // 如果有历史记录
    this.pdfHistory.pushCurrentPosition(); // 保存当前位置
    this.pdfHistory.push({ // 添加新位置到历史记录
      namedDest, // 命名目标
      explicitDest, // 显式目标
      pageNumber // 页码
    });
  }
  this.pdfViewer.scrollPageIntoView({ // 滚动到指定页面
    pageNumber, // 页码
    destArray: explicitDest, // 目标数组
    ignoreDestinationZoom: this._ignoreDestinationZoom // 是否忽略目标缩放
  });
}</code></pre>
      </div>
    </div>
    
    <!-- goToPage方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="goToPage" class="method-name">goToPage(val)</h3>
      </div>
      <div class="method-content">
        <p>跳转到指定页码。</p>
        <pre><code class="language-javascript">/**
 * 跳转到指定页码
 * @param {string|number} val - 页码或页面标签
 */
goToPage(val) {
  if (!this.pdfDocument) { // 如果没有PDF文档
    return; // 直接返回
  }
  // 将页面标签转换为页码，或者将字符串页码转换为数字
  const pageNumber = typeof val === "string" && this.pdfViewer.pageLabelToPageNumber(val) || val | 0;
  if (!(Number.isInteger(pageNumber) && pageNumber > 0 && pageNumber <= this.pagesCount)) { // 检查页码是否有效
    console.error(`PDFLinkService.goToPage: "${val}" is not a valid page.`); // 输出错误信息
    return; // 直接返回
  }
  if (this.pdfHistory) { // 如果有历史记录
    this.pdfHistory.pushCurrentPosition(); // 保存当前位置
    this.pdfHistory.pushPage(pageNumber); // 添加页面到历史记录
  }
  this.pdfViewer.scrollPageIntoView({ // 滚动到指定页面
    pageNumber // 页码
  });
}</code></pre>
      </div>
    </div>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <h3>初始化缩略图流程</h3>
    <div class="mermaid">
      graph TD
        A[调用setDocument] --> B{检查是否已有PDF文档}
        B -->|有| C[取消渲染]
        C --> D[重置视图]
        B -->|无| E[设置新PDF文档]
        D --> E
        E --> F{检查PDF文档是否有效}
        F -->|无效| G[返回]
        F -->|有效| H[获取第一页]
        H --> I[获取可选内容配置]
        I --> J[获取文档页数]
        J --> K[获取第一页视口]
        K --> L[创建所有页面的缩略图]
        L --> M[为第一个缩略图设置页面]
        M --> N[为当前缩略图添加选中样式]
    </div>
    
    <h3>滚动缩略图到视图流程</h3>
    <div class="mermaid">
      graph TD
        A[调用scrollThumbnailIntoView] --> B{检查PDF文档是否存在}
        B -->|不存在| C[返回]
        B -->|存在| D{检查缩略图是否存在}
        D -->|不存在| E[输出错误并返回]
        D -->|存在| F{是否是当前页码}
        F -->|否| G[移除之前缩略图的选中样式]
        G --> H[为新缩略图添加选中样式]
        F -->|是| I[获取可见缩略图]
        H --> I
        I --> J{是否有可见缩略图}
        J -->|无| K[更新当前页码]
        J -->|有| L{目标页码是否在可见区域外}
        L -->|是| M[设置需要滚动]
        L -->|否| N[检查目标缩略图可见性]
        N --> O{是否完全可见}
        O -->|否| P[设置需要滚动]
        O -->|是| Q[不需要滚动]
        M --> R{是否需要滚动}
        P --> R
        Q --> R
        R -->|是| S[滚动缩略图到视图]
        R -->|否| K
        S --> K
        K --> T[结束]
    </div>
  </div>

  <!-- 返回顶部按钮 -->
  <button class="back-to-top">↑</button>

  <script>
    // 创建目录
    createTableOfContents();
  </script>
</body>
</html> 