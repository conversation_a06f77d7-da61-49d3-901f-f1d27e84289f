<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PDF.js 文档系统</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      color: #2c3e50;
      border-bottom: 2px solid #eee;
      padding-bottom: 10px;
    }
    .module-list {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
    }
    .module-card {
      background: #f9f9f9;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      width: 280px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      transition: transform 0.2s, box-shadow 0.2s;
    }
    .module-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    .module-card h3 {
      margin-top: 0;
      color: #3498db;
    }
    .module-card p {
      font-size: 14px;
      color: #666;
    }
    a {
      color: #3498db;
      text-decoration: none;
    }
    a:hover {
      text-decoration: underline;
    }
    .search-container {
      margin-bottom: 20px;
    }
    #searchInput {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 16px;
    }
  </style>
</head>
<body>
  <h1>PDF.js 文档系统</h1>
  <p>这个文档系统基于 PDF.js 源代码，包含了模块注释、方法代码以及流程图。</p>
  
  <div class="search-container">
    <input type="text" id="searchInput" placeholder="搜索模块...">
  </div>
  
  <div class="module-list" id="moduleList">
    <!-- 模块列表将通过JavaScript动态生成 -->
  </div>

  <script>
    // 模块数据数组，包含所有可用模块
    const modules = [
      {
        name: 'AltTextManager',
        description: '替代文本管理器，用于管理PDF中图像的替代文本（无障碍描述）',
        file: 'alt_text_manager_doc.html'
      },
      {
        name: 'PDFLinkService',
        description: 'PDF链接服务，管理文档内部链接和导航',
        file: 'pdf_link_service_doc.html'
      },
      {
        name: 'PDFViewer',
        description: 'PDF查看器核心组件，负责PDF渲染和展示',
        file: 'pdf_viewer_doc.html'
      },
      {
        name: 'PDFPageView',
        description: 'PDF页面视图，负责单个页面的渲染',
        file: 'pdf_page_view_doc.html'
      },
      {
        name: 'PDFThumbnailViewer',
        description: 'PDF缩略图查看器，管理文档缩略图',
        file: 'pdf_thumbnail_viewer_doc.html'
      },
      {
        name: 'PDFOutlineViewer',
        description: 'PDF大纲查看器，负责显示和管理文档大纲结构',
        file: 'pdf_outline_viewer_doc.html'
      },
      {
        name: 'PDFAttachmentViewer',
        description: 'PDF附件查看器，用于管理和显示PDF文档中的附件',
        file: 'pdf_attachment_viewer_doc.html'
      },
      {
        name: 'PDFLayerViewer',
        description: 'PDF图层查看器，用于管理和控制PDF中的可选内容图层',
        file: 'pdf_layer_viewer_doc.html'
      },
      {
        name: 'PDFAnnotationLayer',
        description: 'PDF注释图层，处理文档中的注释显示和交互',
        file: 'pdf_annotation_layer_doc.html'
      },
      {
        name: 'PDFTextLayer',
        description: 'PDF文本图层，负责文本内容的提取和显示',
        file: 'pdf_text_layer_doc.html'
      },
      {
        name: 'PDFRenderingQueue',
        description: 'PDF渲染队列，管理页面渲染的优先级和调度',
        file: 'pdf_rendering_queue_doc.html'
      },
      {
        name: 'PDFPresentationMode',
        description: 'PDF演示模式，提供全屏查看和演示功能',
        file: 'pdf_presentation_mode_doc.html'
      },
      {
        name: 'PDFSidebar',
        description: 'PDF侧边栏，管理缩略图、大纲和附件面板',
        file: 'pdf_sidebar_doc.html'
      },
      {
        name: 'PDFDocumentProperties',
        description: 'PDF文档属性，提供文档元数据的访问和显示',
        file: 'pdf_document_properties_doc.html'
      },
      {
        name: 'PDFFindController',
        description: 'PDF查找控制器，用于在文档中搜索和高亮文本',
        file: 'pdf_find_controller_doc.html'
      },
      {
        name: 'PDFHistory',
        description: 'PDF历史记录，管理浏览历史和状态',
        file: 'pdf_history_doc.html'
      },
      {
        name: 'PDFCursorTools',
        description: 'PDF光标工具，提供不同的交互模式（如选择、手形工具等）',
        file: 'pdf_cursor_tools_doc.html'
      },
      {
        name: 'PDFScriptingManager',
        description: 'PDF脚本管理器，处理文档中的JavaScript脚本',
        file: 'pdf_scripting_manager_doc.html'
      },
      {
        name: 'SignatureManager',
        description: '签名管理器，处理PDF文档中的数字签名',
        file: 'signature_manager_doc.html'
      },
      {
        name: 'AnnotationEditorParams',
        description: '注释编辑器参数，配置PDF注释编辑器的行为',
        file: 'annotation_editor_params_doc.html'
      },
      {
        name: 'PasswordPrompt',
        description: '密码提示，处理加密PDF文档的密码输入',
        file: 'password_prompt_doc.html'
      },
      {
        name: 'PDFFindBar',
        description: 'PDF查找栏，提供文档内容搜索的用户界面',
        file: 'pdf_find_bar_doc.html'
      },
      {
        name: 'Toolbar',
        description: 'PDF工具栏，提供常用功能按钮和控件',
        file: 'toolbar_doc.html'
      },
      {
        name: 'SecondaryToolbar',
        description: '次要工具栏，提供额外的PDF操作功能',
        file: 'secondary_toolbar_doc.html'
      },
      {
        name: 'EventBus',
        description: '事件总线，用于PDF.js组件间的通信',
        file: 'event_bus_doc.html'
      },
      {
        name: 'OverlayManager',
        description: '覆盖层管理器，处理模态对话框和覆盖UI',
        file: 'overlay_manager_doc.html'
      },
      {
        name: 'GrabToPan',
        description: '抓取平移工具，实现文档的手形工具拖动功能',
        file: 'grab_to_pan_doc.html'
      },
      {
        name: 'ProgressBar',
        description: '进度条，显示PDF加载和处理的进度',
        file: 'progress_bar_doc.html'
      },
      {
        name: 'EditorUndoBar',
        description: '编辑器撤销栏，提供撤销编辑操作的功能',
        file: 'editor_undo_bar_doc.html'
      },
      {
        name: 'L10n',
        description: '本地化工具，处理PDF.js界面的多语言支持',
        file: 'l10n_doc.html'
      },
      {
        name: 'AnnotationLayerBuilder',
        description: '注释层构建器，负责创建和管理PDF注释层',
        file: 'annotation_layer_builder_doc.html'
      },
      {
        name: 'AnnotationEditorLayerBuilder',
        description: '注释编辑器层构建器，提供注释编辑功能',
        file: 'annotation_editor_layer_builder_doc.html'
      },
      {
        name: 'TextAccessibilityManager',
        description: '文本可访问性管理器，提升PDF文本的可访问性支持',
        file: 'text_accessibility_manager_doc.html'
      },
      {
        name: 'TextHighlighter',
        description: '文本高亮器，负责PDF中文本搜索结果的高亮显示',
        file: 'text_highlighter_doc.html'
      },
      {
        name: 'PDFPageViewBuffer',
        description: '页面视图缓冲区，管理PDF页面视图的缓存和重用',
        file: 'pdf_page_view_buffer_doc.html'
      },
      {
        name: 'OutputScale',
        description: '输出缩放，处理屏幕与PDF缩放比例的转换',
        file: 'output_scale_doc.html'
      },
      {
        name: 'Localization',
        description: '本地化系统，负责处理PDF.js界面的多语言翻译和国际化',
        file: 'localization_doc.html'
      },
      {
        name: 'PDFPrintService',
        description: 'PDF打印服务，提供PDF文档的打印功能',
        file: 'pdf_print_service_doc.html'
      },
      {
        name: 'PDFPrintServiceFactory',
        description: 'PDF打印服务工厂，负责创建打印服务实例',
        file: 'pdf_print_service_factory_doc.html'
      },
      {
        name: 'SimpleLinkService',
        description: '简化版链接服务，用于非交互式环境下的PDF链接处理',
        file: 'simple_link_service_doc.html'
      },
      {
        name: 'PDFViewerApplication',
        description: 'PDF查看器应用程序，作为PDF.js查看器的核心控制器',
        file: 'pdf_viewer_application_doc.html'
      },
      {
        name: 'CaretBrowsingMode',
        description: '插入符浏览模式，提供键盘导航和文本选择功能',
        file: 'caret_browsing_mode_doc.html'
      },
      {
        name: 'EditDescriptionDialog',
        description: '编辑描述对话框，用于编辑签名或注释的描述文本',
        file: 'edit_description_dialog_doc.html'
      },
      {
        name: 'GenericCom',
        description: '通用通信模块，负责管理主线程与Web Worker之间的通信',
        file: 'genericcom_doc.html'
      },
      {
        name: 'PDF.js API',
        description: 'PDF.js核心API，用于PDF文档的加载、渲染和操作',
        file: 'pdf_js_api_doc.html'
      }
      // 随着文档添加，这里会扩展更多模块
    ];

    // 渲染模块列表
    function renderModules(moduleArray) {
      const moduleList = document.getElementById('moduleList');
      moduleList.innerHTML = '';
      
      moduleArray.forEach(module => {
        const moduleCard = document.createElement('div');
        moduleCard.className = 'module-card';
        
        moduleCard.innerHTML = `
          <h3><a href="${module.file}">${module.name}</a></h3>
          <p>${module.description}</p>
        `;
        
        moduleList.appendChild(moduleCard);
      });
    }

    // 初始渲染
    renderModules(modules);

    // 搜索功能
    document.getElementById('searchInput').addEventListener('input', function(e) {
      const searchTerm = e.target.value.toLowerCase();
      
      if (searchTerm === '') {
        renderModules(modules);
        return;
      }
      
      const filteredModules = modules.filter(module => 
        module.name.toLowerCase().includes(searchTerm) || 
        module.description.toLowerCase().includes(searchTerm)
      );
      
      renderModules(filteredModules);
    });
  </script>
</body>
</html> 