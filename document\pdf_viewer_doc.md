# PDFViewer 模块文档

## 模块简介

PDFViewer 是 PDF.js 库的核心组件，负责管理和渲染 PDF 文档的页面。它提供了完整的 PDF 查看器功能，包括页面导航、缩放、旋转、文本层处理、注释处理等功能。

## 常量

PDFViewer 使用以下常量：

- `DEFAULT_SCALE`: 默认缩放比例
- `UNKNOWN_SCALE`: 未知缩放比例标记
- `TextLayerMode`: 文本层模式枚举
  - `DISABLE`: 禁用文本层
  - `ENABLE`: 启用文本层
  - `ENABLE_PERMISSIONS`: 启用带权限的文本层
- `AnnotationMode`: 注释模式枚举
  - `DISABLE`: 禁用注释
  - `ENABLE`: 启用注释
  - `ENABLE_FORMS`: 启用表单注释
  - `ENABLE_STORAGE`: 启用注释存储
- `AnnotationEditorType`: 注释编辑器类型枚举
  - `NONE`: 无编辑器
  - `DISABLE`: 禁用编辑器
  - `FREETEXT`: 自由文本编辑器
  - `INK`: 墨迹编辑器
  - `STAMP`: 图章编辑器
- `ScrollMode`: 滚动模式枚举
  - `UNKNOWN`: 未知模式
  - `VERTICAL`: 垂直滚动
  - `HORIZONTAL`: 水平滚动
  - `PAGE`: 按页滚动
- `PresentationModeState`: 演示模式状态枚举
  - `UNKNOWN`: 未知状态
  - `NORMAL`: 普通状态
  - `CHANGING`: 状态变更中
  - `FULLSCREEN`: 全屏状态
- `PermissionFlag`: 权限标志枚举
  - `COPY`: 复制权限
  - `MODIFY_ANNOTATIONS`: 修改注释权限
  - `MODIFY_CONTENTS`: 修改内容权限
  - `FILL_INTERACTIVE_FORMS`: 填写交互式表单权限

## 属性

PDFViewer 类包含以下主要属性：

### 公共属性

- `container`: 包含查看器的容器元素
- `viewer`: 查看器元素
- `eventBus`: 事件总线，用于事件分发
- `linkService`: 链接服务，处理 PDF 文档中的链接
- `downloadManager`: 下载管理器
- `findController`: 查找控制器，用于在文档中查找文本
- `imageResourcesPath`: 图像资源路径
- `enablePrintAutoRotate`: 是否启用打印自动旋转
- `removePageBorders`: 是否移除页面边框
- `maxCanvasPixels`: 最大画布像素数
- `maxCanvasDim`: 最大画布尺寸
- `l10n`: 本地化对象
- `pageColors`: 页面颜色
- `renderingQueue`: PDF 渲染队列
- `pdfDocument`: 当前加载的 PDF 文档
- `_pages`: 存储页面视图的数组
- `_currentPageNumber`: 当前页码
- `_currentScale`: 当前缩放比例
- `_currentScaleValue`: 当前缩放值
- `_pagesRotation`: 页面旋转角度
- `_scrollMode`: 滚动模式
- `_firstPageCapability`: 第一页加载能力
- `_onePageRenderedCapability`: 一页渲染完成能力
- `_pagesCapability`: 所有页面加载能力
- `presentationModeState`: 演示模式状态

### 私有属性

- `#buffer`: 页面视图缓冲区
- `#altTextManager`: 替代文本管理器
- `#annotationEditorHighlightColors`: 注释编辑器高亮颜色
- `#annotationEditorMode`: 注释编辑器模式
- `#annotationEditorUIManager`: 注释编辑器UI管理器
- `#annotationMode`: 注释模式
- `#enableHWA`: 是否启用硬件加速
- `#enableHighlightFloatingButton`: 是否启用高亮浮动按钮
- `#enablePermissions`: 是否启用权限
- `#textLayerMode`: 文本图层模式
- `#supportsPinchToZoom`: 是否支持捏合缩放
- `#eventAbortController`: 事件中止控制器
- `#resizeObserver`: 调整大小观察器
- `#getAllTextInProgress`: 是否正在获取所有文本
- `#hiddenCopyElement`: 隐藏的复制元素
- `#interruptCopyCondition`: 中断复制条件

## 方法

### 构造函数

```javascript
constructor(options)
```

创建一个新的 PDFViewer 实例。

**参数:**
- `options`: 配置选项对象
  - `container`: 包含查看器的容器元素
  - `viewer`: 查看器元素（可选，默认为容器的第一个子元素）
  - `eventBus`: 事件总线
  - `linkService`: 链接服务（可选，默认创建 SimpleLinkService）
  - `downloadManager`: 下载管理器（可选）
  - `findController`: 查找控制器（可选）
  - `scriptingManager`: 脚本管理器（可选）
  - `textLayerMode`: 文本层模式（可选，默认为 TextLayerMode.ENABLE）
  - `annotationMode`: 注释模式（可选，默认为 AnnotationMode.ENABLE_FORMS）
  - `annotationEditorMode`: 注释编辑器模式（可选，默认为 AnnotationEditorType.NONE）
  - `imageResourcesPath`: 图像资源路径（可选）
  - `removePageBorders`: 是否移除页面边框（可选）
  - `enablePermissions`: 是否启用权限（可选）
  - `renderingQueue`: 渲染队列（可选）

### Getter/Setter 方法

- `pagesCount`: 获取页面总数
- `getPageView(index)`: 获取指定索引的页面视图
- `getCachedPageViews()`: 获取缓存的页面视图
- `pageViewsReady`: 检查页面视图是否准备就绪
- `renderForms`: 检查是否渲染表单
- `enableScripting`: 检查是否启用脚本
- `currentPageNumber`: 获取/设置当前页码
- `currentPageLabel`: 获取/设置当前页面标签
- `currentScale`: 获取/设置当前缩放比例
- `currentScaleValue`: 获取/设置当前缩放值
- `pagesRotation`: 获取/设置页面旋转角度
- `firstPagePromise`: 获取第一页Promise
- `onePageRendered`: 获取一页渲染完成Promise
- `pagesPromise`: 获取所有页面Promise

### 核心方法

- `setDocument(pdfDocument)`: 设置PDF文档
- `getAllText()`: 获取PDF中所有文本
- `refresh(noUpdate, updateArgs)`: 刷新视图
- `update()`: 更新视图
- `scrollPageIntoView(params)`: 将页面滚动到视图中
- `scrollIntoView(element, spot, scrollMatches)`: 将元素滚动到视图中
- `getVisiblePages()`: 获取可见页面
- `updateAltTextManager()`: 更新替代文本管理器
- `resetView()`: 重置视图
- `_cancelRendering()`: 取消渲染
- `forceRendering(currentlyVisiblePages)`: 强制渲染

### 私有方法

- `#initializePermissions(permissions)`: 初始化权限
- `#onePageRenderedOrForceFetch(signal)`: 等待至少一页渲染完成或强制获取
- `#copyCallback(textLayerMode, event)`: 复制回调
- `#setScale(value, options)`: 设置缩放比例
- `#resetCurrentPageView()`: 重置当前页面视图
- `#updateContainerHeightCss()`: 更新容器高度CSS
- `#resizeObserverCallback(entries)`: 调整大小观察器回调

## 工作流程

### 文档加载与渲染流程

1. 创建 PDFViewer 实例并配置选项
2. 调用 `setDocument()` 方法设置 PDF 文档
3. PDF 文档加载后创建页面视图
4. 渲染队列处理页面渲染
5. 触发 `pagesloaded` 事件

```mermaid
graph TD
    A[创建 PDFViewer 实例] --> B[调用 setDocument 方法]
    B --> C[加载 PDF 文档]
    C --> D[创建页面视图]
    D --> E[渲染队列处理页面渲染]
    E --> F[触发 pagesloaded 事件]
```

### 页面导航流程

1. 用户通过 UI 或 API 请求切换页面
2. 调用 `currentPageNumber` setter 或 `_setCurrentPageNumber` 方法
3. 更新当前页码
4. 触发 `pagechanging` 事件
5. 重置当前页面视图（如果需要）

```mermaid
graph TD
    A[用户请求切换页面] --> B[调用 currentPageNumber setter]
    B --> C[检查页码有效性]
    C -->|有效| D[更新当前页码]
    C -->|无效| E[返回错误]
    D --> F[触发 pagechanging 事件]
    F --> G[重置当前页面视图]
```

### 缩放与旋转流程

1. 用户通过 UI 或 API 请求缩放/旋转
2. 调用相应的 setter 方法
3. 更新缩放/旋转值
4. 刷新视图
5. 触发相应事件

```mermaid
graph TD
    A[用户请求缩放/旋转] --> B[调用 setter 方法]
    B --> C[更新缩放/旋转值]
    C --> D[刷新视图]
    D --> E[触发相应事件]
```

## 使用示例

### 基本初始化

```javascript
// 创建必要的组件
const eventBus = new EventBus();
const pdfLinkService = new PDFLinkService({
  eventBus,
});

// 创建 PDFViewer 实例
const pdfViewer = new PDFViewer({
  container: document.getElementById('viewerContainer'),
  viewer: document.getElementById('viewer'),
  eventBus,
  linkService: pdfLinkService,
  textLayerMode: TextLayerMode.ENABLE,
  annotationMode: AnnotationMode.ENABLE_FORMS,
});

// 设置 PDFViewer 为 LinkService 的 viewer
pdfLinkService.setViewer(pdfViewer);

// 加载 PDF 文档
const loadingTask = pdfjsLib.getDocument('document.pdf');
loadingTask.promise.then(pdfDocument => {
  pdfViewer.setDocument(pdfDocument);
  pdfLinkService.setDocument(pdfDocument);
});
```

### 页面导航

```javascript
// 跳转到特定页面
pdfViewer.currentPageNumber = 5;

// 获取当前页码
const currentPage = pdfViewer.currentPageNumber;

// 获取总页数
const pagesCount = pdfViewer.pagesCount;
```

### 缩放和旋转

```javascript
// 设置缩放比例
pdfViewer.currentScale = 1.5;

// 设置缩放值（可以是预定义值）
pdfViewer.currentScaleValue = 'page-fit';

// 设置页面旋转
pdfViewer.pagesRotation = 90; // 旋转90度
```

## 注意事项

1. PDFViewer 的容器元素必须是绝对定位的，否则会抛出错误。
2. 如果页面数量超过特定限制，PDFViewer 会自动强制使用页面滚动模式以提高性能。
3. PDFViewer 的各种功能（如文本层、注释等）受到文档权限的限制。
4. 对于大型文档，应考虑性能影响，可能需要调整缓冲区大小和渲染策略。 