<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PDFViewer - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#constants">常量</a>
    <a href="#properties">属性</a>
    <a href="#methods">方法</a>
    <a href="#flowcharts">流程图</a>
    <a href="#examples">示例</a>
    <a href="#notes">注意事项</a>
  </div>

  <h1>PDFViewer 模块文档</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>PDFViewer 是 PDF.js 库的核心组件，负责管理和渲染 PDF 文档的页面。它提供了完整的 PDF 查看器功能，包括页面导航、缩放、旋转、文本层处理、注释处理等功能。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 常量 -->
  <div id="constants">
    <h2>常量</h2>
    
    <p>PDFViewer 使用以下常量：</p>
    
    <ul>
      <li><code>DEFAULT_SCALE</code>: 默认缩放比例</li>
      <li><code>UNKNOWN_SCALE</code>: 未知缩放比例标记</li>
      <li>
        <code>TextLayerMode</code>: 文本层模式枚举
        <ul>
          <li><code>DISABLE</code>: 禁用文本层</li>
          <li><code>ENABLE</code>: 启用文本层</li>
          <li><code>ENABLE_PERMISSIONS</code>: 启用带权限的文本层</li>
        </ul>
      </li>
      <li>
        <code>AnnotationMode</code>: 注释模式枚举
        <ul>
          <li><code>DISABLE</code>: 禁用注释</li>
          <li><code>ENABLE</code>: 启用注释</li>
          <li><code>ENABLE_FORMS</code>: 启用表单注释</li>
          <li><code>ENABLE_STORAGE</code>: 启用注释存储</li>
        </ul>
      </li>
      <li>
        <code>AnnotationEditorType</code>: 注释编辑器类型枚举
        <ul>
          <li><code>NONE</code>: 无编辑器</li>
          <li><code>DISABLE</code>: 禁用编辑器</li>
          <li><code>FREETEXT</code>: 自由文本编辑器</li>
          <li><code>INK</code>: 墨迹编辑器</li>
          <li><code>STAMP</code>: 图章编辑器</li>
        </ul>
      </li>
      <li>
        <code>ScrollMode</code>: 滚动模式枚举
        <ul>
          <li><code>UNKNOWN</code>: 未知模式</li>
          <li><code>VERTICAL</code>: 垂直滚动</li>
          <li><code>HORIZONTAL</code>: 水平滚动</li>
          <li><code>PAGE</code>: 按页滚动</li>
        </ul>
      </li>
      <li>
        <code>PresentationModeState</code>: 演示模式状态枚举
        <ul>
          <li><code>UNKNOWN</code>: 未知状态</li>
          <li><code>NORMAL</code>: 普通状态</li>
          <li><code>CHANGING</code>: 状态变更中</li>
          <li><code>FULLSCREEN</code>: 全屏状态</li>
        </ul>
      </li>
      <li>
        <code>PermissionFlag</code>: 权限标志枚举
        <ul>
          <li><code>COPY</code>: 复制权限</li>
          <li><code>MODIFY_ANNOTATIONS</code>: 修改注释权限</li>
          <li><code>MODIFY_CONTENTS</code>: 修改内容权限</li>
          <li><code>FILL_INTERACTIVE_FORMS</code>: 填写交互式表单权限</li>
        </ul>
      </li>
    </ul>
  </div>

  <!-- 属性 -->
  <div id="properties">
    <h2>属性</h2>
    
    <h3>公共属性</h3>
    <ul>
      <li><code>container</code>: 包含查看器的容器元素</li>
      <li><code>viewer</code>: 查看器元素</li>
      <li><code>eventBus</code>: 事件总线，用于事件分发</li>
      <li><code>linkService</code>: 链接服务，处理 PDF 文档中的链接</li>
      <li><code>downloadManager</code>: 下载管理器</li>
      <li><code>findController</code>: 查找控制器，用于在文档中查找文本</li>
      <li><code>imageResourcesPath</code>: 图像资源路径</li>
      <li><code>enablePrintAutoRotate</code>: 是否启用打印自动旋转</li>
      <li><code>removePageBorders</code>: 是否移除页面边框</li>
      <li><code>maxCanvasPixels</code>: 最大画布像素数</li>
      <li><code>maxCanvasDim</code>: 最大画布尺寸</li>
      <li><code>l10n</code>: 本地化对象</li>
      <li><code>pageColors</code>: 页面颜色</li>
      <li><code>renderingQueue</code>: PDF 渲染队列</li>
      <li><code>pdfDocument</code>: 当前加载的 PDF 文档</li>
      <li><code>_pages</code>: 存储页面视图的数组</li>
      <li><code>_currentPageNumber</code>: 当前页码</li>
      <li><code>_currentScale</code>: 当前缩放比例</li>
      <li><code>_currentScaleValue</code>: 当前缩放值</li>
      <li><code>_pagesRotation</code>: 页面旋转角度</li>
      <li><code>_scrollMode</code>: 滚动模式</li>
      <li><code>_firstPageCapability</code>: 第一页加载能力</li>
      <li><code>_onePageRenderedCapability</code>: 一页渲染完成能力</li>
      <li><code>_pagesCapability</code>: 所有页面加载能力</li>
      <li><code>presentationModeState</code>: 演示模式状态</li>
    </ul>

    <h3>私有属性</h3>
    <ul>
      <li><code>#buffer</code>: 页面视图缓冲区</li>
      <li><code>#altTextManager</code>: 替代文本管理器</li>
      <li><code>#annotationEditorHighlightColors</code>: 注释编辑器高亮颜色</li>
      <li><code>#annotationEditorMode</code>: 注释编辑器模式</li>
      <li><code>#annotationEditorUIManager</code>: 注释编辑器UI管理器</li>
      <li><code>#annotationMode</code>: 注释模式</li>
      <li><code>#enableHWA</code>: 是否启用硬件加速</li>
      <li><code>#enableHighlightFloatingButton</code>: 是否启用高亮浮动按钮</li>
      <li><code>#enablePermissions</code>: 是否启用权限</li>
      <li><code>#textLayerMode</code>: 文本图层模式</li>
      <li><code>#supportsPinchToZoom</code>: 是否支持捏合缩放</li>
      <li><code>#eventAbortController</code>: 事件中止控制器</li>
      <li><code>#resizeObserver</code>: 调整大小观察器</li>
      <li><code>#getAllTextInProgress</code>: 是否正在获取所有文本</li>
      <li><code>#hiddenCopyElement</code>: 隐藏的复制元素</li>
      <li><code>#interruptCopyCondition</code>: 中断复制条件</li>
    </ul>
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法</h2>
    
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">构造函数</h3>
      </div>
      <div class="method-content">
        <p>创建一个新的 PDFViewer 实例。</p>
        <pre><code class="language-javascript">
constructor(options)
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>options</code>: 配置选项对象
            <ul>
              <li><code>container</code>: 包含查看器的容器元素</li>
              <li><code>viewer</code>: 查看器元素（可选，默认为容器的第一个子元素）</li>
              <li><code>eventBus</code>: 事件总线</li>
              <li><code>linkService</code>: 链接服务（可选，默认创建 SimpleLinkService）</li>
              <li><code>downloadManager</code>: 下载管理器（可选）</li>
              <li><code>findController</code>: 查找控制器（可选）</li>
              <li><code>scriptingManager</code>: 脚本管理器（可选）</li>
              <li><code>textLayerMode</code>: 文本层模式（可选，默认为 TextLayerMode.ENABLE）</li>
              <li><code>annotationMode</code>: 注释模式（可选，默认为 AnnotationMode.ENABLE_FORMS）</li>
              <li><code>annotationEditorMode</code>: 注释编辑器模式（可选，默认为 AnnotationEditorType.NONE）</li>
              <li><code>imageResourcesPath</code>: 图像资源路径（可选）</li>
              <li><code>removePageBorders</code>: 是否移除页面边框（可选）</li>
              <li><code>enablePermissions</code>: 是否启用权限（可选）</li>
              <li><code>renderingQueue</code>: 渲染队列（可选）</li>
            </ul>
          </li>
        </ul>
      </div>
    </div>

    <h3>Getter/Setter 方法</h3>
    <ul>
      <li><code>pagesCount</code>: 获取页面总数</li>
      <li><code>getPageView(index)</code>: 获取指定索引的页面视图</li>
      <li><code>getCachedPageViews()</code>: 获取缓存的页面视图</li>
      <li><code>pageViewsReady</code>: 检查页面视图是否准备就绪</li>
      <li><code>renderForms</code>: 检查是否渲染表单</li>
      <li><code>enableScripting</code>: 检查是否启用脚本</li>
      <li><code>currentPageNumber</code>: 获取/设置当前页码</li>
      <li><code>currentPageLabel</code>: 获取/设置当前页面标签</li>
      <li><code>currentScale</code>: 获取/设置当前缩放比例</li>
      <li><code>currentScaleValue</code>: 获取/设置当前缩放值</li>
      <li><code>pagesRotation</code>: 获取/设置页面旋转角度</li>
      <li><code>firstPagePromise</code>: 获取第一页Promise</li>
      <li><code>onePageRendered</code>: 获取一页渲染完成Promise</li>
      <li><code>pagesPromise</code>: 获取所有页面Promise</li>
    </ul>
    
    <h3>核心方法</h3>
    <ul>
      <li><code>setDocument(pdfDocument)</code>: 设置PDF文档</li>
      <li><code>getAllText()</code>: 获取PDF中所有文本</li>
      <li><code>refresh(noUpdate, updateArgs)</code>: 刷新视图</li>
      <li><code>update()</code>: 更新视图</li>
      <li><code>scrollPageIntoView(params)</code>: 将页面滚动到视图中</li>
      <li><code>scrollIntoView(element, spot, scrollMatches)</code>: 将元素滚动到视图中</li>
      <li><code>getVisiblePages()</code>: 获取可见页面</li>
      <li><code>updateAltTextManager()</code>: 更新替代文本管理器</li>
      <li><code>resetView()</code>: 重置视图</li>
      <li><code>_cancelRendering()</code>: 取消渲染</li>
      <li><code>forceRendering(currentlyVisiblePages)</code>: 强制渲染</li>
    </ul>
    
    <h3>私有方法</h3>
    <ul>
      <li><code>#initializePermissions(permissions)</code>: 初始化权限</li>
      <li><code>#onePageRenderedOrForceFetch(signal)</code>: 等待至少一页渲染完成或强制获取</li>
      <li><code>#copyCallback(textLayerMode, event)</code>: 复制回调</li>
      <li><code>#setScale(value, options)</code>: 设置缩放比例</li>
      <li><code>#resetCurrentPageView()</code>: 重置当前页面视图</li>
      <li><code>#updateContainerHeightCss()</code>: 更新容器高度CSS</li>
      <li><code>#resizeObserverCallback(entries)</code>: 调整大小观察器回调</li>
    </ul>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <h3>文档加载与渲染流程</h3>
    <p>1. 创建 PDFViewer 实例并配置选项<br>
    2. 调用 <code>setDocument()</code> 方法设置 PDF 文档<br>
    3. PDF 文档加载后创建页面视图<br>
    4. 渲染队列处理页面渲染<br>
    5. 触发 <code>pagesloaded</code> 事件</p>
    
    <div class="mermaid">
      graph TD
          A[创建 PDFViewer 实例] --> B[调用 setDocument 方法]
          B --> C[加载 PDF 文档]
          C --> D[创建页面视图]
          D --> E[渲染队列处理页面渲染]
          E --> F[触发 pagesloaded 事件]
    </div>
    
    <h3>页面导航流程</h3>
    <p>1. 用户通过 UI 或 API 请求切换页面<br>
    2. 调用 <code>currentPageNumber</code> setter 或 <code>_setCurrentPageNumber</code> 方法<br>
    3. 更新当前页码<br>
    4. 触发 <code>pagechanging</code> 事件<br>
    5. 重置当前页面视图（如果需要）</p>
    
    <div class="mermaid">
      graph TD
          A[用户请求切换页面] --> B[调用 currentPageNumber setter]
          B --> C[检查页码有效性]
          C -->|有效| D[更新当前页码]
          C -->|无效| E[返回错误]
          D --> F[触发 pagechanging 事件]
          F --> G[重置当前页面视图]
    </div>
    
    <h3>缩放与旋转流程</h3>
    <p>1. 用户通过 UI 或 API 请求缩放/旋转<br>
    2. 调用相应的 setter 方法<br>
    3. 更新缩放/旋转值<br>
    4. 刷新视图<br>
    5. 触发相应事件</p>
    
    <div class="mermaid">
      graph TD
          A[用户请求缩放/旋转] --> B[调用 setter 方法]
          B --> C[更新缩放/旋转值]
          C --> D[刷新视图]
          D --> E[触发相应事件]
    </div>
  </div>

  <!-- 使用示例 -->
  <div id="examples">
    <h2>使用示例</h2>
    
    <h3>基本初始化</h3>
    <pre><code class="language-javascript">
// 创建必要的组件
const eventBus = new EventBus();
const pdfLinkService = new PDFLinkService({
  eventBus,
});

// 创建 PDFViewer 实例
const pdfViewer = new PDFViewer({
  container: document.getElementById('viewerContainer'),
  viewer: document.getElementById('viewer'),
  eventBus,
  linkService: pdfLinkService,
  textLayerMode: TextLayerMode.ENABLE,
  annotationMode: AnnotationMode.ENABLE_FORMS,
});

// 设置 PDFViewer 为 LinkService 的 viewer
pdfLinkService.setViewer(pdfViewer);

// 加载 PDF 文档
const loadingTask = pdfjsLib.getDocument('document.pdf');
loadingTask.promise.then(pdfDocument => {
  pdfViewer.setDocument(pdfDocument);
  pdfLinkService.setDocument(pdfDocument);
});
    </code></pre>
    
    <h3>页面导航</h3>
    <pre><code class="language-javascript">
// 跳转到特定页面
pdfViewer.currentPageNumber = 5;

// 获取当前页码
const currentPage = pdfViewer.currentPageNumber;

// 获取总页数
const pagesCount = pdfViewer.pagesCount;
    </code></pre>
    
    <h3>缩放和旋转</h3>
    <pre><code class="language-javascript">
// 设置缩放比例
pdfViewer.currentScale = 1.5;

// 设置缩放值（可以是预定义值）
pdfViewer.currentScaleValue = 'page-fit';

// 设置页面旋转
pdfViewer.pagesRotation = 90; // 旋转90度
    </code></pre>
  </div>

  <!-- 注意事项 -->
  <div id="notes">
    <h2>注意事项</h2>
    
    <ol>
      <li>PDFViewer 的容器元素必须是绝对定位的，否则会抛出错误。</li>
      <li>如果页面数量超过特定限制，PDFViewer 会自动强制使用页面滚动模式以提高性能。</li>
      <li>PDFViewer 的各种功能（如文本层、注释等）受到文档权限的限制。</li>
      <li>对于大型文档，应考虑性能影响，可能需要调整缓冲区大小和渲染策略。</li>
    </ol>
  </div>

  <!-- 返回顶部按钮 -->
  <button class="back-to-top">↑</button>

  <script>
    // 创建目录
    createTableOfContents();
  </script>
</body>
</html> 