<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Localization 文档 - PDF.js</title>
  <link rel="stylesheet" href="doc_styles.css">
</head>
<body>
  <header>
    <h1>Localization</h1>
    <p class="module-description">本地化系统，负责处理PDF.js界面的多语言翻译和国际化</p>
    <a href="index.html" class="back-link">返回模块列表</a>
  </header>

  <div class="content">
    <section class="module-intro">
      <h2>模块介绍</h2>
      <p>Localization（本地化）是 PDF.js 中的多语言支持核心系统，它基于 Fluent 本地化框架实现，负责管理和加载不同语言的翻译资源，并将其应用到用户界面元素上。该系统支持动态切换语言、适配从右到左(RTL)的语言排版、处理参数化文本模板等功能，确保 PDF.js 可以在全球范围内为不同语言的用户提供本地化界面。</p>
      
      <div class="module-diagram">
        <h3>Localization 在系统中的位置</h3>
        <div class="mermaid">
          graph TD
            PDFViewerApplication --> L10n
            L10n --> GenericL10n
            GenericL10n --> DOMLocalization
            DOMLocalization --> Localization["Localization（核心）"]
            Localization -->|加载| TranslationResources["翻译资源(.ftl)"]
            Localization -->|应用到| DOM["DOM元素"]
        </div>
      </div>
    </section>

    <section class="architecture">
      <h2>架构设计</h2>
      <p>PDF.js 的本地化系统采用分层架构，从底层到上层包括：</p>
      <ol>
        <li><strong>Localization</strong>: 核心类，负责管理翻译资源和格式化文本</li>
        <li><strong>DOMLocalization</strong>: 扩展Localization，提供DOM元素的自动翻译能力</li>
        <li><strong>L10n</strong>: PDF.js的包装类，提供简洁API，处理文本方向等功能</li>
        <li><strong>GenericL10n</strong>: L10n的具体实现，用于PDF查看器</li>
      </ol>
      
      <div class="mermaid">
        flowchart TD
          A[翻译资源文件.ftl] -->|加载| B[Localization核心类]
          B -->|翻译格式化| C[DOMLocalization]
          C -->|DOM操作| D[L10n API包装]
          D -->|应用到UI| E[PDF.js界面元素]
          F[用户语言设置] -->|影响| B
      </div>
    </section>

    <section class="properties">
      <h2>属性</h2>
      
      <h3>Localization 类属性</h3>
      <div class="property">
        <h4>resourceIds</h4>
        <p>包含本地化资源ID的数组，用于标识需要加载的翻译资源文件。</p>
      </div>
      
      <div class="property">
        <h4>generateBundles</h4>
        <p>用于从资源ID生成翻译包的生成器函数。该函数负责实际加载翻译资源并创建翻译包。</p>
      </div>
      
      <div class="property">
        <h4>bundles</h4>
        <p>缓存的翻译包迭代器，使用CachedAsyncIterable包装以提高性能。</p>
      </div>
      
      <h3>DOMLocalization 类属性</h3>
      <div class="property">
        <h4>roots</h4>
        <p>翻译根元素集合，用于管理需要翻译的DOM子树。</p>
      </div>
      
      <div class="property">
        <h4>pendingElements</h4>
        <p>等待翻译的DOM元素集合。</p>
      </div>
      
      <div class="property">
        <h4>mutationObserver</h4>
        <p>DOM变化观察器，用于监听页面中本地化属性的变化。</p>
      </div>
    </section>

    <section class="methods">
      <h2>方法</h2>

      <h3>Localization 类方法</h3>

      <div class="method">
        <h4>constructor(resourceIds = [], generateBundles)</h4>
        <p>创建Localization实例。</p>
        <h5>参数：</h5>
        <ul>
          <li><code>resourceIds</code> - 本地化资源ID列表</li>
          <li><code>generateBundles</code> - 用于从资源ID生成翻译包的函数</li>
        </ul>
        
        <pre><code>// 创建基本的Localization实例
const localization = new Localization(
  ["viewer.ftl"], // 资源ID
  async function* generateBundles(resourceIds) {
    // 加载翻译资源的逻辑
    for (const resourceId of resourceIds) {
      const response = await fetch(`/locale/${locale}/${resourceId}`);
      const source = await response.text();
      const bundle = new FluentBundle(locale);
      bundle.addResource(new FluentResource(source));
      yield bundle;
    }
  }
);</code></pre>
      </div>

      <div class="method">
        <h4>addResourceIds(resourceIds, eager = false)</h4>
        <p>添加新的资源ID到资源列表。</p>
        <h5>参数：</h5>
        <ul>
          <li><code>resourceIds</code> - 要添加的资源ID数组</li>
          <li><code>eager</code> - 是否立即预加载翻译包</li>
        </ul>
        <h5>返回：</h5>
        <p>添加后的资源ID总数</p>
        
        <pre><code>// 添加新的资源ID
const totalIds = localization.addResourceIds(
  ["editor.ftl", "printing.ftl"],
  true // 预加载这些资源
);</code></pre>
      </div>

      <div class="method">
        <h4>formatMessages(keys)</h4>
        <p>格式化消息对象（包含值和属性）。</p>
        <h5>参数：</h5>
        <ul>
          <li><code>keys</code> - 要格式化的消息键数组</li>
        </ul>
        <h5>返回：</h5>
        <p>Promise，解析为格式化后的消息对象数组</p>
        
        <pre><code>// 格式化多个消息
const messages = await localization.formatMessages([
  { id: "welcome-title", args: null },
  { id: "page-count", args: { count: 5 } }
]);</code></pre>
      </div>

      <div class="method">
        <h4>onChange(eager = false)</h4>
        <p>更新翻译包。当资源ID变更时调用。</p>
        <h5>参数：</h5>
        <ul>
          <li><code>eager</code> - 是否预加载翻译包</li>
        </ul>
        
        <pre><code>// 手动触发翻译包更新
localization.onChange(true);</code></pre>
      </div>

      <h3>DOMLocalization 类方法</h3>

      <div class="method">
        <h4>translateRoots()</h4>
        <p>翻译所有已注册的根元素。</p>
        
        <pre><code>// 触发所有根元素的翻译
domLocalization.translateRoots();</code></pre>
      </div>

      <div class="method">
        <h4>translateElements(elements)</h4>
        <p>翻译指定的DOM元素集合。</p>
        <h5>参数：</h5>
        <ul>
          <li><code>elements</code> - 需要翻译的DOM元素集合</li>
        </ul>
        <h5>返回：</h5>
        <p>Promise，翻译完成后解析</p>
        
        <pre><code>// 翻译特定元素
const elements = document.querySelectorAll('[data-l10n-id]');
await domLocalization.translateElements(elements);</code></pre>
      </div>

      <div class="method">
        <h4>connectRoot(root)</h4>
        <p>将DOM子树连接到本地化系统。</p>
        <h5>参数：</h5>
        <ul>
          <li><code>root</code> - 要连接的DOM根元素</li>
        </ul>
        
        <pre><code>// 连接DOM子树到本地化系统
domLocalization.connectRoot(document.querySelector('#pdf-viewer'));</code></pre>
      </div>

      <div class="method">
        <h4>disconnectRoot(root)</h4>
        <p>将DOM子树从本地化系统断开。</p>
        <h5>参数：</h5>
        <ul>
          <li><code>root</code> - 要断开的DOM根元素</li>
        </ul>
        
        <pre><code>// 断开DOM子树与本地化系统的连接
domLocalization.disconnectRoot(document.querySelector('#pdf-viewer'));</code></pre>
      </div>

      <div class="method">
        <h4>setAttributes(element, id, args)</h4>
        <p>设置元素的本地化属性。</p>
        <h5>参数：</h5>
        <ul>
          <li><code>element</code> - 要设置属性的DOM元素</li>
          <li><code>id</code> - 消息ID</li>
          <li><code>args</code> - 消息参数</li>
        </ul>
        <h5>返回：</h5>
        <p>设置了属性的元素</p>
        
        <pre><code>// 设置元素的本地化属性
const button = document.querySelector('#next-button');
domLocalization.setAttributes(
  button,
  'next-page-button',
  { page: currentPage + 1 }
);</code></pre>
      </div>

      <div class="method">
        <h4>getAttributes(element)</h4>
        <p>获取元素的本地化属性。</p>
        <h5>参数：</h5>
        <ul>
          <li><code>element</code> - DOM元素</li>
        </ul>
        <h5>返回：</h5>
        <p>包含id和args的对象</p>
        
        <pre><code>// 获取元素的本地化属性
const { id, args } = domLocalization.getAttributes(
  document.querySelector('#page-count')
);</code></pre>
      </div>
    </section>

    <section class="usage">
      <h2>使用示例</h2>

      <div class="example">
        <h3>基本用法</h3>
        <pre><code>// 创建DOMLocalization实例
const domL10n = new DOMLocalization(
  ["viewer.ftl"],
  async function* generateBundles(resourceIds) {
    // 在此示例中，我们只处理一个语言（简体中文）
    const locale = "zh-CN";
    
    for (const resourceId of resourceIds) {
      // 加载翻译资源
      const response = await fetch(`/locale/${locale}/${resourceId}`);
      const source = await response.text();
      
      // 创建Fluent包并添加资源
      const bundle = new FluentBundle(locale);
      bundle.addResource(new FluentResource(source));
      
      // 生成翻译包
      yield bundle;
    }
  }
);

// 连接DOM根元素到本地化系统
domL10n.connectRoot(document.body);

// 翻译根元素下的所有可本地化元素
domL10n.translateRoots();</code></pre>
      </div>

      <div class="example">
        <h3>在PDFViewerApplication中使用</h3>
        <pre><code>class PDFViewerApplication {
  constructor() {
    this.l10n = null;
  }
  
  async initializeL10n() {
    // 获取用户首选语言
    const language = this.getPreferredLanguage();
    
    // 创建通用本地化实例
    const l10n = new GenericL10n(language);
    
    // 设置文档方向（LTR或RTL）
    document.documentElement.dir = l10n.getDirection();
    
    // 添加资源ID
    l10n.addResourceIds(["viewer.ftl", "toolbar.ftl"]);
    
    // 翻译整个文档
    await l10n.translate(document.body);
    
    // 保存本地化实例
    this.l10n = l10n;
    
    return l10n;
  }
  
  getPreferredLanguage() {
    // 检查URL参数
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has("lang")) {
      return urlParams.get("lang");
    }
    
    // 检查本地存储
    const storedLanguage = localStorage.getItem("pdfjs.language");
    if (storedLanguage) {
      return storedLanguage;
    }
    
    // 使用浏览器首选语言
    return navigator.language || "en-US";
  }
}</code></pre>
      </div>

      <div class="example">
        <h3>处理动态内容</h3>
        <pre><code>// HTML:
// &lt;div id="page-info" data-l10n-id="page-info" 
//      data-l10n-args='{"current":1,"total":10}'&gt;&lt;/div&gt;

// 更新页码信息
function updatePageInfo(currentPage, totalPages) {
  const pageInfoElement = document.getElementById("page-info");
  
  // 设置新的参数
  domL10n.setAttributes(
    pageInfoElement,
    "page-info",
    { current: currentPage, total: totalPages }
  );
  
  // DOMLocalization会自动检测属性变化并更新翻译
}</code></pre>
      </div>
    </section>

    <section class="implementation-notes">
      <h2>实现说明</h2>
      <div class="note">
        <h3>基于Fluent框架</h3>
        <p>PDF.js的本地化系统基于Mozilla的<a href="https://projectfluent.org/" target="_blank">Fluent</a>框架，这是一个专为UI本地化设计的框架，支持复杂的语法规则、复数形式和格式化。Localization类是对Fluent API的封装，提供了更适合PDF.js使用的接口。</p>
      </div>
      
      <div class="note">
        <h3>资源文件格式</h3>
        <p>翻译资源使用Fluent的.ftl格式存储，这种格式专为本地化设计，支持复杂的消息格式和语言特性。例如：</p>
        <pre><code># 基本消息
welcome = 欢迎使用PDF.js

# 带参数的消息
page-info = 第{ $current }页，共{ $total }页

# 条件消息（处理复数形式）
documents =
    { $count ->
        [0] 没有文档
        [one] 1个文档
       *[other] { $count }个文档
    }</code></pre>
      </div>
      
      <div class="note">
        <h3>懒加载机制</h3>
        <p>本地化系统使用懒加载机制加载翻译资源，只在需要时加载相应的语言包。这通过async generator函数实现，可以减少初始加载时间和内存占用。同时使用CachedAsyncIterable包装器来缓存已加载的资源，避免重复加载。</p>
      </div>
      
      <div class="note">
        <h3>MutationObserver监听DOM变化</h3>
        <p>DOMLocalization使用MutationObserver监听DOM变化，自动检测和翻译新添加的元素或属性变化的元素，确保动态添加的UI元素也能得到正确翻译。</p>
      </div>
    </section>

    <section class="best-practices">
      <h2>最佳实践</h2>
      <div class="practice">
        <h3>使用数据属性标记可本地化元素</h3>
        <p>使用<code>data-l10n-id</code>和<code>data-l10n-args</code>属性标记需要翻译的HTML元素：</p>
        <pre><code>&lt;button data-l10n-id="save-button"&gt;保存&lt;/button&gt;

&lt;div data-l10n-id="welcome-message" 
     data-l10n-args='{"username":"张三"}'&gt;
  欢迎，张三！
&lt;/div&gt;</code></pre>
      </div>
      
      <div class="practice">
        <h3>避免字符串拼接</h3>
        <p>不要在代码中拼接翻译后的字符串，这会导致翻译问题。应该使用参数化的消息：</p>
        <pre><code>// 错误方式
const message = await l10n.get("prefix") + fileName + await l10n.get("suffix");

// 正确方式
const message = await l10n.get("full-message", { fileName });</code></pre>
      </div>
      
      <div class="practice">
        <h3>考虑文本方向</h3>
        <p>始终使用L10n.getDirection()来设置文档方向，以支持从右到左(RTL)的语言：</p>
        <pre><code>document.documentElement.dir = l10n.getDirection();</code></pre>
        <p>对于动态创建的UI元素，也应考虑方向性：</p>
        <pre><code>const popupElement = document.createElement("div");
popupElement.dir = l10n.getDirection();</code></pre>
      </div>
      
      <div class="practice">
        <h3>使用资源ID命名空间</h3>
        <p>在大型应用中，使用前缀为本地化键添加命名空间，避免冲突：</p>
        <pre><code>// 翻译资源文件中的命名空间示例
pdfjs-toolbar-zoom-out = 缩小
pdfjs-toolbar-zoom-in = 放大
pdfjs-document-properties-file-name = 文件名称</code></pre>
      </div>
      
      <div class="practice">
        <h3>提供备用文本</h3>
        <p>在获取翻译时提供备用文本，确保即使翻译缺失也能显示内容：</p>
        <pre><code>// 提供备用文本
const message = await l10n.get(
  "missing-key", 
  { param: value }, 
  "这是备用文本"
);</code></pre>
      </div>
    </section>
  </div>

  <script src="doc_script.js"></script>
  <script src="js/mermaid.js"></script>
  <script>
    mermaid.initialize({ startOnLoad: true, theme: 'neutral' });
  </script>
</body>
</html> 