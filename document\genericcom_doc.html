<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>GenericCom 文档 - PDF.js</title>
  <link rel="stylesheet" href="doc_styles.css">
</head>
<body>
  <header>
    <h1>GenericCom</h1>
    <p class="module-description">通用通信模块，负责管理主线程与Web Worker之间的通信</p>
    <a href="index.html" class="back-link">返回模块列表</a>
  </header>

  <div class="content">
    <section class="module-intro">
      <h2>模块介绍</h2>
      <p>GenericCom 是 PDF.js 中处理主线程与 Web Worker 线程通信的核心模块，在普通浏览器环境中提供了简化的通信接口。它负责初始化通信环境、建立消息传递通道，并为应用程序提供标准化的API，使得主线程可以轻松地与处理PDF解析和渲染的工作线程进行通信，从而保证了用户界面的响应性能，同时提供高效的PDF处理能力。</p>
      
      <div class="module-diagram">
        <h3>GenericCom 在系统中的位置</h3>
        <div class="mermaid">
          graph TD
            PDFViewerApplication -- 初始化 --> GenericCom
            GenericCom -- 创建 --> MessageHandler
            MessageHandler -- 通信 --> Worker["PDF.js Worker"]
            Worker -- 返回数据 --> MessageHandler
            MessageHandler -- 传递结果 --> PDFViewerApplication
        </div>
      </div>
    </section>

    <section class="architecture">
      <h2>架构设计</h2>
      <p>GenericCom 基于浏览器的 Web Worker API 和消息传递机制设计，主要包含以下组件：</p>
      <ul>
        <li><strong>initCom</strong>：通信初始化函数，在浏览器环境中是一个简单的空实现</li>
        <li><strong>MessageHandler</strong>：消息处理器，负责主线程与工作线程之间的通信</li>
        <li><strong>WorkerTransport</strong>：工作线程传输层，处理PDF数据的传输和命令执行</li>
      </ul>
      
      <div class="mermaid">
        flowchart TD
          A[主线程] -->|初始化| B[GenericCom]
          B -->|创建| C[MessageHandler]
          C -->|发送请求| D[Web Worker]
          D -->|处理PDF文档| E[WorkerMessageHandler]
          E -->|返回结果| C
          C -->|更新UI| A
      </div>

      <p>在这个架构中：</p>
      <ul>
        <li>主线程通过 MessageHandler 发送命令和数据到工作线程</li>
        <li>工作线程执行PDF处理任务并返回结果</li>
        <li>MessageHandler 处理异步通信和回调</li>
        <li>WorkerTransport 提供更高级别的API来处理PDF对象和操作</li>
      </ul>
    </section>

    <section class="properties">
      <h2>属性</h2>
      
      <h3>MessageHandler 类属性</h3>
      <div class="property">
        <h4>sourceName</h4>
        <p>消息源名称，标识发送消息的实体（通常为"main"或"worker"）。</p>
      </div>
      
      <div class="property">
        <h4>targetName</h4>
        <p>目标名称，标识消息的接收实体（通常为"worker"或"main"）。</p>
      </div>
      
      <div class="property">
        <h4>comObj</h4>
        <p>通信对象，通常是 Worker 实例或 Worker 上下文中的 self。</p>
      </div>
      
      <div class="property">
        <h4>callbackId</h4>
        <p>回调ID计数器，用于唯一标识每个请求的回调。</p>
      </div>
      
      <div class="property">
        <h4>streamId</h4>
        <p>流ID计数器，用于唯一标识每个数据流。</p>
      </div>
      
      <div class="property">
        <h4>callbackCapabilities</h4>
        <p>存储回调能力对象的映射表，用于处理异步操作的完成。</p>
      </div>
      
      <div class="property">
        <h4>actionHandler</h4>
        <p>存储动作处理函数的映射表，处理接收到的不同类型的消息。</p>
      </div>
    </section>

    <section class="methods">
      <h2>方法</h2>

      <h3>通用方法</h3>
      <div class="method">
        <h4>initCom(app)</h4>
        <p>初始化通信模块。在浏览器环境中，这是一个空实现，因为不需要特殊的初始化。</p>
        <h5>参数：</h5>
        <ul>
          <li><code>app</code> - 应用程序实例</li>
        </ul>
        
        <pre><code>// 在应用程序启动时初始化通信模块
initCom(PDFViewerApplication);</code></pre>
      </div>

      <h3>MessageHandler 类方法</h3>
      <div class="method">
        <h4>constructor(sourceName, targetName, comObj)</h4>
        <p>创建一个新的消息处理器实例。</p>
        <h5>参数：</h5>
        <ul>
          <li><code>sourceName</code> - 消息源名称</li>
          <li><code>targetName</code> - 消息目标名称</li>
          <li><code>comObj</code> - 通信对象</li>
        </ul>
        
        <pre><code>// 创建主线程到工作线程的消息处理器
const messageHandler = new MessageHandler("main", "worker", worker);</code></pre>
      </div>

      <div class="method">
        <h4>on(actionName, callback)</h4>
        <p>注册一个动作处理函数，用于响应特定类型的消息。</p>
        <h5>参数：</h5>
        <ul>
          <li><code>actionName</code> - 动作名称</li>
          <li><code>callback</code> - 处理函数</li>
        </ul>
        
        <pre><code>// 注册处理PDF页面渲染完成的回调
messageHandler.on("RenderPageDone", function(data) {
  console.log(`页面 ${data.pageIndex + 1} 渲染完成`);
});</code></pre>
      </div>

      <div class="method">
        <h4>send(actionName, data, transfers)</h4>
        <p>发送一个不需要响应的消息。</p>
        <h5>参数：</h5>
        <ul>
          <li><code>actionName</code> - 动作名称</li>
          <li><code>data</code> - 消息数据</li>
          <li><code>transfers</code> - 可转移对象（可选）</li>
        </ul>
        
        <pre><code>// 发送配置信息到工作线程
messageHandler.send("configure", {
  verbosity: 1
});</code></pre>
      </div>

      <div class="method">
        <h4>sendWithPromise(actionName, data, transfers)</h4>
        <p>发送一个需要响应的消息，返回Promise。</p>
        <h5>参数：</h5>
        <ul>
          <li><code>actionName</code> - 动作名称</li>
          <li><code>data</code> - 消息数据</li>
          <li><code>transfers</code> - 可转移对象（可选）</li>
        </ul>
        <h5>返回：</h5>
        <p>Promise，解析为响应数据</p>
        
        <pre><code>// 获取PDF文档页数
messageHandler.sendWithPromise("GetPageCount", null)
  .then(function(pageCount) {
    console.log(`文档共有 ${pageCount} 页`);
  });</code></pre>
      </div>

      <div class="method">
        <h4>sendWithStream(actionName, data, queueingStrategy, transfers)</h4>
        <p>发送一个流式处理的消息，返回ReadableStream。</p>
        <h5>参数：</h5>
        <ul>
          <li><code>actionName</code> - 动作名称</li>
          <li><code>data</code> - 消息数据</li>
          <li><code>queueingStrategy</code> - 队列策略</li>
          <li><code>transfers</code> - 可转移对象（可选）</li>
        </ul>
        <h5>返回：</h5>
        <p>ReadableStream，可用于读取流数据</p>
        
        <pre><code>// 获取页面文本内容的流
const textContentStream = messageHandler.sendWithStream(
  "GetTextContent",
  { pageIndex: 0 },
  { highWaterMark: 100 }
);

// 处理流数据
const reader = textContentStream.getReader();
reader.read().then(function processText({ done, value }) {
  if (done) {
    return;
  }
  console.log("接收到文本块:", value);
  return reader.read().then(processText);
});</code></pre>
      </div>

      <div class="method">
        <h4>destroy()</h4>
        <p>销毁消息处理器，清理资源。</p>
        
        <pre><code>// 销毁消息处理器
messageHandler.destroy();</code></pre>
      </div>
    </section>

    <section class="usage">
      <h2>使用示例</h2>

      <div class="example">
        <h3>初始化通信</h3>
        <pre><code>// 在PDF查看器应用程序中初始化通信
class PDFViewerApplication {
  // ...
  
  async initialize() {
    // 初始化通信模块
    initCom(this);
    
    // 创建PDF工作线程
    const worker = await PDFWorker.create();
    
    // 继续应用程序初始化
    // ...
  }
  
  // ...
}</code></pre>
      </div>

      <div class="example">
        <h3>创建工作线程和消息处理</h3>
        <pre><code>// 创建PDF工作线程
const worker = new PDFWorker({
  name: "pdf-worker",
  verbosity: 1
});

// 获取工作线程的消息处理器
const messageHandler = worker.messageHandler;

// 设置消息处理器
messageHandler.on("PageRendered", data => {
  console.log(`页面 ${data.pageNumber} 已渲染`);
});

// 发送请求加载文档
messageHandler.sendWithPromise("LoadDocument", {
  url: "document.pdf"
}).then(docData => {
  console.log("文档已加载:", docData);
}).catch(error => {
  console.error("加载文档失败:", error);
});</code></pre>
      </div>

      <div class="example">
        <h3>流式获取PDF页面内容</h3>
        <pre><code>// 获取页面操作符列表
function getPageOperatorList(pageIndex) {
  return messageHandler.sendWithStream(
    "GetOperatorList",
    {
      pageIndex,
      intent: "display"
    }
  );
}

// 处理操作符列表
const operatorListStream = getPageOperatorList(0);
const reader = operatorListStream.getReader();

async function processOperatorList() {
  while (true) {
    const { done, value } = await reader.read();
    
    if (done) {
      console.log("操作符列表接收完成");
      break;
    }
    
    // 处理接收到的操作符
    console.log("接收到操作符:", value);
  }
}

processOperatorList().catch(error => {
  console.error("处理操作符列表时出错:", error);
});</code></pre>
      </div>
    </section>

    <section class="implementation-notes">
      <h2>实现说明</h2>
      <div class="note">
        <h3>Web Workers 通信</h3>
        <p>GenericCom 使用浏览器的 Web Workers API 进行通信，使用 postMessage 方法发送消息，并通过 message 事件接收响应。这种方式允许主线程将 CPU 密集型的 PDF 处理操作委托给工作线程，同时保持用户界面的响应性。</p>
      </div>
      
      <div class="note">
        <h3>通信协议</h3>
        <p>通信消息遵循特定的协议格式，包含以下字段：</p>
        <ul>
          <li><strong>sourceName</strong>：消息发送者标识</li>
          <li><strong>targetName</strong>：消息接收者标识</li>
          <li><strong>action/callback</strong>：消息类型标识</li>
          <li><strong>callbackId</strong>：回调标识（用于匹配请求和响应）</li>
          <li><strong>data</strong>：消息数据载荷</li>
        </ul>
        <p>这种协议设计确保了异步消息处理和响应的正确匹配。</p>
      </div>
      
      <div class="note">
        <h3>流式数据处理</h3>
        <p>对于大型数据传输（如页面内容或操作符列表），GenericCom 实现了基于 ReadableStream 的流式传输机制，允许渐进式处理数据，避免一次性传输大块数据导致的性能问题。流式消息通过特殊的协议字段 <code>streamId</code> 和流控制命令（如 <code>START</code>, <code>PULL</code>, <code>ENQUEUE</code>, <code>CLOSE</code> 等）进行管理。</p>
      </div>
      
      <div class="note">
        <h3>通用实现</h3>
        <p>GenericCom 模块是为普通浏览器环境设计的通信实现。PDF.js 还有其他特定环境的通信实现，例如用于 Firefox 扩展的 FirefoxCom。通过抽象通信接口，PDF.js 能够在不同环境中运行，同时保持核心功能一致。</p>
      </div>
      
      <div class="note">
        <h3>错误处理</h3>
        <p>通信错误使用特殊的错误封装机制进行传递，确保工作线程中的错误能够正确地传递到主线程，并保留原始的错误信息和堆栈跟踪。这是通过在 postMessage 前将错误对象转换为可序列化的格式，然后在接收端重新构造错误来实现的。</p>
      </div>
    </section>

    <section class="best-practices">
      <h2>最佳实践</h2>
      <div class="practice">
        <h3>正确初始化通信</h3>
        <p>确保在应用程序启动时正确初始化通信模块，并等待工作线程就绪：</p>
        <pre><code>// 应用程序启动
async function startApp() {
  // 初始化通信
  initCom(PDFViewerApplication);
  
  // 创建并初始化工作线程
  const worker = await PDFWorker.create();
  
  // 等待工作线程就绪
  await worker.ready;
  
  // 继续初始化应用程序
  // ...
}</code></pre>
      </div>
      
      <div class="practice">
        <h3>使用可转移对象优化性能</h3>
        <p>在传输大型二进制数据时，使用可转移对象（Transferable Objects）以避免数据复制：</p>
        <pre><code>// 创建大型ArrayBuffer
const buffer = new ArrayBuffer(10 * 1024 * 1024); // 10MB
const view = new Uint8Array(buffer);

// 填充数据
// ...

// 将buffer作为可转移对象发送
messageHandler.send("ProcessData", {
  buffer: view
}, [buffer]); // 注意：传输后，原始buffer将不可用</code></pre>
      </div>
      
      <div class="practice">
        <h3>合理处理流式数据</h3>
        <p>对于大型数据操作，使用流式API避免内存压力：</p>
        <pre><code>// 获取文档文本内容的流
const textContentStream = messageHandler.sendWithStream(
  "GetDocumentText",
  { docId: "doc1" }
);

// 使用流式处理，避免一次性加载所有内容
const reader = textContentStream.getReader();
const textChunks = [];

async function readAllChunks() {
  while (true) {
    const { done, value } = await reader.read();
    if (done) break;
    textChunks.push(value);
    
    // 可以在这里处理每个数据块，而不是等待所有数据
    processTextChunk(value);
  }
  
  return textChunks;
}</code></pre>
      </div>
      
      <div class="practice">
        <h3>正确清理资源</h3>
        <p>当不再需要工作线程时，正确销毁资源以避免内存泄漏：</p>
        <pre><code>// 销毁工作线程和通信资源
async function cleanup() {
  try {
    // 终止所有待处理的操作
    await messageHandler.sendWithPromise("Terminate", null);
    
    // 销毁消息处理器
    messageHandler.destroy();
    
    // 终止工作线程
    worker.terminate();
    
    console.log("资源已清理完毕");
  } catch (error) {
    console.error("清理资源时出错:", error);
  }
}</code></pre>
      </div>
      
      <div class="practice">
        <h3>处理通信错误</h3>
        <p>始终处理通信错误，确保应用程序能够优雅地处理工作线程的故障：</p>
        <pre><code>// 发送请求并处理可能的错误
messageHandler.sendWithPromise("RenderPage", {
  pageIndex: 0,
  viewport: { width: 800, height: 600 }
})
.then(result => {
  console.log("页面渲染成功");
})
.catch(error => {
  console.error("页面渲染失败:", error);
  
  // 检查特定类型的错误
  if (error instanceof RenderingCancelledException) {
    console.log("渲染被取消");
  } else if (error.name === "WorkerTerminated") {
    console.log("工作线程已终止，尝试恢复");
    restoreWorker();
  } else {
    // 处理其他类型的错误
    showErrorToUser(error.message);
  }
});</code></pre>
      </div>
    </section>
  </div>

  <script src="doc_script.js"></script>
  <script src="js/mermaid.js"></script>
  <script>
    mermaid.initialize({ startOnLoad: true, theme: 'neutral' });
  </script>
</body>
</html> 