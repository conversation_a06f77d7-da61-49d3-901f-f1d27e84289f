<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PDFAttachmentViewer - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#constants">常量</a>
    <a href="#properties">属性</a>
    <a href="#methods">方法</a>
    <a href="#flowcharts">流程图</a>
    <a href="#examples">示例</a>
    <a href="#notes">注意事项</a>
  </div>

  <h1>PDFAttachmentViewer 模块文档</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>PDFAttachmentViewer 是 PDF.js 库中负责显示和管理 PDF 文档附件的组件。它处理文档中嵌入的文件附件的展示、下载和基本操作，使用户可以查看和访问 PDF 文档中包含的附加文件。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 常量 -->
  <div id="constants">
    <h2>常量</h2>
    
    <p>PDFAttachmentViewer 使用以下常量：</p>
    
    <ul>
      <li>
        <code>AttachmentState.INITIAL_STATE</code>: 初始状态对象
        <ul>
          <li><code>attachmentsCount</code>: 0</li>
          <li><code>keepSelectedAttachment</code>: false</li>
        </ul>
      </li>
      <li><code>BYTES_PER_KILOBYTE</code>: 每千字节的字节数 (1024)</li>
      <li><code>BYTES_PER_MEGABYTE</code>: 每兆字节的字节数 (1048576)</li>
    </ul>
  </div>

  <!-- 属性 -->
  <div id="properties">
    <h2>属性</h2>
    
    <h3>公共属性</h3>
    <ul>
      <li><code>attachments</code>: 文档附件数据</li>
      <li><code>container</code>: 附件容器元素</li>
      <li><code>eventBus</code>: 事件总线</li>
      <li><code>downloadManager</code>: 下载管理器</li>
      <li><code>l10n</code>: 本地化对象</li>
      <li><code>_pdfDocument</code>: 当前 PDF 文档对象</li>
    </ul>

    <h3>私有属性</h3>
    <ul>
      <li><code>#attachmentsMap</code>: 附件映射对象</li>
      <li><code>#renderedCapability</code>: 渲染完成的延迟对象</li>
      <li><code>#pendingDispatchEvent</code>: 待处理的事件分发标志</li>
      <li><code>#boundHandleEvent</code>: 绑定的事件处理函数</li>
    </ul>
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法</h2>
    
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">构造函数</h3>
      </div>
      <div class="method-content">
        <p>创建一个新的 PDFAttachmentViewer 实例。</p>
        <pre><code class="language-javascript">
constructor({ container, eventBus, downloadManager, l10n = undefined })
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>container</code>: 附件容器元素</li>
          <li><code>eventBus</code>: 事件总线</li>
          <li><code>downloadManager</code>: 下载管理器</li>
          <li><code>l10n</code>: 本地化对象 (可选)</li>
        </ul>
      </div>
    </div>

    <h3>核心方法</h3>
    <ul>
      <li><code>reset(keepRenderedCapability = false)</code>: 重置附件视图到初始状态</li>
      <li><code>render({ attachments, keepRenderedCapability = false })</code>: 渲染 PDF 文档的附件内容</li>
      <li><code>_dispatchEvent(attachmentsCount)</code>: 分发附件已加载事件</li>
      <li><code>_bindLink(element, { content, filename })</code>: 绑定附件项链接</li>
      <li><code>_bindPdfLink(button, content, filename)</code>: 绑定 PDF 附件链接</li>
      <li><code>_calculateSize(size)</code>: 计算附件大小的可读格式</li>
      <li><code>_appendAttachment({ id, filename, content })</code>: 添加附件项到视图</li>
    </ul>
    
    <h3>私有方法</h3>
    <ul>
      <li><code>#handleEvent()</code>: 处理事件</li>
      <li><code>#ensureAttachmentLoaded()</code>: 确保附件已加载</li>
      <li><code>#save()</code>: 保存附件</li>
      <li><code>#open()</code>: 打开附件</li>
      <li><code>#downloadData(data, filename, contentType)</code>: 下载附件数据</li>
      <li><code>#createLoadingItem()</code>: 创建加载中的项目</li>
    </ul>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <h3>附件初始化与渲染流程</h3>
    <p>1. 创建 PDFAttachmentViewer 实例<br>
    2. 调用 <code>render()</code> 方法设置文档附件数据<br>
    3. 解析附件信息<br>
    4. 生成 DOM 元素并添加到容器<br>
    5. 绑定点击事件<br>
    6. 分发附件加载完成事件</p>
    
    <div class="mermaid">
      graph TD
          A[创建 PDFAttachmentViewer 实例] --> B[调用 render 方法]
          B --> C[解析附件信息]
          C --> D[生成附件项 DOM 元素]
          D --> E[为每个附件添加点击事件]
          E --> F[分发附件加载完成事件]
    </div>
    
    <h3>附件下载流程</h3>
    <p>1. 用户点击附件下载按钮<br>
    2. 触发 <code>#save</code> 方法处理点击事件<br>
    3. 获取附件内容<br>
    4. 使用 downloadManager 处理下载<br>
    5. 触发文件保存对话框</p>
    
    <div class="mermaid">
      graph TD
          A[用户点击附件下载按钮] --> B[调用 #save 方法]
          B --> C[获取附件内容]
          C --> D[调用 downloadManager 的下载方法]
          D --> E[浏览器触发文件保存对话框]
    </div>
    
    <h3>附件打开流程</h3>
    <p>1. 用户点击附件打开按钮<br>
    2. 触发 <code>#open</code> 方法<br>
    3. 获取附件内容<br>
    4. 创建 Blob URL<br>
    5. 在新窗口打开或预览附件</p>
    
    <div class="mermaid">
      graph TD
          A[用户点击附件打开按钮] --> B[调用 #open 方法]
          B --> C[获取附件内容]
          C --> D[创建 Blob URL]
          D --> E[在新窗口打开附件]
    </div>
  </div>

  <!-- 示例 -->
  <div id="examples">
    <h2>使用示例</h2>
    
    <h3>基本用法</h3>
    <pre><code class="language-javascript">
// 创建 PDFAttachmentViewer 实例
const attachmentViewer = new PDFAttachmentViewer({
  container: document.getElementById('attachmentView'),
  eventBus: eventBus,
  downloadManager: downloadManager
});

// 加载文档时渲染附件
const loadingTask = pdfjsLib.getDocument('document.pdf');
loadingTask.promise.then(function(pdfDocument) {
  pdfDocument.getAttachments().then(function(attachments) {
    attachmentViewer.render({ attachments, pdfDocument });
  });
});
    </code></pre>
    
    <h3>监听附件加载事件</h3>
    <pre><code class="language-javascript">
// 监听附件加载事件
eventBus.on("attachmentsloaded", function(evt) {
  console.log(`文档包含 ${evt.attachmentsCount} 个附件`);
  
  // 根据是否有附件更新 UI
  if (evt.attachmentsCount > 0) {
    attachmentButton.classList.remove("hidden");
  } else {
    attachmentButton.classList.add("hidden");
  }
});
    </code></pre>
  </div>

  <!-- 注意事项 -->
  <div id="notes">
    <h2>注意事项</h2>
    
    <ul>
      <li>附件功能依赖于 PDF 文档本身包含的附件。如果 PDF 文档没有附件，则渲染方法不会生成任何内容。</li>
      <li>附件下载功能依赖于 downloadManager 的正确配置。确保 downloadManager 已正确初始化。</li>
      <li>某些 PDF 文档可能包含不安全的附件。建议在打开附件前进行安全检查或提醒用户潜在风险。</li>
      <li>附件预览功能依赖于浏览器对相应文件类型的支持。某些文件类型可能无法直接在浏览器中预览。</li>
      <li>大型附件的下载可能需要较长时间，建议实现下载进度指示。</li>
      <li>在某些受限环境中（如跨域限制），附件下载功能可能受到限制，需要适当的 CORS 配置。</li>
    </ul>
  </div>

  <script>
    // 在页面加载完成后初始化 Mermaid
    document.addEventListener('DOMContentLoaded', function() {
      mermaid.initialize({ startOnLoad: true });
      
      // 生成目录
      generateTOC();
    });
  </script>
</body>
</html> 