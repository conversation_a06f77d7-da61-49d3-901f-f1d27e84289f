<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PDFFindBar - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#constants">常量</a>
    <a href="#properties">属性</a>
    <a href="#methods">方法</a>
    <a href="#flowcharts">流程图</a>
    <a href="#examples">示例</a>
    <a href="#notes">注意事项</a>
  </div>

  <h1>PDFFindBar 模块文档</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>PDFFindBar 是 PDF.js 库中提供文档内容搜索用户界面的组件。它提供了一个搜索栏，允许用户在 PDF 文档中搜索和高亮显示文本。PDFFindBar 与 PDFFindController 协同工作，前者负责用户界面交互，后者负责实际的搜索逻辑。此组件提供了多种搜索选项，如区分大小写、全字匹配、突出显示所有匹配项等。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>
  
  <!-- 常量 -->
  <div id="constants">
    <h2>常量</h2>
    
    <p>PDFFindBar 使用以下常量：</p>
    
    <ul>
      <li>
        <code>FindState</code>: 搜索状态枚举
        <ul>
          <li><code>FOUND</code>: 找到匹配项</li>
          <li><code>NOT_FOUND</code>: 未找到匹配项</li>
          <li><code>WRAPPED</code>: 搜索已循环到文档开始/结束</li>
          <li><code>PENDING</code>: 搜索正在进行中</li>
        </ul>
      </li>
      <li>
        <code>MATCHES_COUNT_LIMIT</code>: 匹配计数限制</li>
      <li>
        <code>FIND_TIMEOUT</code>: 搜索超时时间（毫秒）</li>
      <li>
        <code>FIND_SCROLL_TIMEOUT_MS</code>: 搜索滚动超时时间（毫秒）</li>
    </ul>
  </div>

  <!-- 属性 -->
  <div id="properties">
    <h2>属性</h2>
    
    <h3>公共属性</h3>
    <ul>
      <li><code>opened</code>: 搜索栏是否已打开</li>
      <li><code>bar</code>: 搜索栏 DOM 元素</li>
      <li><code>findField</code>: 搜索输入字段</li>
      <li><code>highlightAll</code>: 是否高亮所有匹配项</li>
      <li><code>caseSensitive</code>: 是否区分大小写</li>
      <li><code>matchDiacritics</code>: 是否匹配变音符号</li>
      <li><code>entireWord</code>: 是否匹配整个单词</li>
      <li><code>findMsg</code>: 搜索信息显示元素</li>
      <li><code>findResultsCount</code>: 搜索结果计数元素</li>
      <li><code>findPreviousButton</code>: 查找上一个按钮</li>
      <li><code>findNextButton</code>: 查找下一个按钮</li>
    </ul>

    <h3>私有属性</h3>
    <ul>
      <li><code>#eventBus</code>: 事件总线</li>
      <li><code>#l10n</code>: 本地化对象</li>
      <li><code>#findController</code>: 查找控制器</li>
      <li><code>#toggleButton</code>: 切换按钮</li>
      <li><code>#highlightAllCheckbox</code>: 高亮所有匹配项复选框</li>
      <li><code>#caseSensitiveCheckbox</code>: 区分大小写复选框</li>
      <li><code>#matchDiacriticsCheckbox</code>: 匹配变音符号复选框</li>
      <li><code>#entireWordCheckbox</code>: 匹配整个单词复选框</li>
      <li><code>#findBarLabel</code>: 查找栏标签</li>
      <li><code>#findBarClose</code>: 查找栏关闭按钮</li>
      <li><code>#findStatus</code>: 查找状态元素</li>
      <li><code>#currentPage</code>: 当前页面元素</li>
      <li><code>#pageCount</code>: 页面计数元素</li>
      <li><code>#matchesCountMsg</code>: 匹配计数消息元素</li>
      <li><code>#scrollFindResultsIntoViewEl</code>: 滚动查找结果到视图元素</li>
    </ul>
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法</h2>
    
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">构造函数</h3>
      </div>
      <div class="method-content">
        <p>创建一个新的 PDFFindBar 实例。</p>
        <pre><code class="language-javascript">
constructor({
  l10n,
  eventBus,
  findController,
})
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>l10n</code>: 本地化对象，用于翻译搜索栏界面文本</li>
          <li><code>eventBus</code>: 事件总线，用于与其他组件通信</li>
          <li><code>findController</code>: 查找控制器，负责实际的搜索逻辑</li>
        </ul>
      </div>
    </div>

    <h3>核心方法</h3>
    <ul>
      <li><code>reset()</code>: 重置搜索栏状态</li>
      <li><code>open()</code>: 打开搜索栏</li>
      <li><code>close()</code>: 关闭搜索栏</li>
      <li><code>toggle()</code>: 切换搜索栏的显示状态</li>
      <li><code>dispatchEvent(type, findPrev)</code>: 分发查找事件</li>
      <li><code>updateUIState(state, previous, matchesCount)</code>: 更新界面状态</li>
      <li><code>updateResultsCount(matchesCount)</code>: 更新结果计数</li>
    </ul>
    
    <h3>事件处理方法</h3>
    <ul>
      <li><code>#handleEvent(evt)</code>: 处理事件</li>
      <li><code>#bindListeners()</code>: 绑定事件监听器</li>
      <li><code>#unbindListeners()</code>: 解除事件监听器</li>
      <li><code>#onInput()</code>: 处理输入事件</li>
      <li><code>#onKeyDown(evt)</code>: 处理键盘按键事件</li>
      <li><code>#onFind(evt)</code>: 处理查找事件</li>
      <li><code>#onFindBarClose(evt)</code>: 处理查找栏关闭事件</li>
      <li><code>#onCaseSensitiveChange(evt)</code>: 处理区分大小写选项变更</li>
      <li><code>#onEntireWordChange(evt)</code>: 处理全字匹配选项变更</li>
      <li><code>#onHighlightAllChange(evt)</code>: 处理高亮所有匹配项选项变更</li>
      <li><code>#onMatchDiacriticsChange(evt)</code>: 处理匹配变音符号选项变更</li>
    </ul>
    
    <h3>辅助方法</h3>
    <ul>
      <li><code>#updateUIResultsCount()</code>: 更新界面结果计数</li>
      <li><code>#updateSearchMatchesCount(matchesCount)</code>: 更新搜索匹配计数</li>
      <li><code>#setTitle(title, content)</code>: 设置元素的标题和内容</li>
      <li><code>#toggleElement(element, enabled)</code>: 切换元素的启用状态</li>
      <li><code>#scrollFindIntoView(scrollElement, options)</code>: 滚动查找结果到视图</li>
      <li><code>#isScrollFindResultsIntoViewEnabled()</code>: 检查是否启用滚动查找结果到视图</li>
    </ul>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <h3>搜索流程</h3>
    <p>1. 用户打开搜索栏<br>
    2. 输入搜索词<br>
    3. 设置搜索选项<br>
    4. 提交搜索<br>
    5. 显示搜索结果</p>
    
    <div class="mermaid">
      graph TD
          A[用户点击搜索按钮] --> B[调用 PDFFindBar.open 方法]
          B --> C[显示搜索栏界面]
          C --> D[用户输入搜索词]
          D --> E[用户设置搜索选项]
          E --> F[用户按回车或点击查找按钮]
          F --> G[调用 dispatchEvent 方法]
          G --> H[触发 findagain 事件]
          H --> I[PDFFindController 处理搜索]
          I --> J[查找匹配的文本]
          J --> K{是否找到匹配?}
          K -->|是| L[高亮匹配文本]
          K -->|否| M[显示未找到消息]
          L --> N[更新 UI 状态为 FOUND]
          M --> O[更新 UI 状态为 NOT_FOUND]
          N --> P[更新匹配计数]
          O --> P
    </div>
    
    <h3>用户交互流程</h3>
    <p>1. 用户与搜索栏交互<br>
    2. 切换搜索选项<br>
    3. 执行前/后导航<br>
    4. 关闭搜索栏</p>
    
    <div class="mermaid">
      graph TD
          A[搜索栏处于打开状态] --> B[用户交互选项]
          B --> C[输入/修改搜索词]
          B --> D[切换区分大小写选项]
          B --> E[切换整词匹配选项]
          B --> F[切换高亮全部选项]
          B --> G[切换匹配变音符号选项]
          B --> H[点击上一个匹配按钮]
          B --> I[点击下一个匹配按钮]
          B --> J[点击关闭按钮]
          C --> K[自动触发搜索]
          D --> L[重新执行搜索]
          E --> L
          F --> L
          G --> L
          H --> M[查找上一个匹配]
          I --> N[查找下一个匹配]
          J --> O[关闭搜索栏]
    </div>
    
    <h3>状态更新流程</h3>
    <p>1. 搜索执行完毕<br>
    2. 状态传递给搜索栏<br>
    3. 更新界面显示<br>
    4. 滚动到匹配位置</p>
    
    <div class="mermaid">
      graph TD
          A[PDFFindController 完成搜索] --> B[触发 updatefindmatchescount 事件]
          A --> C[触发 updatefindcontrolstate 事件]
          B --> D[调用 PDFFindBar.updateResultsCount]
          C --> E[调用 PDFFindBar.updateUIState]
          D --> F[更新匹配计数显示]
          E --> G[根据状态更新界面]
          G --> H{状态是什么?}
          H -->|FOUND| I[显示成功匹配信息]
          H -->|NOT_FOUND| J[显示未找到信息]
          H -->|PENDING| K[显示搜索中信息]
          H -->|WRAPPED| L[显示搜索已循环信息]
          I --> M[滚动到当前匹配位置]
          J --> N[重置滚动位置]
          L --> M
    </div>
  </div>

  <!-- 示例 -->
  <div id="examples">
    <h2>使用示例</h2>
    
    <h3>基本用法</h3>
    <pre><code class="language-javascript">
// 创建事件总线
const eventBus = new EventBus();

// 创建查找控制器
const findController = new PDFFindController({
  linkService: pdfLinkService,
  eventBus: eventBus
});

// 创建 PDFFindBar 实例
const findBar = new PDFFindBar({
  eventBus,
  findController,
  l10n: pdfViewerL10n // 本地化对象
});

// 初始化搜索栏 DOM 元素
const findBarElement = document.getElementById('findbar');
findBarElement.appendChild(findBar.bar);

// 绑定查找按钮事件
document.getElementById('viewFind').addEventListener('click', function() {
  findBar.toggle();
});

// 监听查找事件
eventBus.on('find', (evt) => {
  console.log('查找事件:', evt);
});

// 监听查找状态更新事件
eventBus.on('updatefindcontrolstate', (evt) => {
  console.log('查找状态更新:', evt.state);
});

// 监听查找匹配计数更新事件
eventBus.on('updatefindmatchescount', (evt) => {
  console.log('匹配计数更新:', evt.matchesCount);
});
    </code></pre>
    
    <h3>自定义搜索栏</h3>
    <pre><code class="language-javascript">
// 扩展 PDFFindBar 类，添加自定义功能
class CustomFindBar extends PDFFindBar {
  constructor(options) {
    super(options);
    
    // 添加额外的搜索选项
    this.fuzzyMatch = false;
    this.searchInComments = false;
  }
  
  // 重写 reset 方法以包含新选项
  reset() {
    super.reset();
    this.fuzzyMatch = false;
    this.searchInComments = false;
    
    // 重置额外的复选框
    if (this.#fuzzyMatchCheckbox) {
      this.#fuzzyMatchCheckbox.checked = false;
    }
    if (this.#searchInCommentsCheckbox) {
      this.#searchInCommentsCheckbox.checked = false;
    }
  }
  
  // 创建自定义的查找栏
  #createUICustom() {
    // 首先调用原始方法创建基本 UI
    super._createUI();
    
    // 添加模糊匹配选项
    const fuzzyMatchContainer = document.createElement('div');
    fuzzyMatchContainer.className = 'findbar-option';
    
    this.#fuzzyMatchCheckbox = document.createElement('input');
    this.#fuzzyMatchCheckbox.type = 'checkbox';
    this.#fuzzyMatchCheckbox.id = 'findFuzzyMatch';
    
    const fuzzyMatchLabel = document.createElement('label');
    fuzzyMatchLabel.setAttribute('for', 'findFuzzyMatch');
    fuzzyMatchLabel.textContent = '模糊匹配';
    
    fuzzyMatchContainer.appendChild(this.#fuzzyMatchCheckbox);
    fuzzyMatchContainer.appendChild(fuzzyMatchLabel);
    
    // 添加在注释中搜索选项
    const searchInCommentsContainer = document.createElement('div');
    searchInCommentsContainer.className = 'findbar-option';
    
    this.#searchInCommentsCheckbox = document.createElement('input');
    this.#searchInCommentsCheckbox.type = 'checkbox';
    this.#searchInCommentsCheckbox.id = 'findSearchInComments';
    
    const searchInCommentsLabel = document.createElement('label');
    searchInCommentsLabel.setAttribute('for', 'findSearchInComments');
    searchInCommentsLabel.textContent = '在注释中搜索';
    
    searchInCommentsContainer.appendChild(this.#searchInCommentsCheckbox);
    searchInCommentsContainer.appendChild(searchInCommentsLabel);
    
    // 将新选项添加到现有的选项组中
    const optionsContainer = this.bar.querySelector('.findbar-options');
    optionsContainer.appendChild(fuzzyMatchContainer);
    optionsContainer.appendChild(searchInCommentsContainer);
    
    // 为新选项添加事件监听器
    this.#fuzzyMatchCheckbox.addEventListener('change', this.#onFuzzyMatchChange.bind(this));
    this.#searchInCommentsCheckbox.addEventListener('change', this.#onSearchInCommentsChange.bind(this));
  }
  
  // 处理模糊匹配选项变更
  #onFuzzyMatchChange(evt) {
    this.fuzzyMatch = evt.target.checked;
    this.dispatchEvent('find', false);
  }
  
  // 处理在注释中搜索选项变更
  #onSearchInCommentsChange(evt) {
    this.searchInComments = evt.target.checked;
    this.dispatchEvent('find', false);
  }
  
  // 重写 dispatchEvent 方法以包含新选项
  dispatchEvent(type, findPrev = false) {
    this.#eventBus.dispatch('find', {
      source: this,
      type,
      query: this.findField.value,
      caseSensitive: this.caseSensitive,
      entireWord: this.entireWord,
      highlightAll: this.highlightAll,
      findPrevious: findPrev,
      matchDiacritics: this.matchDiacritics,
      fuzzyMatch: this.fuzzyMatch,
      searchInComments: this.searchInComments
    });
  }
}

// 使用自定义查找栏
const customFindBar = new CustomFindBar({
  eventBus,
  findController,
  l10n: pdfViewerL10n
});

// 初始化自定义查找栏
const findBarElement = document.getElementById('findbar');
findBarElement.appendChild(customFindBar.bar);
    </code></pre>
    
    <h3>添加搜索历史功能</h3>
    <pre><code class="language-javascript">
// 实现一个简单的搜索历史管理器
class SearchHistoryManager {
  constructor(maxHistoryItems = 10) {
    this.history = [];
    this.maxHistoryItems = maxHistoryItems;
    this.currentIndex = -1;
    
    // 尝试从 localStorage 加载历史记录
    this.loadHistory();
  }
  
  // 添加搜索词到历史记录
  addToHistory(searchTerm) {
    if (!searchTerm || searchTerm.trim() === '') {
      return;
    }
    
    // 移除重复项
    const index = this.history.indexOf(searchTerm);
    if (index !== -1) {
      this.history.splice(index, 1);
    }
    
    // 添加到历史记录开头
    this.history.unshift(searchTerm);
    
    // 如果超过最大历史记录数，删除最旧的记录
    if (this.history.length > this.maxHistoryItems) {
      this.history.pop();
    }
    
    // 重置当前索引
    this.currentIndex = -1;
    
    // 保存到 localStorage
    this.saveHistory();
  }
  
  // 获取上一个历史记录
  getPrevious() {
    if (this.history.length === 0) {
      return '';
    }
    
    this.currentIndex = Math.min(this.currentIndex + 1, this.history.length - 1);
    return this.history[this.currentIndex];
  }
  
  // 获取下一个历史记录
  getNext() {
    if (this.history.length === 0 || this.currentIndex <= 0) {
      this.currentIndex = -1;
      return '';
    }
    
    this.currentIndex--;
    return this.currentIndex >= 0 ? this.history[this.currentIndex] : '';
  }
  
  // 清除历史记录
  clearHistory() {
    this.history = [];
    this.currentIndex = -1;
    this.saveHistory();
  }
  
  // 保存历史记录到 localStorage
  saveHistory() {
    try {
      localStorage.setItem('pdf_search_history', JSON.stringify(this.history));
    } catch (error) {
      console.error('保存搜索历史失败:', error);
    }
  }
  
  // 从 localStorage 加载历史记录
  loadHistory() {
    try {
      const savedHistory = localStorage.getItem('pdf_search_history');
      if (savedHistory) {
        this.history = JSON.parse(savedHistory);
      }
    } catch (error) {
      console.error('加载搜索历史失败:', error);
      this.history = [];
    }
  }
  
  // 获取所有历史记录
  getAllHistory() {
    return [...this.history];
  }
}

// 扩展 PDFFindBar，添加历史记录功能
function enhanceFindBarWithHistory(findBar) {
  // 创建历史记录管理器
  const historyManager = new SearchHistoryManager();
  
  // 保存原始的 onKeyDown 方法引用
  const originalOnKeyDown = findBar._onKeyDown || findBar.onKeyDown;
  
  // 重写 onKeyDown 方法，添加历史浏览功能
  findBar._onKeyDown = function(evt) {
    // 处理上下箭头键以浏览历史记录
    if (evt.key === 'ArrowUp' || evt.key === 'ArrowDown') {
      const searchTerm = evt.key === 'ArrowUp' 
        ? historyManager.getPrevious() 
        : historyManager.getNext();
      
      if (searchTerm) {
        this.findField.value = searchTerm;
        evt.preventDefault();
      }
      return;
    }
    
    // 处理 Enter 键以添加当前搜索词到历史记录
    if (evt.key === 'Enter') {
      historyManager.addToHistory(this.findField.value);
    }
    
    // 调用原始方法
    originalOnKeyDown.call(this, evt);
  };
  
  // 添加历史记录下拉菜单
  function addHistoryDropdown() {
    // 创建下拉按钮
    const historyButton = document.createElement('button');
    historyButton.className = 'findbar-history-button';
    historyButton.textContent = '▾';
    historyButton.title = '搜索历史';
    
    // 创建下拉菜单
    const historyDropdown = document.createElement('div');
    historyDropdown.className = 'findbar-history-dropdown';
    historyDropdown.style.display = 'none';
    
    // 将按钮和下拉菜单添加到搜索字段容器
    const findFieldContainer = findBar.findField.parentElement;
    findFieldContainer.style.position = 'relative';
    findFieldContainer.appendChild(historyButton);
    findFieldContainer.appendChild(historyDropdown);
    
    // 切换历史下拉菜单显示
    historyButton.addEventListener('click', function(evt) {
      evt.preventDefault();
      
      if (historyDropdown.style.display === 'none') {
        // 更新历史列表
        updateHistoryDropdown();
        historyDropdown.style.display = 'block';
      } else {
        historyDropdown.style.display = 'none';
      }
    });
    
    // 点击文档其他位置时关闭下拉菜单
    document.addEventListener('click', function(evt) {
      if (!findFieldContainer.contains(evt.target)) {
        historyDropdown.style.display = 'none';
      }
    });
    
    // 更新历史下拉菜单内容
    function updateHistoryDropdown() {
      historyDropdown.innerHTML = '';
      
      const history = historyManager.getAllHistory();
      
      if (history.length === 0) {
        const emptyItem = document.createElement('div');
        emptyItem.className = 'findbar-history-item empty';
        emptyItem.textContent = '无搜索历史';
        historyDropdown.appendChild(emptyItem);
        return;
      }
      
      // 添加历史记录项
      history.forEach(term => {
        const historyItem = document.createElement('div');
        historyItem.className = 'findbar-history-item';
        historyItem.textContent = term;
        
        historyItem.addEventListener('click', function() {
          findBar.findField.value = term;
          historyDropdown.style.display = 'none';
          findBar.dispatchEvent('find', false);
        });
        
        historyDropdown.appendChild(historyItem);
      });
      
      // 添加清除历史按钮
      const clearButton = document.createElement('div');
      clearButton.className = 'findbar-history-clear';
      clearButton.textContent = '清除历史记录';
      
      clearButton.addEventListener('click', function() {
        historyManager.clearHistory();
        historyDropdown.style.display = 'none';
      });
      
      historyDropdown.appendChild(clearButton);
    }
    
    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
      .findbar-history-button {
        position: absolute;
        right: 5px;
        top: 50%;
        transform: translateY(-50%);
        border: none;
        background: none;
        cursor: pointer;
        color: #666;
        font-size: 12px;
      }
      
      .findbar-history-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #ccc;
        border-top: none;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        z-index: 1000;
        max-height: 200px;
        overflow-y: auto;
      }
      
      .findbar-history-item {
        padding: 8px 10px;
        cursor: pointer;
        font-size: 14px;
        border-bottom: 1px solid #eee;
      }
      
      .findbar-history-item:hover {
        background-color: #f5f5f5;
      }
      
      .findbar-history-item.empty {
        font-style: italic;
        color: #999;
        cursor: default;
      }
      
      .findbar-history-clear {
        padding: 8px 10px;
        text-align: center;
        color: #f44336;
        cursor: pointer;
        font-size: 13px;
        border-top: 1px solid #eee;
      }
      
      .findbar-history-clear:hover {
        background-color: #ffebee;
      }
    `;
    
    document.head.appendChild(style);
  }
  
  // 初始化历史下拉菜单
  addHistoryDropdown();
  
  return findBar;
}

// 使用增强的查找栏
const findBar = new PDFFindBar({
  eventBus,
  findController,
  l10n: pdfViewerL10n
});

// 添加历史记录功能
enhanceFindBarWithHistory(findBar);

// 初始化增强的查找栏
const findBarElement = document.getElementById('findbar');
findBarElement.appendChild(findBar.bar);
    </code></pre>
  </div>

  <!-- 注意事项 -->
  <div id="notes">
    <h2>注意事项</h2>
    
    <ul>
      <li>PDFFindBar 主要负责用户界面交互，实际的搜索逻辑由 PDFFindController 处理。</li>
      <li>搜索栏应提供清晰的用户反馈，特别是在搜索状态变化时（如找到匹配项、未找到匹配项等）。</li>
      <li>为提升用户体验，建议实现键盘快捷键支持，如 Ctrl+F 打开搜索栏，Enter 查找下一个，Shift+Enter 查找上一个。</li>
      <li>在移动设备上，搜索栏的设计应考虑触摸友好性，如增大按钮尺寸和间距。</li>
      <li>对于大型文档，搜索可能需要较长时间，应显示适当的加载指示器和进度信息。</li>
      <li>考虑添加搜索历史功能，方便用户重复使用之前的搜索词。</li>
      <li>当文档中有大量匹配项时，过度的高亮显示可能影响性能，应考虑设置合理的限制。</li>
      <li>搜索栏应支持国际化和本地化，以适应不同语言环境的用户。</li>
      <li>关注辅助功能支持，确保搜索栏可通过键盘完全操作，并提供适当的 ARIA 属性。</li>
      <li>在实现自定义搜索功能时，应保持与 PDFFindController 的兼容性，确保事件和参数传递正确。</li>
    </ul>
  </div>

  <script>
    // 在页面加载完成后初始化 Mermaid
    document.addEventListener('DOMContentLoaded', function() {
      mermaid.initialize({ startOnLoad: true });
      
      // 生成目录
      generateTOC();
    });
  </script>
</body>
</html>
