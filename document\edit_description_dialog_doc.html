<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>EditDescriptionDialog - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#methods">方法列表</a>
    <a href="#flowcharts">流程图</a>
  </div>

  <h1>EditDescriptionDialog</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>EditDescriptionDialog是PDF.js中用于编辑注释和签名描述的对话框组件。它提供了一个用户界面，允许用户查看、修改和清除PDF文档中注释或签名的描述文本。此组件主要与签名编辑器一起使用，支持签名预览的展示、描述文本的编辑和存储，以及与用户交互相关的事件处理。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法列表</h2>
    
    <!-- 构造函数 -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">constructor({dialog, description, cancelButton, updateButton, editSignatureView}, overlayManager)</h3>
      </div>
      <div class="method-content">
        <p>创建编辑描述对话框的实例，设置必要的DOM元素引用和事件处理。</p>
        <pre><code class="language-javascript">
constructor({
  dialog, // 对话框
  description, // 描述容器
  cancelButton, // 取消按钮
  updateButton, // 更新按钮
  editSignatureView // 编辑签名视图
}, overlayManager) { // 构造函数
  const descriptionInput = this.#description = description.firstElementChild; // 获取描述输入框
  this.#signatureSVG = editSignatureView; // 设置签名SVG
  this.#dialog = dialog; // 设置对话框
  this.#overlayManager = overlayManager; // 设置覆盖层管理器
  dialog.addEventListener("close", this.#close.bind(this)); // 添加关闭事件监听器
  dialog.addEventListener("contextmenu", e => { // 添加右键菜单事件监听器
    if (e.target !== this.#description) { // 如果目标不是描述输入框
      e.preventDefault(); // 阻止默认行为
    }
  });
  cancelButton.addEventListener("click", this.#cancel.bind(this)); // 添加取消按钮的点击事件监听器
  updateButton.addEventListener("click", this.#update.bind(this)); // 添加更新按钮的点击事件监听器
  const clearDescription = description.lastElementChild; // 获取清除描述按钮
  clearDescription.addEventListener("click", () => { // 添加清除描述按钮的点击事件监听器
    descriptionInput.value = ""; // 清空描述输入框
    clearDescription.disabled = true; // 禁用清除描述按钮
    updateButton.disabled = this.#previousDescription === ""; // 根据之前的描述是否为空设置更新按钮的禁用状态
  });
  descriptionInput.addEventListener("input", () => { // 添加描述输入框的输入事件监听器
    const {
      value // 输入框的值
    } = descriptionInput; // 从描述输入框获取
    clearDescription.disabled = value === ""; // 根据值是否为空设置清除描述按钮的禁用状态
    updateButton.disabled = value === this.#previousDescription; // 根据值是否与之前的描述相同设置更新按钮的禁用状态
    editSignatureView.setAttribute("aria-label", value); // 设置编辑签名视图的aria-label属性为值
  }, {
    passive: true // 设置为被动监听器
  });
  overlayManager.register(dialog); // 向覆盖层管理器注册对话框
}
        </code></pre>
      </div>
    </div>
    
    <!-- open方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">async open(editor)</h3>
      </div>
      <div class="method-content">
        <p>打开编辑描述对话框，显示签名预览和当前描述，并注册必要的事件。</p>
        <pre><code class="language-javascript">
async open(editor) { // 打开编辑描述对话框的异步方法
  this.#uiManager = editor._uiManager; // 设置UI管理器为编辑器的UI管理器
  this.#currentEditor = editor; // 设置当前编辑器
  this.#previousDescription = this.#description.value = editor.description; // 设置之前的描述和描述输入框的值为编辑器的描述
  this.#description.dispatchEvent(new Event("input")); // 触发描述输入框的输入事件
  this.#uiManager.removeEditListeners(); // 移除编辑监听器
  const { // 使用解构赋值
    areContours, // 是否为轮廓
    outline // 轮廓
  } = editor.getSignaturePreview(); // 获取签名预览
  const svgFactory = new DOMSVGFactory(); // 创建SVG工厂实例
  const path = svgFactory.createElement("path"); // 创建路径元素
  this.#signatureSVG.append(path); // 将路径添加到签名SVG中
  this.#signatureSVG.setAttribute("viewBox", outline.viewBox); // 设置签名SVG的视图框属性
  path.setAttribute("d", outline.toSVGPath()); // 设置路径的d属性为轮廓的SVG路径
  if (areContours) { // 如果是轮廓
    path.classList.add("contours"); // 添加轮廓类
  }
  await this.#overlayManager.open(this.#dialog); // 打开对话框
}
        </code></pre>
      </div>
    </div>
    
    <!-- 私有方法: #update -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">#update()</h3>
      </div>
      <div class="method-content">
        <p>更新签名描述，保存用户的修改并报告遥测数据。</p>
        <pre><code class="language-javascript">
async #update() { // 更新描述的异步私有方法
  this.#currentEditor._reportTelemetry({ // 报告遥测数据
    action: "pdfjs.signature.edit_description", // 动作为编辑签名描述
    data: { // 数据
      hasBeenChanged: true // 已被更改
    }
  });
  this.#currentEditor.description = this.#description.value; // 设置当前编辑器的描述为描述输入框的值
  this.#finish(); // 完成操作
}
        </code></pre>
      </div>
    </div>
    
    <!-- 私有方法: #cancel -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">#cancel()</h3>
      </div>
      <div class="method-content">
        <p>取消对话框操作，不保存修改并报告遥测数据。</p>
        <pre><code class="language-javascript">
#cancel() { // 取消的私有方法
  this.#currentEditor._reportTelemetry({ // 报告遥测数据
    action: "pdfjs.signature.edit_description", // 动作为编辑签名描述
    data: { // 数据
      hasBeenChanged: false // 未被更改
    }
  });
  this.#finish(); // 完成操作
}
        </code></pre>
      </div>
    </div>
    
    <!-- 私有方法: #finish -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">#finish()</h3>
      </div>
      <div class="method-content">
        <p>完成编辑操作，关闭对话框。</p>
        <pre><code class="language-javascript">
#finish() { // 完成的私有方法
  this.#overlayManager.closeIfActive(this.#dialog); // 如果对话框处于活动状态则关闭
}
        </code></pre>
      </div>
    </div>
    
    <!-- 私有方法: #close -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">#close()</h3>
      </div>
      <div class="method-content">
        <p>关闭对话框时的清理操作，恢复UI状态并清除引用。</p>
        <pre><code class="language-javascript">
#close() { // 关闭的私有方法
  this.#uiManager?.addEditListeners(); // 如果存在UI管理器则添加编辑监听器
  this.#uiManager = null; // 重置UI管理器
  this.#currentEditor = null; // 重置当前编辑器
  this.#signatureSVG.firstElementChild.remove(); // 移除签名SVG的第一个子元素
}
        </code></pre>
      </div>
    </div>
    
    <!-- 私有属性 -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">私有属性</h3>
      </div>
      <div class="method-content">
        <p>EditDescriptionDialog类使用以下私有属性存储状态和引用：</p>
        <ul>
          <li><strong>#currentEditor</strong> - 当前正在编辑的编辑器实例</li>
          <li><strong>#previousDescription</strong> - 打开对话框时的初始描述文本</li>
          <li><strong>#description</strong> - 描述输入框元素</li>
          <li><strong>#dialog</strong> - 对话框DOM元素</li>
          <li><strong>#overlayManager</strong> - 覆盖层管理器实例</li>
          <li><strong>#signatureSVG</strong> - 显示签名预览的SVG元素</li>
          <li><strong>#uiManager</strong> - 编辑器UI管理器实例</li>
        </ul>
      </div>
    </div>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <!-- 对话框使用流程图 -->
    <div class="mermaid">
      graph TD
        A["SignatureEditor触发编辑描述"] --> B["调用EditDescriptionDialog.open(editor)"]
        B --> C["设置编辑器引用和初始描述"]
        C --> D["移除UI编辑监听器"]
        D --> E["获取签名预览"]
        E --> F["创建SVG路径并设置签名预览"]
        F --> G["打开对话框"]
        G --> H{"用户操作"}
        H -->|"点击取消"| I["调用#cancel()"]
        H -->|"点击更新"| J["调用#update()"]
        H -->|"点击清除"| K["清空描述内容"]
        I --> L["不保存修改"]
        J --> M["保存修改"]
        K --> H
        L --> N["关闭对话框"]
        M --> N
        N --> O["调用#close()清理资源"]
    </div>
    
    <!-- 对话框与组件交互图 -->
    <div class="mermaid">
      graph LR
        A["EditDescriptionDialog"] -->|"获取/设置描述"| B["SignatureEditor"]
        A -->|"管理对话框"| C["OverlayManager"]
        A -->|"展示"| D["SVG预览"]
        A -->|"交互"| E["描述输入框"]
        A -->|"交互"| F["按钮控件"]
        B -->|"报告"| G["遥测系统"]
        B -->|"提供UI管理"| H["UIManager"]
        A -->|"控制"| H
    </div>
  </div>

  <!-- 使用示例 -->
  <div id="usage-example">
    <h2>使用示例</h2>
    <p>以下是EditDescriptionDialog在PDF.js中的典型使用方式：</p>
    <pre><code class="language-javascript">
// 在SignatureManager中创建编辑描述对话框实例
constructor(addSignatureDialog, editSignatureDialog, createSignature, overlayManager, l10n, storage, eventBus) {
  // ...其他初始化代码...
  
  // 获取编辑签名对话框的DOM元素
  const editSignatureElements = {
    dialog: editSignatureDialog,
    description: editSignatureDialog.querySelector(".editSignatureDescription"),
    cancelButton: editSignatureDialog.querySelector(".editSignatureCancel"),
    updateButton: editSignatureDialog.querySelector(".editSignatureUpdate"),
    editSignatureView: editSignatureDialog.querySelector(".editSignatureView")
  };
  
  // 创建EditDescriptionDialog实例
  this.#editDescriptionDialog = new EditDescriptionDialog(editSignatureElements, overlayManager);
  
  // ...其他初始化代码...
}

// 在需要编辑描述时打开对话框
async editDescription(editor) {
  await this.#editDescriptionDialog.open(editor);
}

// 在用户界面中触发编辑描述操作
documentElement.addEventListener("click", async event => {
  if (event.target.classList.contains("editSignatureDescription")) {
    const editor = this.#findSignatureEditor(event.target);
    if (editor) {
      await this.editDescription(editor);
    }
  }
});
    </code></pre>
  </div>

  <!-- 实现细节 -->
  <div id="implementation-details">
    <h2>实现细节</h2>
    <p>EditDescriptionDialog的实现采用了以下设计策略：</p>
    <ul>
      <li><strong>组件化设计</strong>：作为一个独立的UI组件，完全负责描述编辑对话框的显示和交互逻辑。</li>
      <li><strong>事件驱动</strong>：通过监听用户界面事件（如点击、输入）来响应用户操作。</li>
      <li><strong>懒加载签名预览</strong>：签名预览只在对话框打开时才生成和显示，减少不必要的资源消耗。</li>
      <li><strong>状态跟踪</strong>：通过比较当前描述与初始描述的差异，动态启用或禁用UI控件（如更新按钮）。</li>
      <li><strong>遥测整合</strong>：集成了遥测报告功能，记录用户是否修改了描述，用于改进产品。</li>
      <li><strong>无障碍支持</strong>：通过设置aria-label属性，确保签名预览对屏幕阅读器用户可访问。</li>
      <li><strong>资源清理</strong>：对话框关闭时会移除临时创建的DOM元素，重置引用，避免内存泄漏。</li>
    </ul>
  </div>

  <!-- 注意事项 -->
  <div id="notes">
    <h2>注意事项</h2>
    <p>使用EditDescriptionDialog时，需要注意以下几点：</p>
    <ul>
      <li>EditDescriptionDialog需要与OverlayManager配合使用，用于管理对话框的显示和隐藏。</li>
      <li>对话框打开时会临时禁用编辑器的编辑监听器，以避免干扰描述编辑操作。</li>
      <li>描述编辑后会自动更新签名编辑器的描述属性，但不会自动保存到PDF文档，需要用户显式保存文档。</li>
      <li>对话框中的签名预览是只读的，用户只能编辑描述文本，不能修改签名本身。</li>
      <li>清除按钮和更新按钮的启用状态会根据当前描述内容自动调整，以提供更好的用户体验。</li>
      <li>为了防止内存泄漏，在不再需要对话框时应确保调用close方法清理资源。</li>
      <li>对话框是通过overlayManager.register注册的，因此它遵循覆盖层管理器的生命周期和显示规则。</li>
    </ul>
  </div>

  <!-- 返回顶部按钮 -->
  <button class="back-to-top">↑</button>

  <script>
    // 创建目录
    createTableOfContents();
  </script>
</body>
</html> 