# PDF.js API

PDF.js核心API，用于PDF文档的加载、渲染和操作

## 模块介绍

PDF.js API是与PDF文档交互的核心接口集合，提供了加载PDF文件、访问文档内容、渲染页面和操作文档结构的能力。这套API被PDF.js的所有其他组件所依赖，是构建PDF查看器和处理PDF文件的基础。

PDF.js API可以分为几个主要部分：
- 文档加载与管理
- 页面渲染与操作
- 文档内容访问
- 工作线程管理
- 辅助工具与类型

## API文档

### 文档加载与管理

#### getDocument(params)

加载PDF文档并返回一个PDFDocumentLoadingTask对象

```javascript
const loadingTask = pdfjsLib.getDocument({
  url: 'document.pdf',
  cMapUrl: 'cmaps/',
  cMapPacked: true
});

loadingTask.promise.then(pdfDocument => {
  // 文档加载成功，pdfDocument是PDFDocumentProxy实例
}).catch(error => {
  // 处理加载错误
});
```

**参数：**

| 名称 | 类型 | 描述 |
|------|------|------|
| url | string | PDF文档的URL |
| data | TypedArray | 包含PDF数据的类型化数组 |
| cMapUrl | string | 字符映射表目录的URL |
| cMapPacked | boolean | 是否使用压缩的字符映射表 |
| worker | PDFWorker | 用于处理PDF的工作线程 |
| range | PDFDataRangeTransport | 用于PDF数据范围传输的对象 |
| withCredentials | boolean | 是否使用凭据进行跨域请求 |
| password | string | 用于加密文档的密码 |

#### PDFDocumentProxy

表示已加载的PDF文档，提供访问文档属性和页面的方法

**方法：**

- **getPage(pageNumber)** - 获取指定页码的页面对象
- **getPageIndex(ref)** - 根据引用获取页面索引
- **getPageLabels()** - 获取页面标签
- **getMetadata()** - 获取文档元数据
- **getDestinations()** - 获取文档中的目标位置
- **getAttachments()** - 获取文档附件
- **getJavaScript()** - 获取文档中的JavaScript
- **getOutline()** - 获取文档大纲
- **getPermissions()** - 获取文档权限
- **getDownloadInfo()** - 获取下载信息
- **getStats()** - 获取文档统计信息
- **getData()** - 获取文档数据
- **saveDocument()** - 保存文档

**属性：**

- **numPages** - 文档页数
- **fingerprints** - 文档指纹
- **annotationStorage** - 注释存储
- **isPureXfa** - 是否为纯XFA文档

### 页面渲染与操作

#### PDFPageProxy

表示PDF文档中的单个页面，提供渲染和获取页面内容的方法

**方法：**

- **getViewport(options)** - 获取页面视口
- **render(options)** - 渲染页面
- **getTextContent(options)** - 获取页面文本内容
- **getAnnotations(options)** - 获取页面注释
- **getOperatorList()** - 获取页面操作符列表
- **getJSActions()** - 获取页面JavaScript动作

**属性：**

- **pageNumber** - 页码
- **rotate** - 页面旋转角度
- **ref** - 页面引用
- **userUnit** - 用户单位
- **view** - 页面视图

#### TextLayer

用于在PDF页面上渲染文本层

**方法：**

- **render(options)** - 渲染文本层
- **hide()** - 隐藏文本层
- **show()** - 显示文本层

#### XfaLayer

用于在PDF页面上渲染XFA表单层

**方法：**

- **render(options)** - 渲染XFA层
- **update(options)** - 更新XFA层

### 工作线程管理

#### GlobalWorkerOptions

配置PDF.js工作线程的全局选项

**属性：**

- **workerSrc** - 工作线程脚本的URL
- **workerPort** - 与工作线程通信的端口

```javascript
pdfjsLib.GlobalWorkerOptions.workerSrc = 'pdf.worker.js';
```

#### PDFWorker

用于处理PDF数据的工作线程

**方法：**

- **constructor(options)** - 创建新的工作线程
- **destroy()** - 销毁工作线程

### 辅助工具与类型

#### PermissionFlag

表示PDF文档权限的常量

**属性：**

- **PRINT** - 打印权限
- **MODIFY_CONTENTS** - 修改内容权限
- **COPY** - 复制权限
- **MODIFY_ANNOTATIONS** - 修改注释权限
- **FILL_INTERACTIVE_FORMS** - 填写表单权限
- **COPY_FOR_ACCESSIBILITY** - 无障碍复制权限
- **ASSEMBLE** - 组装文档权限
- **PRINT_HIGH_QUALITY** - 高质量打印权限

#### InvalidPDFException

表示无效PDF文件的异常

#### PasswordResponses

密码响应常量

**属性：**

- **NEED_PASSWORD** - 需要密码
- **INCORRECT_PASSWORD** - 密码不正确

#### PDFDateString

用于解析和格式化PDF日期字符串

#### OutputScale

用于处理屏幕与PDF缩放比例的转换

## 执行流程

### PDF文档加载到渲染的执行顺序

PDF.js在viewer.mjs中的文档加载到渲染的完整流程如下：

1. **初始化**
   - 设置PDF.js工作线程路径: `GlobalWorkerOptions.workerSrc`
   - 配置应用参数: `AppOptions.set()`

2. **文档加载**
   - 创建加载任务: `getDocument()`
   - 设置回调函数: `onProgress`, `onPassword`
   - 加载文档: `loadingTask.promise`

3. **文档准备**
   - 获取文档属性: `pdfDocument.numPages`, `pdfDocument.getDownloadInfo()`
   - 获取页面布局: `pdfDocument.getPageLayout()`
   - 获取页面模式: `pdfDocument.getPageMode()`
   - 获取打开动作: `pdfDocument.getOpenAction()`

4. **视图初始化**
   - 设置页数: `toolbar.setPagesCount()`
   - 设置文档链接: `pdfLinkService.setDocument()`
   - 初始化查看器: `pdfViewer.setDocument()`
   - 初始化缩略图: `pdfThumbnailViewer.setDocument()`

5. **页面渲染**
   - 获取页面对象: `pdfDocument.getPage()`
   - 创建视口: `page.getViewport()`
   - 渲染页面: `page.render()`
   - 添加文本层: `TextLayer.render()`
   - 添加注释层: `AnnotationLayer.render()`

6. **完成与交互**
   - 发送渲染完成事件: `eventBus.dispatch("pagerendered")`
   - 隐藏加载UI: `loadingBar.hide()`
   - 发送文档加载完成事件: `eventBus.dispatch("documentloaded")`

整个流程是从创建加载任务开始，到文档完全加载并渲染到页面上结束。期间会有很多异步操作，包括文档加载、页面渲染和各种组件的初始化。

## 在前端框架中使用

### Vue2/Vue3中集成PDF.js

以下是在Vue项目中集成PDF.js，实现与viewer.mjs类似功能的步骤指南：

#### 1. 安装依赖

```
npm install pdfjs-dist
```

或者使用CDN直接引入：

```html
<script src="https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/build/pdf.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/build/pdf.worker.min.js"></script>
```

#### 2. 创建PDF查看器组件 (Vue3 Composition API)

```vue
<template>
  <div class="pdf-container">
    <div class="toolbar">
      <button @click="prevPage" :disabled="currentPage <= 1">上一页</button>
      <span>{{ currentPage }} / {{ totalPages }}</span>
      <button @click="nextPage" :disabled="currentPage >= totalPages">下一页</button>
      <select v-model="scale">
        <option value="0.5">50%</option>
        <option value="1">100%</option>
        <option value="1.5">150%</option>
        <option value="2">200%</option>
      </select>
    </div>
    <div class="pdf-viewer">
      <div v-for="page in visiblePages" :key="page" class="pdf-page-container">
        <canvas :id="'pdf-page-' + page" class="pdf-page"></canvas>
        <div :id="'pdf-text-layer-' + page" class="pdf-text-layer"></div>
        <div :id="'pdf-annotation-layer-' + page" class="pdf-annotation-layer"></div>
      </div>
      <div v-if="loading" class="loading-indicator">加载中...</div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue';
import * as pdfjsLib from 'pdfjs-dist';
import { TextLayerBuilder } from 'pdfjs-dist/web/pdf_viewer';

export default {
  name: 'PDFViewer',
  props: {
    pdfUrl: {
      type: String,
      required: true
    }
  },
  setup(props) {
    // 设置PDF.js工作线程路径
    pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/build/pdf.worker.min.js';

    // 状态管理
    const pdfDocument = ref(null);
    const currentPage = ref(1);
    const totalPages = ref(0);
    const scale = ref(1);
    const loading = ref(true);
    const visiblePages = ref([1]);
    const pageRendering = reactive({});
    
    // 加载PDF文档
    const loadPdf = async () => {
      try {
        loading.value = true;
        
        // 创建PDF加载任务
        const loadingTask = pdfjsLib.getDocument(props.pdfUrl);
        
        // 设置加载进度回调
        loadingTask.onProgress = ({ loaded, total }) => {
          console.log(`加载进度: ${Math.round(loaded / total * 100)}%`);
        };
        
        // 加载文档
        pdfDocument.value = await loadingTask.promise;
        totalPages.value = pdfDocument.value.numPages;
        
        // 渲染当前页
        renderPage(currentPage.value);
      } catch (error) {
        console.error('加载PDF错误:', error);
      } finally {
        loading.value = false;
      }
    };
    
    // 渲染页面
    const renderPage = async (pageNumber) => {
      if (pageRendering[pageNumber]) {
        return;
      }
      
      pageRendering[pageNumber] = true;
      
      try {
        // 获取页面
        const page = await pdfDocument.value.getPage(pageNumber);
        
        // 设置视口
        const viewport = page.getViewport({ scale: parseFloat(scale.value) });
        
        // 获取画布和上下文
        const canvas = document.getElementById(`pdf-page-${pageNumber}`);
        const context = canvas.getContext('2d');
        
        // 设置画布尺寸
        canvas.height = viewport.height;
        canvas.width = viewport.width;
        
        // 渲染页面
        const renderContext = {
          canvasContext: context,
          viewport: viewport
        };
        
        const renderTask = page.render(renderContext);
        
        // 渲染文本层
        const textLayerDiv = document.getElementById(`pdf-text-layer-${pageNumber}`);
        textLayerDiv.style.height = viewport.height + 'px';
        textLayerDiv.style.width = viewport.width + 'px';
        
        const textContent = await page.getTextContent();
        const textLayer = new TextLayerBuilder({
          textLayerDiv: textLayerDiv,
          pageIndex: page.pageNumber - 1,
          viewport: viewport
        });
        
        textLayer.setTextContent(textContent);
        textLayer.render();
        
        // 渲染注释层
        const annotationLayerDiv = document.getElementById(`pdf-annotation-layer-${pageNumber}`);
        annotationLayerDiv.style.height = viewport.height + 'px';
        annotationLayerDiv.style.width = viewport.width + 'px';
        
        const annotations = await page.getAnnotations();
        pdfjsLib.AnnotationLayer.render({
          viewport: viewport.clone({ dontFlip: true }),
          div: annotationLayerDiv,
          annotations: annotations,
          page: page
        });
        
        // 等待渲染完成
        await renderTask.promise;
        
        console.log(`页面 ${pageNumber} 渲染完成`);
      } catch (error) {
        console.error(`渲染页面 ${pageNumber} 错误:`, error);
      } finally {
        pageRendering[pageNumber] = false;
      }
    };
    
    // 页面导航
    const prevPage = () => {
      if (currentPage.value > 1) {
        currentPage.value--;
        updateVisiblePages();
      }
    };
    
    const nextPage = () => {
      if (currentPage.value < totalPages.value) {
        currentPage.value++;
        updateVisiblePages();
      }
    };
    
    // 更新可见页面
    const updateVisiblePages = () => {
      // 简单实现，只显示当前页
      visiblePages.value = [currentPage.value];
      renderPage(currentPage.value);
    };
    
    // 观察缩放变化
    watch(scale, () => {
      // 重新渲染当前可见页面
      visiblePages.value.forEach(pageNumber => {
        renderPage(pageNumber);
      });
    });
    
    // 生命周期钩子
    onMounted(() => {
      loadPdf();
    });
    
    onUnmounted(() => {
      // 清理资源
      if (pdfDocument.value) {
        pdfDocument.value.destroy();
      }
    });
    
    return {
      currentPage,
      totalPages,
      scale,
      loading,
      visiblePages,
      prevPage,
      nextPage
    };
  }
}
</script>

<style scoped>
.pdf-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.toolbar {
  padding: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
  background-color: #f5f5f5;
}

.pdf-viewer {
  flex: 1;
  overflow: auto;
  position: relative;
}

.pdf-page-container {
  position: relative;
  margin: 10px auto;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.pdf-page {
  display: block;
}

.pdf-text-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  opacity: 0.2;
  line-height: 1.0;
}

.pdf-text-layer > span {
  color: transparent;
  position: absolute;
  white-space: pre;
  cursor: text;
  transform-origin: 0% 0%;
}

.pdf-annotation-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.loading-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.8);
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}
</style>
```

#### 3. 在父组件中使用

```vue
<template>
  <div class="app">
    <h1>PDF查看器示例</h1>
    <PDFViewer :pdfUrl="pdfUrl" />
  </div>
</template>

<script>
import PDFViewer from './components/PDFViewer.vue';

export default {
  components: {
    PDFViewer
  },
  data() {
    return {
      pdfUrl: '/sample.pdf' // PDF文件URL
    };
  }
};
</script>

<style>
.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

h1 {
  margin: 0;
  padding: 16px;
}
</style>
```

#### 4. 高级功能实现

要实现更完整的功能，类似viewer.mjs，可以添加以下功能：

- **缩略图导航** - 创建单独的缩略图组件
- **大纲视图** - 使用`pdfDocument.getOutline()`获取文档大纲
- **文本搜索** - 实现类似PDFFindController的功能
- **虚拟滚动** - 只渲染视口中可见的页面，提高性能
- **注释工具** - 添加注释编辑功能
- **打印支持** - 实现PDF打印功能

#### 5. 最佳实践

- 使用Vuex/Pinia管理PDF状态，特别是对于大型应用
- 使用Web Worker处理PDF解析，避免阻塞主线程
- 实现虚拟滚动，只渲染可见页面，提高性能
- 添加错误处理和加载状态提示
- 缓存已渲染的页面，避免重复渲染
- 考虑使用一些现成的库如vue-pdf或vue-pdf-embed作为起点

## 使用示例

### 基本使用

```javascript
// 设置工作线程路径
pdfjsLib.GlobalWorkerOptions.workerSrc = 'pdf.worker.js';

// 加载PDF文档
const loadingTask = pdfjsLib.getDocument('document.pdf');

// 处理加载过程
loadingTask.onProgress = ({ loaded, total }) => {
  const percent = (loaded / total * 100).toFixed(2);
  console.log(`加载进度: ${percent}%`);
};

// 处理加载完成
loadingTask.promise.then(pdfDocument => {
  console.log(`PDF加载完成，共${pdfDocument.numPages}页`);
  
  // 获取第一页
  return pdfDocument.getPage(1);
}).then(pdfPage => {
  // 创建渲染上下文
  const viewport = pdfPage.getViewport({ scale: 1.5 });
  const canvas = document.getElementById('pdf-canvas');
  const context = canvas.getContext('2d');
  
  // 设置画布尺寸
  canvas.height = viewport.height;
  canvas.width = viewport.width;
  
  // 渲染页面
  const renderTask = pdfPage.render({
    canvasContext: context,
    viewport: viewport
  });
  
  return renderTask.promise;
}).catch(error => {
  console.error('PDF处理错误:', error);
});
```

### 文本提取

```javascript
// 获取页面后提取文本
pdfDocument.getPage(1).then(pdfPage => {
  return pdfPage.getTextContent();
}).then(textContent => {
  // 处理提取的文本内容
  const textItems = textContent.items;
  let text = '';
  
  for (const item of textItems) {
    text += item.str + ' ';
  }
  
  console.log('提取的文本:', text);
}).catch(error => {
  console.error('文本提取错误:', error);
});
```

### 离线使用

```javascript
// 预加载PDF数据
fetch('document.pdf')
  .then(response => response.arrayBuffer())
  .then(arrayBuffer => {
    // 使用ArrayBuffer加载PDF
    return pdfjsLib.getDocument({ data: new Uint8Array(arrayBuffer) }).promise;
  })
  .then(pdfDocument => {
    // 使用加载的文档
    console.log('PDF已加载，可以离线使用');
  });
```

## 实现细节

### 工作线程架构

PDF.js使用Web Worker架构来处理PDF解析和渲染，以避免阻塞主线程。工作线程负责处理PDF数据的解码、页面操作符解析等计算密集型任务，而主线程则负责UI渲染和用户交互。

这种架构确保了即使在处理大型或复杂的PDF文件时，用户界面仍然保持响应。

### 渲染管道

PDF.js的渲染过程包括多个步骤：

1. 解析PDF页面内容流，生成操作符列表
2. 解释操作符列表，创建页面视觉表示
3. 将页面内容渲染到Canvas或SVG
4. 根据需要添加文本层、注释层等

每个步骤都可以被单独优化和控制，提供了高度的灵活性。

### 缓存策略

PDF.js实现了多级缓存策略，以提高渲染性能：

- 页面对象缓存 - 避免重复解析同一页面
- 操作符列表缓存 - 存储已解析的页面指令
- 字形缓存 - 重用常用字体字形
- 图像缓存 - 存储解码后的图像数据

## 使用注意

### 内存管理

处理大型PDF文件时，应注意内存使用。以下策略可以帮助减少内存消耗：

- 使用`cleanup()`方法释放不再需要的页面资源
- 限制同时加载和渲染的页面数量
- 适当降低渲染比例以减少内存使用

### 跨域限制

从不同域加载PDF文件和工作线程时，需要考虑CORS（跨源资源共享）策略：

- 确保服务器配置了适当的CORS头
- 对跨域请求使用`withCredentials`选项
- 工作线程必须与主应用遵循相同的源策略限制

### 兼容性考虑

PDF.js依赖于多个现代Web技术，在使用时需要注意：

- 确保目标浏览器支持Promises、TypedArrays和Canvas
- 考虑为旧版浏览器提供polyfills
- 某些功能（如流式加载）在不同浏览器中可能有不同的性能特性 