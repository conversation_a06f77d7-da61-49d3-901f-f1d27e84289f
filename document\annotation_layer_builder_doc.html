<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AnnotationLayerBuilder 文档 - PDF.js</title>
  <link rel="stylesheet" href="doc_styles.css">
</head>
<body>
  <header>
    <h1>AnnotationLayerBuilder</h1>
    <p class="module-description">注释层构建器，负责创建和管理PDF注释层</p>
    <a href="index.html" class="back-link">返回模块列表</a>
  </header>

  <div class="content">
    <section class="module-intro">
      <h2>模块介绍</h2>
      <p>AnnotationLayerBuilder 是 PDF.js 中负责构建和管理 PDF 注释层的组件。它负责初始化、渲染和更新页面上的注释元素，包括链接、表单字段、注释等。该构建器作为 PDF 查看器与底层注释层实现之间的桥梁，处理注释层的生命周期和交互逻辑。</p>
      
      <div class="module-diagram">
        <h3>组件位置</h3>
        <div class="mermaid">
          graph TD
            PDFViewer[PDFViewer] --> PDFPageView[PDFPageView]
            PDFPageView --> AnnotationLayerBuilder[AnnotationLayerBuilder]
            AnnotationLayerBuilder --> AnnotationLayer[AnnotationLayer]
            AnnotationLayer --> AnnotationElement[AnnotationElement]
        </div>
      </div>
    </section>

    <section class="properties">
      <h2>属性</h2>
      
      <h3>公共属性</h3>
      <div class="property">
        <h4>div</h4>
        <p>包含注释层的 DOM 元素。初始为 null，在 render 方法调用后被设置。</p>
        <pre><code>// 获取注释层的 DOM 元素
const annotationLayerElement = annotationLayerBuilder.div;</code></pre>
      </div>

      <div class="property">
        <h4>annotationLayer</h4>
        <p>底层的 AnnotationLayer 实例。初始为 null，在初始化后被设置。</p>
        <pre><code>// 访问底层注释层
const layer = annotationLayerBuilder.annotationLayer;</code></pre>
      </div>

      <h3>私有属性</h3>
      <div class="property">
        <h4>pdfPage</h4>
        <p>关联的 PDF 页面对象，用于获取注释数据。</p>
      </div>

      <div class="property">
        <h4>linkService</h4>
        <p>链接服务，处理注释中的链接导航。</p>
      </div>

      <div class="property">
        <h4>downloadManager</h4>
        <p>下载管理器，处理文件附件的下载。</p>
      </div>

      <div class="property">
        <h4>annotationStorage</h4>
        <p>注释存储，保存注释的状态和修改。</p>
      </div>

      <div class="property">
        <h4>imageResourcesPath</h4>
        <p>注释图像资源的路径。</p>
      </div>

      <div class="property">
        <h4>renderForms</h4>
        <p>是否渲染表单字段。</p>
      </div>

      <div class="property">
        <h4>enableScripting</h4>
        <p>是否启用 JavaScript 脚本。</p>
      </div>

      <div class="property">
        <h4>_accessibilityManager</h4>
        <p>可访问性管理器，提供无障碍功能支持。</p>
      </div>

      <div class="property">
        <h4>_annotationCanvasMap</h4>
        <p>注释画布映射，用于渲染特定类型的注释。</p>
      </div>

      <div class="property">
        <h4>_annotationEditorUIManager</h4>
        <p>注释编辑器 UI 管理器，用于处理注释编辑。</p>
      </div>

      <div class="property">
        <h4>_cancelled</h4>
        <p>标记渲染过程是否被取消。</p>
      </div>

      <div class="property">
        <h4>_eventBus</h4>
        <p>事件总线，用于组件间通信。</p>
      </div>
    </section>

    <section class="methods">
      <h2>方法</h2>

      <div class="method">
        <h3>constructor(options)</h3>
        <p>创建 AnnotationLayerBuilder 实例。</p>
        <h4>参数：</h4>
        <ul>
          <li><code>options.pdfPage</code> - 关联的 PDF 页面</li>
          <li><code>options.linkService</code> - 链接服务</li>
          <li><code>options.downloadManager</code> - 下载管理器</li>
          <li><code>options.annotationStorage</code> - 注释存储（可选）</li>
          <li><code>options.imageResourcesPath</code> - 图像资源路径（可选）</li>
          <li><code>options.renderForms</code> - 是否渲染表单（可选）</li>
          <li><code>options.enableScripting</code> - 是否启用脚本（可选）</li>
          <li><code>options.hasJSActionsPromise</code> - 是否有 JS 操作的 Promise（可选）</li>
          <li><code>options.fieldObjectsPromise</code> - 字段对象的 Promise（可选）</li>
          <li><code>options.annotationCanvasMap</code> - 注释画布映射（可选）</li>
          <li><code>options.accessibilityManager</code> - 可访问性管理器（可选）</li>
          <li><code>options.annotationEditorUIManager</code> - 注释编辑器 UI 管理器（可选）</li>
          <li><code>options.onAppend</code> - 注释层添加到 DOM 时的回调（可选）</li>
        </ul>
        
        <pre><code>// 创建注释层构建器
const annotationLayerBuilder = new AnnotationLayerBuilder({
  pdfPage,
  linkService,
  downloadManager,
  renderForms: true,
  enableScripting: false,
});</code></pre>
      </div>

      <div class="method">
        <h3>render(viewport, intent = "display")</h3>
        <p>渲染注释层。</p>
        <h4>参数：</h4>
        <ul>
          <li><code>viewport</code> - 视口对象，定义页面的缩放和旋转</li>
          <li><code>intent</code> - 渲染意图，默认为 "display"</li>
        </ul>
        <h4>返回：</h4>
        <p>Promise，解析为渲染完成的状态</p>
        
        <pre><code>// 渲染注释层
annotationLayerBuilder.render(viewport).then(() => {
  console.log('注释层渲染完成');
});</code></pre>

        <div class="mermaid">
          sequenceDiagram
            participant PDFPageView
            participant AnnotationLayerBuilder
            participant AnnotationLayer
            participant AnnotationElement
            
            PDFPageView->>AnnotationLayerBuilder: render(viewport)
            AnnotationLayerBuilder->>AnnotationLayerBuilder: 创建 div（如果不存在）
            AnnotationLayerBuilder->>AnnotationLayerBuilder: #initAnnotationLayer(viewport)
            AnnotationLayerBuilder->>AnnotationLayer: new AnnotationLayer(...)
            AnnotationLayerBuilder->>PDFPageView: pdfPage.getAnnotations()
            PDFPageView-->>AnnotationLayerBuilder: annotations
            AnnotationLayerBuilder->>AnnotationLayer: render(...)
            AnnotationLayer->>AnnotationElement: 创建多个 AnnotationElement
            AnnotationLayer-->>AnnotationLayerBuilder: 渲染完成
        </div>
      </div>

      <div class="method">
        <h3>cancel()</h3>
        <p>取消正在进行的渲染过程。</p>
        <pre><code>// 取消渲染
annotationLayerBuilder.cancel();</code></pre>
      </div>

      <div class="method">
        <h3>hide()</h3>
        <p>隐藏注释层。</p>
        <pre><code>// 隐藏注释层
annotationLayerBuilder.hide();</code></pre>
      </div>

      <div class="method">
        <h3>show()</h3>
        <p>显示注释层。</p>
        <pre><code>// 显示注释层
annotationLayerBuilder.show();</code></pre>
      </div>

      <div class="method">
        <h3>#initAnnotationLayer(viewport, structTreeLayer)</h3>
        <p>初始化注释层的私有方法。</p>
        <h4>参数：</h4>
        <ul>
          <li><code>viewport</code> - 视口对象</li>
          <li><code>structTreeLayer</code> - 结构树层（可选）</li>
        </ul>
      </div>

      <div class="method">
        <h3>update(viewport, intent = "display")</h3>
        <p>更新注释层，调整其尺寸和位置以匹配新的视口。</p>
        <h4>参数：</h4>
        <ul>
          <li><code>viewport</code> - 新的视口对象</li>
          <li><code>intent</code> - 渲染意图，默认为 "display"</li>
        </ul>
        <pre><code>// 更新注释层
annotationLayerBuilder.update(newViewport);</code></pre>
      </div>
    </section>

    <section class="usage">
      <h2>使用示例</h2>

      <div class="example">
        <h3>基本用法</h3>
        <pre><code>// 创建注释层构建器
const annotationLayerBuilder = new AnnotationLayerBuilder({
  pdfPage,
  linkService,
  downloadManager,
  annotationStorage,
  imageResourcesPath: "resources/images/",
  renderForms: true,
  enableScripting: false,
});

// 渲染注释层
annotationLayerBuilder.render(viewport).then(() => {
  console.log("注释层渲染完成");
});

// 页面缩放或旋转时更新注释层
function onPageViewportChange(newViewport) {
  annotationLayerBuilder.update(newViewport);
}

// 隐藏/显示注释层
function toggleAnnotations(visible) {
  if (visible) {
    annotationLayerBuilder.show();
  } else {
    annotationLayerBuilder.hide();
  }
}</code></pre>
      </div>

      <div class="example">
        <h3>与 PDFPageView 结合使用</h3>
        <pre><code>// 在 PDFPageView 中使用 AnnotationLayerBuilder
class PDFPageView {
  constructor(options) {
    // ...其他初始化代码...
    
    this.annotationLayerBuilder = new AnnotationLayerBuilder({
      pdfPage: this.pdfPage,
      linkService: options.linkService,
      downloadManager: options.downloadManager,
      annotationStorage: options.annotationStorage,
      imageResourcesPath: options.imageResourcesPath,
      renderForms: options.renderForms,
      enableScripting: options.enableScripting,
      accessibilityManager: options.accessibilityManager,
    });
  }
  
  render() {
    // ...其他渲染代码...
    
    // 渲染注释层
    if (this.annotationLayerBuilder) {
      this._renderAnnotationLayer();
    }
  }
  
  _renderAnnotationLayer() {
    const viewport = this.viewport.clone({ dontFlip: true });
    this.annotationLayerBuilder.render(viewport).catch(reason => {
      console.error(`无法渲染注释层: ${reason}`);
    });
  }
}</code></pre>
      </div>
    </section>

    <section class="implementation-notes">
      <h2>实现说明</h2>
      <div class="note">
        <h3>注释层的生命周期</h3>
        <p>AnnotationLayerBuilder 管理注释层的整个生命周期，包括创建、渲染、更新和销毁。当页面被加载时，构建器会创建注释层并渲染注释；当页面缩放或旋转时，它会更新注释层的位置和尺寸；当页面被移除时，它负责清理相关资源。</p>
      </div>
      
      <div class="note">
        <h3>与其他组件的关系</h3>
        <p>AnnotationLayerBuilder 与多个 PDF.js 组件交互：</p>
        <ul>
          <li>从 PDFPage 获取注释数据</li>
          <li>使用 LinkService 处理注释中的链接</li>
          <li>通过 DownloadManager 处理附件下载</li>
          <li>利用 AnnotationStorage 保存注释状态</li>
          <li>通过 AccessibilityManager 提供无障碍支持</li>
          <li>与 AnnotationEditorUIManager 协作实现注释编辑功能</li>
        </ul>
      </div>
      
      <div class="note">
        <h3>注释类型</h3>
        <p>AnnotationLayerBuilder 支持多种注释类型，包括但不限于：</p>
        <ul>
          <li>链接注释 (Link)</li>
          <li>文本注释 (Text)</li>
          <li>表单字段 (Widget)</li>
          <li>线条和形状 (Line, Square, Circle)</li>
          <li>高亮和标记 (Highlight, Underline, StrikeOut)</li>
          <li>印章 (Stamp)</li>
          <li>文件附件 (FileAttachment)</li>
        </ul>
        <p>不同类型的注释由不同的 AnnotationElement 子类处理，AnnotationLayerBuilder 负责协调它们的创建和渲染。</p>
      </div>
    </section>

    <section class="best-practices">
      <h2>最佳实践</h2>
      <div class="practice">
        <h3>性能优化</h3>
        <p>注释层可能包含大量元素，尤其是在复杂的 PDF 文档中，建议：</p>
        <ul>
          <li>仅在可见页面上渲染注释层</li>
          <li>在页面滚动时考虑暂时隐藏注释层以提高滚动性能</li>
          <li>使用 <code>cancel()</code> 方法取消不再需要的渲染操作</li>
        </ul>
      </div>
      
      <div class="practice">
        <h3>错误处理</h3>
        <p>注释渲染过程可能因各种原因失败，如注释数据损坏或格式不支持。始终使用 Promise 的 catch 方法处理潜在错误：</p>
        <pre><code>annotationLayerBuilder.render(viewport).then(() => {
  console.log('渲染成功');
}).catch(error => {
  console.error('注释渲染失败:', error);
  // 实施适当的后备机制
});</code></pre>
      </div>
      
      <div class="practice">
        <h3>可访问性</h3>
        <p>为了确保 PDF 内容对所有用户可访问，请确保：</p>
        <ul>
          <li>始终提供 AccessibilityManager 以支持屏幕阅读器</li>
          <li>确保表单字段有适当的标签和描述</li>
          <li>为图像注释提供替代文本</li>
        </ul>
      </div>
    </section>
  </div>

  <script src="doc_script.js"></script>
  <script src="js/mermaid.js"></script>
  <script>
    mermaid.initialize({ startOnLoad: true, theme: 'neutral' });
  </script>
</body>
</html> 