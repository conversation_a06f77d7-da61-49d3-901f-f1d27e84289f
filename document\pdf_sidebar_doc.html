<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PDFSidebar - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#constants">常量</a>
    <a href="#properties">属性</a>
    <a href="#methods">方法</a>
    <a href="#flowcharts">流程图</a>
    <a href="#examples">示例</a>
    <a href="#notes">注意事项</a>
  </div>

  <h1>PDFSidebar 模块文档</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>PDFSidebar 是 PDF.js 库中负责管理缩略图、大纲和附件面板的组件。它提供了一个可折叠的侧边栏界面，用户可以通过它浏览文档的缩略图、大纲结构和附件。侧边栏支持多种视图模式切换，增强了 PDF 文档的导航体验。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 常量 -->
  <div id="constants">
    <h2>常量</h2>
    
    <p>PDFSidebar 使用以下常量：</p>
    
    <ul>
      <li>
        <code>SidebarView</code>: 侧边栏视图枚举
        <ul>
          <li><code>UNKNOWN</code>: 未知视图</li>
          <li><code>NONE</code>: 无视图（侧边栏关闭）</li>
          <li><code>THUMBS</code>: 缩略图视图</li>
          <li><code>OUTLINE</code>: 大纲视图</li>
          <li><code>ATTACHMENTS</code>: 附件视图</li>
          <li><code>LAYERS</code>: 图层视图</li>
        </ul>
      </li>
      <li>
        <code>UI_NOTIFICATION_CLASS</code>: UI 通知样式类名
      </li>
    </ul>
  </div>

  <!-- 属性 -->
  <div id="properties">
    <h2>属性</h2>
    
    <h3>公共属性</h3>
    <ul>
      <li><code>isOpen</code>: 侧边栏是否打开</li>
      <li><code>active</code>: 当前活动的视图类型</li>
      <li><code>isInitialViewSet</code>: 初始视图是否已设置</li>
      <li><code>isVisible</code>: 侧边栏是否可见</li>
    </ul>

    <h3>私有属性</h3>
    <ul>
      <li><code>#isInitialViewSet</code>: 内部初始视图设置标志</li>
      <li><code>#container</code>: 侧边栏容器元素</li>
      <li><code>#thumbnailButton</code>: 缩略图按钮元素</li>
      <li><code>#outlineButton</code>: 大纲按钮元素</li>
      <li><code>#attachmentsButton</code>: 附件按钮元素</li>
      <li><code>#layersButton</code>: 图层按钮元素</li>
      <li><code>#thumbnailView</code>: 缩略图视图容器</li>
      <li><code>#outlineView</code>: 大纲视图容器</li>
      <li><code>#attachmentsView</code>: 附件视图容器</li>
      <li><code>#layersView</code>: 图层视图容器</li>
      <li><code>#outlineOptionsContainer</code>: 大纲选项容器</li>
      <li><code>#currentOutlineItemButton</code>: 当前大纲项按钮</li>
      <li><code>#eventBus</code>: 事件总线</li>
      <li><code>#l10n</code>: 本地化对象</li>
      <li><code>#enableThumbnailSwipe</code>: 是否启用缩略图滑动</li>
      <li><code>#switchInProgress</code>: 切换是否进行中</li>
      <li><code>#tocButton</code>: 目录按钮元素</li>
      <li><code>#buttons</code>: 按钮元素集合</li>
      <li><code>#views</code>: 视图容器集合</li>
      <li><code>#active</code>: 当前活动视图内部表示</li>
    </ul>
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法</h2>
    
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">构造函数</h3>
      </div>
      <div class="method-content">
        <p>创建一个新的 PDFSidebar 实例。</p>
        <pre><code class="language-javascript">
constructor({
  elements,
  pdfViewer,
  pdfThumbnailViewer,
  eventBus,
  l10n,
  enableThumbnailSwipe = false,
})
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>elements</code>: 侧边栏相关的 DOM 元素</li>
          <li><code>pdfViewer</code>: PDF 查看器实例</li>
          <li><code>pdfThumbnailViewer</code>: PDF 缩略图查看器实例</li>
          <li><code>eventBus</code>: 事件总线</li>
          <li><code>l10n</code>: 本地化对象</li>
          <li><code>enableThumbnailSwipe</code>: 是否启用缩略图滑动 (可选，默认为 false)</li>
        </ul>
      </div>
    </div>

    <h3>核心方法</h3>
    <ul>
      <li><code>reset()</code>: 重置侧边栏状态</li>
      <li><code>open()</code>: 打开侧边栏</li>
      <li><code>close()</code>: 关闭侧边栏</li>
      <li><code>toggle()</code>: 切换侧边栏打开/关闭状态</li>
      <li><code>showLoadingIndicator()</code>: 显示加载指示器</li>
      <li><code>toggleLoadingIndicator(loading)</code>: 切换加载指示器显示状态</li>
      <li><code>switchView(view, forceOpen = false)</code>: 切换到指定视图</li>
      <li><code>updateThumbnailViewer()</code>: 更新缩略图查看器</li>
      <li><code>setInitialView(view = SidebarView.NONE)</code>: 设置初始视图</li>
      <li><code>isThumbnailViewVisible</code>: 缩略图视图是否可见</li>
      <li><code>setOutlineButtonLabel(label)</code>: 设置大纲按钮标签</li>
      <li><code>setOutlineActionButtonsVisibility(visibilityToggle, visibility)</code>: 设置大纲操作按钮可见性</li>
    </ul>
    
    <h3>私有方法</h3>
    <ul>
      <li><code>#isViewEnabled(view)</code>: 检查视图是否启用</li>
      <li><code>#addEventListeners()</code>: 添加事件监听器</li>
      <li><code>#removeEventListeners()</code>: 移除事件监听器</li>
      <li><code>#visibilityChanged()</code>: 处理可见性变化</li>
      <li><code>#sidebarResizerClick(evt)</code>: 处理侧边栏调整大小点击事件</li>
      <li><code>#updateArrow()</code>: 更新箭头状态</li>
      <li><code>#showUINotification(view)</code>: 显示 UI 通知</li>
      <li><code>#hideUINotification(view)</code>: 隐藏 UI 通知</li>
      <li><code>#removeUINotification(view)</code>: 移除 UI 通知</li>
      <li><code>#toggleOutlineButton(visibilityToggle, visibility)</code>: 切换大纲按钮</li>
      <li><code>#handleKeyDown(evt)</code>: 处理键盘按下事件</li>
      <li><code>#buttonsClick(evt)</code>: 处理按钮点击事件</li>
      <li><code>#checkToggleButton(view, button)</code>: 检查切换按钮状态</li>
    </ul>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <h3>侧边栏初始化流程</h3>
    <p>1. 创建 PDFSidebar 实例<br>
    2. 初始化 DOM 元素引用<br>
    3. 添加事件监听器<br>
    4. 设置初始视图<br>
    5. 绑定缩略图查看器</p>
    
    <div class="mermaid">
      graph TD
          A[创建 PDFSidebar 实例] --> B[初始化 DOM 元素引用]
          B --> C[设置按钮和视图映射]
          C --> D[添加事件监听器]
          D --> E[设置初始视图]
          E --> F[绑定缩略图查看器]
    </div>
    
    <h3>视图切换流程</h3>
    <p>1. 用户点击视图按钮<br>
    2. 调用 <code>switchView()</code> 方法<br>
    3. 检查视图是否已启用<br>
    4. 设置侧边栏打开状态<br>
    5. 隐藏所有视图<br>
    6. 显示选定的视图<br>
    7. 更新按钮状态</p>
    
    <div class="mermaid">
      graph TD
          A[用户点击视图按钮] --> B[调用 switchView 方法]
          B --> C{视图是否启用?}
          C -->|否| D[退出]
          C -->|是| E{是否强制打开?}
          E -->|是| F[打开侧边栏]
          E -->|否| G{是否需要关闭?}
          G -->|是| H[关闭侧边栏]
          G -->|否| I[隐藏所有视图]
          F --> I
          I --> J[显示选定视图]
          J --> K[更新按钮状态]
          H --> L[更新活动状态]
          K --> L
    </div>
    
    <h3>侧边栏打开/关闭流程</h3>
    <p>1. 用户触发侧边栏切换<br>
    2. 调用 <code>toggle()</code> 方法<br>
    3. 根据当前状态决定打开或关闭<br>
    4. 更新侧边栏样式<br>
    5. 分发侧边栏变化事件</p>
    
    <div class="mermaid">
      graph TD
          A[用户触发侧边栏切换] --> B[调用 toggle 方法]
          B --> C{侧边栏当前是否打开?}
          C -->|是| D[调用 close 方法]
          C -->|否| E[调用 open 方法]
          D --> F[设置关闭样式]
          E --> G[设置打开样式]
          F --> H[更新箭头状态]
          G --> H
          H --> I[分发 sidebarviewchanged 事件]
    </div>
  </div>

  <!-- 示例 -->
  <div id="examples">
    <h2>使用示例</h2>
    
    <h3>基本用法</h3>
    <pre><code class="language-javascript">
// 创建 PDFSidebar 实例
const sidebar = new PDFSidebar({
  elements: {
    // 必要的DOM元素
    outerContainer: document.getElementById('outerContainer'),
    sidebarContainer: document.getElementById('sidebarContainer'),
    toggleButton: document.getElementById('sidebarToggle'),
    thumbnailButton: document.getElementById('viewThumbnail'),
    outlineButton: document.getElementById('viewOutline'),
    attachmentsButton: document.getElementById('viewAttachments'),
    layersButton: document.getElementById('viewLayers'),
    thumbnailView: document.getElementById('thumbnailView'),
    outlineView: document.getElementById('outlineView'),
    attachmentsView: document.getElementById('attachmentsView'),
    layersView: document.getElementById('layersView'),
    outlineOptionsContainer: document.getElementById('outlineOptionsContainer'),
    currentOutlineItemButton: document.getElementById('currentOutlineItem')
  },
  pdfViewer: pdfViewer,
  pdfThumbnailViewer: pdfThumbnailViewer,
  eventBus: eventBus,
  l10n: l10n
});

// 设置初始视图（默认为缩略图）
sidebar.setInitialView(SidebarView.THUMBS);

// 监听侧边栏视图变化事件
eventBus.on('sidebarviewchanged', function(evt) {
  console.log('侧边栏视图变化为:', evt.view);
});
    </code></pre>
    
    <h3>侧边栏视图切换</h3>
    <pre><code class="language-javascript">
// 添加视图切换按钮事件处理
document.getElementById('thumbnailButton').addEventListener('click', function() {
  sidebar.switchView(SidebarView.THUMBS);
});

document.getElementById('outlineButton').addEventListener('click', function() {
  sidebar.switchView(SidebarView.OUTLINE);
});

document.getElementById('attachmentsButton').addEventListener('click', function() {
  sidebar.switchView(SidebarView.ATTACHMENTS);
});

document.getElementById('layersButton').addEventListener('click', function() {
  sidebar.switchView(SidebarView.LAYERS);
});

// 切换侧边栏可见性
document.getElementById('sidebarToggle').addEventListener('click', function() {
  sidebar.toggle();
});
    </code></pre>
    
    <h3>响应式侧边栏控制</h3>
    <pre><code class="language-javascript">
// 根据屏幕尺寸控制侧边栏
function handleScreenResize() {
  const isSmallScreen = window.innerWidth < 768;
  
  if (isSmallScreen && sidebar.isOpen) {
    // 在小屏幕上自动关闭侧边栏
    sidebar.close();
  } else if (!isSmallScreen && !sidebar.isOpen && sidebar.isInitialViewSet) {
    // 在大屏幕上自动打开侧边栏（如果已设置初始视图）
    sidebar.open();
  }
}

// 监听窗口大小变化
window.addEventListener('resize', handleScreenResize);

// 初始检查
handleScreenResize();

// 在PDF加载完成后打开到特定视图
eventBus.on('documentloaded', function() {
  // 如果文档有大纲，打开到大纲视图
  pdfDocument.getOutline().then(function(outline) {
    if (outline && outline.length > 0) {
      sidebar.switchView(SidebarView.OUTLINE, true); // 强制打开到大纲视图
    }
  });
});
    </code></pre>
  </div>

  <!-- 注意事项 -->
  <div id="notes">
    <h2>注意事项</h2>
    
    <ul>
      <li>侧边栏的 DOM 结构应与 PDFSidebar 组件期望的结构相匹配。确保提供正确的 DOM 元素引用以避免运行时错误。</li>
      <li>某些视图（如大纲、附件、图层）的可用性取决于 PDF 文档是否包含相应的数据。应处理文档不包含这些数据的情况。</li>
      <li>在移动设备上，侧边栏可能需要不同的行为（如默认关闭或悬浮显示），以优化小屏幕上的可用空间。</li>
      <li>当用户切换视图或打开/关闭侧边栏时，考虑调整主内容区域的大小，以确保最佳的阅读体验。</li>
      <li>侧边栏的大小可能会影响性能，特别是当显示大量缩略图时。考虑实现懒加载或虚拟滚动技术。</li>
      <li>侧边栏状态（打开/关闭、当前视图）可以保存在用户偏好中，以便在用户返回时恢复上次的状态。</li>
      <li>键盘导航和辅助功能支持对于侧边栏很重要，确保用户可以使用键盘切换和导航侧边栏的各个部分。</li>
      <li>主题切换（如暗模式）应考虑侧边栏的样式变化，确保侧边栏与整体 UI 主题协调一致。</li>
    </ul>
  </div>

  <script>
    // 在页面加载完成后初始化 Mermaid
    document.addEventListener('DOMContentLoaded', function() {
      mermaid.initialize({ startOnLoad: true });
      
      // 生成目录
      generateTOC();
    });
  </script>
</body>
</html> 