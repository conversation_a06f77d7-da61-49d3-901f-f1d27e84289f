<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PDFPrintServiceFactory - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#methods">方法列表</a>
    <a href="#flowcharts">流程图</a>
  </div>

  <h1>PDFPrintServiceFactory</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>PDFPrintServiceFactory是PDF.js中负责创建和管理PDF打印服务的工厂类。它作为PDF打印功能的入口点，提供了创建PDFPrintService实例的方法，并确保在任何时候只有一个活动的打印服务存在。该工厂类与浏览器的打印机制紧密集成，使PDF.js能够以高质量方式打印PDF文档内容。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法列表</h2>
    
    <!-- initGlobals方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">static initGlobals(app)</h3>
      </div>
      <div class="method-content">
        <p>初始化打印服务工厂所需的全局变量，设置对PDF查看器应用程序的引用。该方法在PDF.js初始化过程中被调用，确保打印服务可以访问必要的应用程序资源。</p>
        <pre><code class="language-javascript">
static initGlobals(app) { // 初始化全局变量的静态方法
  viewerApp = app; // 设置查看器应用程序引用
}
        </code></pre>
      </div>
    </div>
    
    <!-- supportsPrinting属性 -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">static get supportsPrinting()</h3>
      </div>
      <div class="method-content">
        <p>获取当前环境是否支持PDF打印功能。该属性使用shadow方法缓存结果，避免重复计算。在PDF.js初始化时，会检查此属性来决定是否启用打印相关功能。</p>
        <pre><code class="language-javascript">
static get supportsPrinting() { // 是否支持打印的getter
  return shadow(this, "supportsPrinting", true); // 使用shadow缓存返回true
}
        </code></pre>
      </div>
    </div>
    
    <!-- createPrintService方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">static createPrintService(params)</h3>
      </div>
      <div class="method-content">
        <p>创建一个新的PDF打印服务实例。该方法确保在任何时间点只有一个活动的打印服务，如果已经存在活动的打印服务，则会抛出错误。params参数包含创建打印服务所需的配置信息，如PDF文档对象、页面概览信息和打印容器等。</p>
        <pre><code class="language-javascript">
static createPrintService(params) { // 创建打印服务的静态方法
  if (activeService) { // 如果已有活动的打印服务
    throw new Error("The print service is created and active."); // 抛出错误
  }
  return activeService = new PDFPrintService(params); // 创建并返回新的打印服务实例
}
        </code></pre>
      </div>
    </div>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <!-- 打印服务创建流程图 -->
    <div class="mermaid">
      graph TD
        A["开始打印流程"] --> B{"检查是否已有<br>活动打印服务"}
        B -->|"是"| C["抛出错误:<br>打印服务已存在并激活"]
        B -->|"否"| D["调用PDFPrintServiceFactory.createPrintService(params)"]
        D --> E["创建PDFPrintService实例"]
        E --> F["设置activeService引用"]
        F --> G["返回打印服务实例"]
        G --> H["渲染PDF页面到打印容器"]
        H --> I["调用浏览器打印功能"]
        I --> J["打印完成后销毁打印服务"]
    </div>
    
    <!-- 打印服务工厂与其他组件关系图 -->
    <div class="mermaid">
      graph LR
        A["PDFViewerApplication"] -->|"初始化"| B["PDFPrintServiceFactory"]
        B -->|"创建"| C["PDFPrintService"]
        C -->|"渲染"| D["打印容器"]
        D -->|"显示"| E["浏览器打印对话框"]
        F["window.print()"] -->|"触发"| B
        G["Ctrl+P快捷键"] -->|"触发"| F
    </div>
  </div>

  <!-- 使用示例 -->
  <div id="usage-example">
    <h2>使用示例</h2>
    <p>以下是PDFPrintServiceFactory在PDF.js中的典型使用方式：</p>
    <pre><code class="language-javascript">
// 初始化打印服务工厂
PDFPrintServiceFactory.initGlobals(PDFViewerApplication);

// 检查是否支持打印
const supportsPrinting = PDFPrintServiceFactory.supportsPrinting;
if (!supportsPrinting) {
  console.warn("当前环境不支持PDF打印功能");
  return;
}

// 准备打印参数
const printParams = {
  pdfDocument: this.pdfDocument,
  pagesOverview: this.pagesOverview,
  printContainer: this.printContainer,
  printResolution: 150,
  printAnnotationStoragePromise: this.annotationStorage.getAll()
};

// 创建打印服务并启动打印流程
try {
  const printService = PDFPrintServiceFactory.createPrintService(printParams);
  printService.layout();
  printService.renderPages()
    .then(() => printService.performPrint())
    .catch(error => console.error("打印出错:", error))
    .finally(() => {
      if (printService.active) {
        printService.destroy();
      }
    });
} catch (error) {
  console.error("创建打印服务失败:", error);
}
    </code></pre>
  </div>

  <!-- 实现细节 -->
  <div id="implementation-details">
    <h2>实现细节</h2>
    <p>PDFPrintServiceFactory的实现采用了以下关键设计决策：</p>
    <ul>
      <li>使用<strong>单例模式</strong>确保在任何时间点只有一个活动的打印服务。这通过全局的activeService变量进行管理。</li>
      <li>采用<strong>工厂模式</strong>来创建PDFPrintService实例，封装了实例创建的复杂性。</li>
      <li>重写了<strong>window.print()</strong>方法，以便在用户请求打印时集成PDF.js的打印逻辑。</li>
      <li>使用<strong>shadow缓存技术</strong>优化supportsPrinting属性的性能，避免重复计算。</li>
      <li>与PDF.js的<strong>覆盖层管理系统</strong>集成，提供打印进度对话框。</li>
    </ul>
  </div>

  <!-- 注意事项 -->
  <div id="notes">
    <h2>注意事项</h2>
    <p>在使用PDFPrintServiceFactory时，需要注意以下几点：</p>
    <ul>
      <li>必须先调用<code>initGlobals()</code>方法设置应用程序引用，然后才能使用打印功能。</li>
      <li>打印服务一次只能有一个实例活动，尝试创建多个实例会引发错误。</li>
      <li>打印完成后应确保调用打印服务的<code>destroy()</code>方法释放资源。</li>
      <li>在某些浏览器环境中，打印功能可能受到限制或表现不一致，应进行充分测试。</li>
    </ul>
  </div>

  <!-- 返回顶部按钮 -->
  <button class="back-to-top">↑</button>

  <script>
    // 创建目录
    createTableOfContents();
  </script>
</body>
</html> 