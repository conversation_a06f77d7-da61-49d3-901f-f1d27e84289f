<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Native.js - 原生JavaScript翻页效果库文档</title>
  <link rel="stylesheet" href="../document/doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="../document/js/mermaid.js"></script>
  <script src="../document/doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="../document/index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#methods">方法列表</a>
    <a href="#flowcharts">流程图</a>
    <a href="#examples">示例</a>
  </div>

  <h1>Native.js - 原生JavaScript翻页效果库</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>Native.js 是一个完全基于原生JavaScript实现的翻页效果库，不依赖任何第三方库（如jQuery）。它提供了丰富的翻页动画效果，支持触摸和鼠标交互，适用于电子书、杂志、相册等应用场景。</p>

    <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ffc107;">
      <h4 style="margin-top: 0; color: #856404;">📦 版本说明</h4>
      <ul style="margin: 10px 0;">
        <li><strong>Native.js</strong> - 完整版本，包含所有原始功能（复杂，可能需要进一步调试）</li>
        <li><strong>Native_Simple.js</strong> - 简化版本，专注于核心翻页功能（推荐使用）</li>
      </ul>
      <p style="margin-bottom: 0;"><strong>建议：</strong>对于大多数应用场景，推荐使用 <code>Native_Simple.js</code>，它更稳定且易于使用。</p>
    </div>
    
    <h3>主要特性</h3>
    <ul>
      <li>✅ 完全原生JavaScript实现，无第三方依赖</li>
      <li>✅ 支持单页和双页显示模式</li>
      <li>✅ 支持触摸和鼠标事件</li>
      <li>✅ 支持CSS3硬件加速</li>
      <li>✅ 支持渐变效果</li>
      <li>✅ 支持自定义动画时长</li>
      <li>✅ 提供丰富的API接口</li>
      <li>✅ 支持模块化导入（CommonJS/AMD）</li>
    </ul>

    <h3>浏览器兼容性</h3>
    <ul>
      <li>Chrome 30+</li>
      <li>Firefox 25+</li>
      <li>Safari 7+</li>
      <li>Edge 12+</li>
      <li>IE 10+（部分功能）</li>
    </ul>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>核心API方法</h2>
    
    <!-- Turn 方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">element.turn(options)</h3>
      </div>
      <div class="method-content">
        <p>初始化翻页效果，将普通容器转换为可翻页的电子书。</p>
        <h4>参数说明</h4>
        <ul>
          <li><code>options</code> (Object): 配置选项
            <ul>
              <li><code>page</code> (Number): 初始页面，默认为1</li>
              <li><code>display</code> (String): 显示模式，'single'或'double'，默认'double'</li>
              <li><code>width</code> (Number): 容器宽度</li>
              <li><code>height</code> (Number): 容器高度</li>
              <li><code>gradients</code> (Boolean): 是否启用渐变效果，默认true</li>
              <li><code>duration</code> (Number): 动画持续时间（毫秒），默认600</li>
              <li><code>acceleration</code> (Boolean): 是否启用硬件加速，默认true</li>
            </ul>
          </li>
        </ul>
        <pre><code class="language-javascript">
// 基本初始化
var flipbook = document.getElementById('flipbook');
flipbook.turn({
    width: 800,
    height: 600,
    display: 'double',
    gradients: true,
    duration: 600
});
        </code></pre>
      </div>
    </div>

    <!-- Flip 方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">element.flip(options)</h3>
      </div>
      <div class="method-content">
        <p>为单个页面元素添加翻页折叠效果。</p>
        <h4>参数说明</h4>
        <ul>
          <li><code>options</code> (Object): 配置选项
            <ul>
              <li><code>corners</code> (String): 激活的角落，'forward'、'backward'或'all'</li>
              <li><code>cornerSize</code> (Number): 角落活动区域大小，默认100</li>
              <li><code>gradients</code> (Boolean): 是否启用渐变效果</li>
              <li><code>duration</code> (Number): 动画持续时间</li>
            </ul>
          </li>
        </ul>
        <pre><code class="language-javascript">
// 为页面添加翻页效果
var page = document.querySelector('.page');
page.flip({
    corners: 'forward',
    cornerSize: 100,
    gradients: true
});
        </code></pre>
      </div>
    </div>

    <!-- 页面控制方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">翻页控制方法</h3>
      </div>
      <div class="method-content">
        <p>提供程序化控制翻页的方法。</p>
        <pre><code class="language-javascript">
// 跳转到指定页面
flipbook.turn('page', 5);

// 获取当前页面
var currentPage = flipbook.turn('page');

// 下一页
flipbook.turn('next');

// 上一页
flipbook.turn('previous');

// 添加页面
flipbook.turn('addPage', pageElement, 3);

// 移除页面
flipbook.turn('removePage', 3);

// 停止所有动画
flipbook.turn('stop');
        </code></pre>
      </div>
    </div>

    <!-- 事件方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">事件处理</h3>
      </div>
      <div class="method-content">
        <p>支持多种事件回调，可以监听翻页过程中的各种状态。</p>
        <pre><code class="language-javascript">
// 初始化时绑定事件
flipbook.turn({
    when: {
        turning: function(event, page, view) {
            console.log('正在翻到第' + page + '页');
        },
        turned: function(event, page, view) {
            console.log('已翻到第' + page + '页');
        },
        start: function(event, pageObject, corner) {
            console.log('开始翻页');
        },
        end: function(event, pageObject) {
            console.log('翻页结束');
        }
    }
});

// 或者使用addEventListener
flipbook.addEventListener('turning', function(e) {
    console.log('翻页中...', e.detail);
});
        </code></pre>
      </div>
    </div>

    <!-- 工具方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">工具方法</h3>
      </div>
      <div class="method-content">
        <p>提供一些实用的工具方法。</p>
        <pre><code class="language-javascript">
// 检查是否正在动画
var isAnimating = flipbook.turn('animating');

// 获取当前视图
var view = flipbook.turn('view');

// 获取页面范围
var range = flipbook.turn('range');

// 检查是否有指定页面
var hasPage = flipbook.turn('hasPage', 5);

// 禁用/启用翻页
flipbook.turn('disable', true);  // 禁用
flipbook.turn('disable', false); // 启用

// 调整大小
flipbook.turn('size', 1000, 700);
        </code></pre>
      </div>
    </div>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>执行流程图</h2>
    
    <h3>初始化流程</h3>
    <div class="mermaid">
      graph TD
        A[调用 element.turn(options)] --> B{检查3D支持}
        B -->|支持| C[启用硬件加速]
        B -->|不支持| D[使用2D变换]
        C --> E[设置容器样式]
        D --> E
        E --> F[初始化数据结构]
        F --> G[绑定事件监听器]
        G --> H[添加现有页面]
        H --> I[设置初始页面]
        I --> J[初始化完成]
    </div>

    <h3>翻页交互流程</h3>
    <div class="mermaid">
      graph TD
        A[用户触摸/点击角落] --> B{检查角落是否激活}
        B -->|是| C[开始翻页动画]
        B -->|否| D[忽略事件]
        C --> E[计算折叠路径]
        E --> F[应用CSS变换]
        F --> G[更新渐变效果]
        G --> H{用户是否释放}
        H -->|是| I[完成翻页]
        H -->|否| J[继续跟随手势]
        J --> E
        I --> K[触发turned事件]
        K --> L[更新页面状态]
    </div>

    <h3>页面管理流程</h3>
    <div class="mermaid">
      graph TD
        A[页面操作请求] --> B{操作类型}
        B -->|添加页面| C[addPage流程]
        B -->|删除页面| D[removePage流程]
        B -->|跳转页面| E[page流程]
        
        C --> C1[验证页面编号]
        C1 --> C2[创建页面包装器]
        C2 --> C3[添加到DOM]
        C3 --> C4[初始化翻页效果]
        
        D --> D1[停止相关动画]
        D1 --> D2[从DOM移除]
        D2 --> D3[更新页面编号]
        D3 --> D4[重新计算范围]
        
        E --> E1[验证目标页面]
        E1 --> E2[计算翻页路径]
        E2 --> E3[执行翻页动画]
        E3 --> E4[更新当前页面]
    </div>
  </div>

  <!-- 基础示例 -->
  <div id="examples">
    <h2>基础示例</h2>

    <h3>简化版本使用（推荐）</h3>
    <pre><code class="language-html">
&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;head&gt;
    &lt;title&gt;简化版翻页书&lt;/title&gt;
    &lt;style&gt;
        #flipbook {
            margin: 50px auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        .page {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            cursor: pointer;
        }
    &lt;/style&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;div id="flipbook"&gt;
        &lt;div class="page"&gt;第1页&lt;/div&gt;
        &lt;div class="page"&gt;第2页&lt;/div&gt;
        &lt;div class="page"&gt;第3页&lt;/div&gt;
        &lt;div class="page"&gt;第4页&lt;/div&gt;
    &lt;/div&gt;

    &lt;script src="Native_Simple.js"&gt;&lt;/script&gt;
    &lt;script&gt;
        // 初始化翻页书
        var flipbook = document.getElementById('flipbook');
        flipbook.turn({
            width: 800,
            height: 600,
            display: 'double',
            duration: 600,
            when: {
                turned: function(event) {
                    var page = event.detail[0];
                    console.log('当前页面：' + page);
                }
            }
        });

        // 控制按钮
        function nextPage() {
            flipbook.turn('next');
        }

        function previousPage() {
            flipbook.turn('previous');
        }
    &lt;/script&gt;
&lt;/body&gt;
&lt;/html&gt;
    </code></pre>

    <h3>完整版本使用</h3>
    <pre><code class="language-html">
&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;head&gt;
    &lt;title&gt;翻页书示例&lt;/title&gt;
    &lt;style&gt;
        #flipbook {
            margin: 50px auto;
        }
        .page {
            background: white;
            border: 1px solid #ccc;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #333;
        }
    &lt;/style&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;div id="flipbook"&gt;
        &lt;div class="page"&gt;第1页&lt;/div&gt;
        &lt;div class="page"&gt;第2页&lt;/div&gt;
        &lt;div class="page"&gt;第3页&lt;/div&gt;
        &lt;div class="page"&gt;第4页&lt;/div&gt;
    &lt;/div&gt;

    &lt;script src="Native.js"&gt;&lt;/script&gt;
    &lt;script&gt;
        // 初始化翻页书
        var flipbook = document.getElementById('flipbook');
        flipbook.turn({
            width: 800,
            height: 600,
            display: 'double',
            gradients: true,
            duration: 600,
            when: {
                turned: function(event, page) {
                    console.log('当前页面：' + page);
                }
            }
        });

        // 添加控制按钮
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') {
                flipbook.turn('previous');
            } else if (e.key === 'ArrowRight') {
                flipbook.turn('next');
            }
        });
    &lt;/script&gt;
&lt;/body&gt;
&lt;/html&gt;
    </code></pre>

    <h3>动态添加页面</h3>
    <pre><code class="language-javascript">
// 动态创建页面
function createPage(content) {
    var page = document.createElement('div');
    page.className = 'page';
    page.innerHTML = content;
    return page;
}

// 添加新页面
var newPage = createPage('&lt;h2&gt;新页面&lt;/h2&gt;&lt;p&gt;这是动态添加的页面&lt;/p&gt;');
flipbook.turn('addPage', newPage);

// 在指定位置插入页面
var insertPage = createPage('&lt;h2&gt;插入页面&lt;/h2&gt;');
flipbook.turn('addPage', insertPage, 3); // 插入到第3页位置
    </code></pre>

    <h3>响应式设计</h3>
    <pre><code class="language-javascript">
// 响应式调整大小
function resizeFlipbook() {
    var container = document.getElementById('flipbook');
    var windowWidth = window.innerWidth;
    var windowHeight = window.innerHeight;
    
    var width = Math.min(windowWidth * 0.8, 800);
    var height = Math.min(windowHeight * 0.8, 600);
    
    container.turn('size', width, height);
}

// 监听窗口大小变化
window.addEventListener('resize', resizeFlipbook);

// 初始调整
resizeFlipbook();
    </code></pre>
  </div>

  <!-- 返回顶部按钮 -->
  <button class="back-to-top">↑</button>

  <script>
    // 创建目录
    createTableOfContents();
  </script>
</body>
</html>
