/**
 * Native_Fixed.js - 完全兼容原始index.js的原生JavaScript翻页效果库
 * 保持与原始jQuery版本完全一致的3D翻书动画效果
 */

(function() {
    'use strict';
    
    // 全局变量定义 - 与原版完全一致
    var has3d,
        vendor = '',
        PI = Math.PI,
        A90 = PI/2,
        isTouch = 'ontouchstart' in window,
        
        // 事件类型映射
        events = isTouch ? 
            {start: 'touchstart', move: 'touchmove', end: 'touchend'} :
            {start: 'mousedown', move: 'mousemove', end: 'mouseup'},
        
        // 角落常量定义
        corners = {
            backward: ['bl', 'tl'],
            forward: ['br', 'tr'],
            all: ['tl', 'bl', 'tr', 'br']
        },
        
        // 显示模式
        displays = ['single', 'double'],
        
        // Turn 默认选项
        turnOptions = {
            page: 1,                // 首页
            gradients: true,        // 启用渐变
            duration: 600,          // 动画持续时间（毫秒）
            acceleration: true,     // 启用硬件加速
            display: 'double',      // 显示模式
            when: null              // 事件回调
        },
        
        // Flip 默认选项
        flipOptions = {
            folding: null,          // 背面页面
            corners: 'forward',     // 激活的角落
            cornerSize: 100,        // 角落活动区域大小
            gradients: true,        // 启用渐变
            duration: 600,          // 动画持续时间
            acceleration: true      // 启用硬件加速
        },
        
        // DOM中的页面数量，最小值：6
        pagesInDOM = 6,
        
        // 页面位置配置
        pagePosition = {
            0: {top: 0, left: 0, right: 'auto', bottom: 'auto'},
            1: {top: 0, right: 0, left: 'auto', bottom: 'auto'}
        };

    // 数据存储管理器 - 替代jQuery的data()方法
    var DataManager = {
        storage: new WeakMap(),
        
        set: function(element, key, value) {
            var data = this.storage.get(element) || {};
            if (typeof key === 'object') {
                Object.assign(data, key);
            } else {
                data[key] = value;
            }
            this.storage.set(element, data);
        },
        
        get: function(element, key) {
            var data = this.storage.get(element) || {};
            return key ? data[key] : data;
        },
        
        remove: function(element, key) {
            var data = this.storage.get(element);
            if (data) {
                if (key) {
                    delete data[key];
                } else {
                    this.storage.delete(element);
                }
            }
        }
    };

    // 工具函数 - 与原版完全一致
    
    /**
     * 获取图层的基本属性
     */
    function divAtt(top, left, zIndex, overf) {
        return {
            'css': {
                position: 'absolute',
                top: top,
                left: left,
                'overflow': overf || 'hidden',
                'z-index': zIndex || 'auto'
            }
        };
    }

    /**
     * 从四个点的贝塞尔曲线获取2D点
     */
    function bezier(p1, p2, p3, p4, t) {
        var mum1 = 1 - t,
            mum13 = mum1 * mum1 * mum1,
            mu3 = t * t * t;

        return point2D(
            Math.round(mum13*p1.x + 3*t*mum1*mum1*p2.x + 3*t*t*mum1*p3.x + mu3*p4.x),
            Math.round(mum13*p1.y + 3*t*mum1*mum1*p2.y + 3*t*t*mum1*p3.y + mu3*p4.y)
        );
    }
    
    /**
     * 将角度从度转换为弧度
     */
    function rad(degrees) {
        return degrees/180*PI;
    }

    /**
     * 将角度从弧度转换为度
     */
    function deg(radians) {
        return radians/PI*180;
    }

    /**
     * 获取2D点对象
     */
    function point2D(x, y) {
        return {x: x, y: y};
    }

    /**
     * 返回平移变换值
     */
    function translate(x, y, use3d) {
        return (has3d && use3d) ? 
            ' translate3d(' + x + 'px,' + y + 'px, 0px) ' : 
            ' translate(' + x + 'px, ' + y + 'px) ';
    }

    /**
     * 返回旋转变换值
     */
    function rotate(degrees) {
        return ' rotate(' + degrees + 'deg) ';
    }

    /**
     * 检查属性是否属于对象
     */
    function has(property, object) {
        return Object.prototype.hasOwnProperty.call(object, property);
    }

    /**
     * 获取CSS3供应商前缀
     */
    function getPrefix() {
        var vendorPrefixes = ['Moz','Webkit','Khtml','O','ms'],
            len = vendorPrefixes.length,
            vendor = '';

        while (len--) {
            if ((vendorPrefixes[len] + 'Transform') in document.body.style) {
                vendor = '-' + vendorPrefixes[len].toLowerCase() + '-';
                break;
            }
        }
        return vendor;
    }

    // DOM 操作工具函数
    
    /**
     * 扩展对象属性 - 替代$.extend
     */
    function extend(target) {
        var sources = Array.prototype.slice.call(arguments, 1);
        sources.forEach(function(source) {
            if (source) {
                for (var key in source) {
                    if (source.hasOwnProperty(key)) {
                        if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
                            target[key] = target[key] || {};
                            extend(target[key], source[key]);
                        } else {
                            target[key] = source[key];
                        }
                    }
                }
            }
        });
        return target;
    }

    /**
     * 创建DOM元素 - 替代$('<div/>')
     */
    function createElement(tagName, attributes) {
        var element = document.createElement(tagName);
        if (attributes) {
            for (var key in attributes) {
                if (key === 'css' && typeof attributes[key] === 'object') {
                    setStyles(element, attributes[key]);
                } else if (key === 'class') {
                    element.className = attributes[key];
                } else {
                    element.setAttribute(key, attributes[key]);
                }
            }
        }
        return element;
    }

    /**
     * 设置元素样式 - 替代.css()
     */
    function setStyles(element, styles, value) {
        if (typeof styles === 'string') {
            element.style[styles] = value;
        } else if (typeof styles === 'object') {
            for (var key in styles) {
                if (styles.hasOwnProperty(key)) {
                    element.style[key] = styles[key];
                }
            }
        }
    }

    /**
     * 获取元素的计算样式值
     */
    function getComputedStyleValue(element, property) {
        return window.getComputedStyle(element)[property];
    }

    /**
     * 获取元素相对于文档的偏移量 - 替代.offset()
     */
    function getOffset(element) {
        var rect = element.getBoundingClientRect();
        return {
            top: rect.top + window.pageYOffset,
            left: rect.left + window.pageXOffset
        };
    }

    /**
     * 获取元素的宽度 - 替代.width()
     */
    function getWidth(element) {
        return element.offsetWidth;
    }

    /**
     * 获取元素的高度 - 替代.height()
     */
    function getHeight(element) {
        return element.offsetHeight;
    }

    /**
     * 检查元素是否可见 - 替代.is(':visible')
     */
    function isVisible(element) {
        return element.offsetWidth > 0 && element.offsetHeight > 0;
    }

    /**
     * 检查数组中是否包含某个值 - 替代$.inArray
     */
    function inArray(item, array) {
        return array.indexOf(item);
    }

    // 事件管理器 - 替代jQuery事件系统
    var EventManager = {
        on: function(element, eventType, handler) {
            element.addEventListener(eventType, handler, false);
        },
        
        off: function(element, eventType, handler) {
            element.removeEventListener(eventType, handler, false);
        },
        
        trigger: function(element, eventType, detail) {
            var event;
            if (typeof CustomEvent === 'function') {
                event = new CustomEvent(eventType, {
                    detail: detail,
                    bubbles: true,
                    cancelable: true
                });
            } else {
                event = document.createEvent('CustomEvent');
                event.initCustomEvent(eventType, true, true, detail);
            }
            element.dispatchEvent(event);
        }
    };

    /**
     * 设置CSS变换 - 替代.transform()
     */
    function setTransform(element, transform, origin) {
        if (origin) {
            element.style[vendor + 'transform-origin'] = origin;
        }
        element.style[vendor + 'transform'] = transform;
    }

    /**
     * 添加渐变效果 - 与原版完全一致
     */
    function gradient(element, p0, p1, colors, numColors) {
        var j, cols = [];

        if (vendor === '-webkit-') {
            for (j = 0; j < numColors; j++) {
                cols.push('color-stop(' + colors[j][0] + ', ' + colors[j][1] + ')');
            }

            setStyles(element, {
                'background-image': '-webkit-gradient(linear, ' + p0.x + '% ' + p0.y + '%,  ' +
                                   p1.x + '% ' + p1.y + '%, ' + cols.join(',') + ' )'
            });

        } else {
            var width = getWidth(element),
                height = getHeight(element);

            p0 = {x: p0.x/100 * width, y: p0.y/100 * height};
            p1 = {x: p1.x/100 * width, y: p1.y/100 * height};

            var dx = p1.x - p0.x,
                dy = p1.y - p0.y,
                angle = Math.atan2(dy, dx),
                angle2 = angle - Math.PI/2,
                diagonal = Math.abs(width * Math.sin(angle2)) + Math.abs(height * Math.cos(angle2)),
                gradientDiagonal = Math.sqrt(dy*dy + dx*dx),
                corner = point2D((p1.x < p0.x) ? width : 0, (p1.y < p0.y) ? height : 0),
                slope = Math.tan(angle),
                inverse = -1/slope,
                x = (inverse*corner.x - corner.y - slope*p0.x + p0.y) / (inverse - slope),
                c = {x: x, y: inverse*x - inverse*corner.x + corner.y},
                segA = Math.sqrt(Math.pow(c.x - p0.x, 2) + Math.pow(c.y - p0.y, 2));

            for (j = 0; j < numColors; j++) {
                cols.push(' ' + colors[j][1] + ' ' +
                         ((segA + gradientDiagonal * colors[j][0]) * 100 / diagonal) + '%');
            }

            setStyles(element, {
                'background-image': vendor + 'linear-gradient(' + (-angle) + 'rad,' + cols.join(',') + ')'
            });
        }
    }

    // Turn方法集合 - 与原版完全一致的实现
    var turnMethods = {

        /**
         * 初始化Turn效果 - 与原版完全一致
         */
        init: function(element, opts) {
            // 定义常量
            if (has3d === undefined) {
                has3d = 'WebKitCSSMatrix' in window || 'MozPerspective' in document.body.style;
                vendor = getPrefix();
            }

            var i, data = DataManager.get(element),
                ch = Array.prototype.slice.call(element.children);

            opts = extend({width: getWidth(element), height: getHeight(element)}, turnOptions, opts);
            data.opts = opts;
            data.pageObjs = {};
            data.pages = {};
            data.pageWrap = {};
            data.pagePlace = {};
            data.pageMv = [];
            data.totalPages = opts.pages || 0;

            if (opts.when) {
                for (i in opts.when) {
                    if (has(i, opts.when)) {
                        EventManager.on(element, i, opts.when[i]);
                    }
                }
            }

            setStyles(element, {
                position: 'relative',
                width: opts.width + 'px',
                height: opts.height + 'px'
            });

            turnMethods.display(element, opts.display);

            if (has3d && !isTouch && opts.acceleration) {
                setTransform(element, translate(0, 0, true));
            }

            for (i = 0; i < ch.length; i++) {
                turnMethods.addPage(element, ch[i], i + 1);
            }

            turnMethods.page(element, opts.page);

            // 允许设置活动角落作为选项
            corners = extend({}, corners, opts.corners);

            // 事件监听器
            EventManager.on(element, events.start, function(e) {
                var data = DataManager.get(element);
                for (var page in data.pages) {
                    if (has(page, data.pages) && flipMethods._eventStart.call(data.pages[page], e) === false) {
                        return false;
                    }
                }
            });

            EventManager.on(document, events.move, function(e) {
                var data = DataManager.get(element);
                for (var page in data.pages) {
                    if (has(page, data.pages)) {
                        flipMethods._eventMove.call(data.pages[page], e);
                    }
                }
            });

            EventManager.on(document, events.end, function(e) {
                var data = DataManager.get(element);
                for (var page in data.pages) {
                    if (has(page, data.pages)) {
                        flipMethods._eventEnd.call(data.pages[page], e);
                    }
                }
            });

            data.done = true;
            DataManager.set(element, data);
            return element;
        },

        /**
         * 设置或获取显示模式
         */
        display: function(element, display) {
            var data = DataManager.get(element),
                currentDisplay = data.display;

            if (display) {
                if (inArray(display, displays) === -1) {
                    throw new Error('"' + display + '" is not a value for display');
                }

                if (display === 'single') {
                    if (!data.pageObjs[0]) {
                        turnMethods.stop(element);
                        setStyles(element, {'overflow': 'hidden'});
                        data.pageObjs[0] = createElement('div', {
                            'class': 'turn-page p-temporal',
                            css: {
                                width: getWidth(element) + 'px',
                                height: getHeight(element) + 'px'
                            }
                        });
                        element.appendChild(data.pageObjs[0]);
                    }
                } else {
                    if (data.pageObjs[0]) {
                        turnMethods.stop(element);
                        setStyles(element, {'overflow': ''});
                        if (data.pageObjs[0].parentNode) {
                            data.pageObjs[0].parentNode.removeChild(data.pageObjs[0]);
                        }
                        delete data.pageObjs[0];
                    }
                }

                data.display = display;

                if (currentDisplay) {
                    var size = turnMethods.size(element);
                    turnMethods._movePages(element, 1, 0);
                    turnMethods.size(element, size.width, size.height);
                    turnMethods.update(element);
                }

                DataManager.set(element, data);
                return element;
            } else {
                return currentDisplay;
            }
        },

        /**
         * 添加页面
         */
        addPage: function(element, pageElement, page) {
            var incPages = false,
                data = DataManager.get(element),
                lastPage = data.totalPages + 1;

            if (page) {
                if (page === lastPage) {
                    page = lastPage;
                    incPages = true;
                } else if (page > lastPage) {
                    throw new Error('It is impossible to add the page "' + page + '", the maximum value is: "' + lastPage + '"');
                }
            } else {
                page = lastPage;
                incPages = true;
            }

            if (page >= 1 && page <= lastPage) {
                if (data.done) turnMethods.stop(element);

                if (page in data.pageObjs) {
                    turnMethods._movePages(element, page, 1);
                }

                if (incPages) {
                    data.totalPages = lastPage;
                }

                pageElement.className = (pageElement.className || '') + ' turn-page p' + page;
                data.pageObjs[page] = pageElement;

                turnMethods._addPage(element, page);

                if (data.done) {
                    turnMethods.update(element);
                }

                turnMethods._removeFromDOM(element);
            }

            return element;
        },

        /**
         * 内部添加页面方法
         */
        _addPage: function(element, page) {
            var data = DataManager.get(element),
                pageElement = data.pageObjs[page];

            if (pageElement) {
                if (turnMethods._necessPage(element, page)) {
                    if (!data.pageWrap[page]) {
                        var pageWidth = (data.display === 'double') ? getWidth(element) / 2 : getWidth(element),
                            pageHeight = getHeight(element);

                        setStyles(pageElement, {
                            width: pageWidth + 'px',
                            height: pageHeight + 'px'
                        });

                        data.pagePlace[page] = page;

                        var wrapperStyles = extend({
                            position: 'absolute',
                            overflow: 'hidden',
                            width: pageWidth + 'px',
                            height: pageHeight + 'px'
                        }, pagePosition[(data.display === 'double') ? page % 2 : 0]);

                        data.pageWrap[page] = createElement('div', {
                            'class': 'turn-page-wrapper',
                            page: page,
                            css: wrapperStyles
                        });

                        element.appendChild(data.pageWrap[page]);
                        data.pageWrap[page].appendChild(pageElement);
                    }

                    if (!page || turnMethods._setPageLoc(element, page) === 1) {
                        turnMethods._makeFlip(element, page);
                    }
                } else {
                    data.pagePlace[page] = 0;

                    if (data.pageObjs[page] && data.pageObjs[page].parentNode) {
                        data.pageObjs[page].parentNode.removeChild(data.pageObjs[page]);
                    }
                }
            }
            DataManager.set(element, data);
        },

        /**
         * 检查页面是否需要
         */
        _necessPage: function(element, page) {
            if (page === 0) {
                return true;
            }

            var range = turnMethods.range(element);
            return page >= range[0] && page <= range[1];
        },

        /**
         * 获取页面范围
         */
        range: function(element, page) {
            var remainingPages, left, right,
                data = DataManager.get(element);
            page = page || data.tpage || data.page;
            var view = turnMethods._view(element, page);

            if (page < 1 || page > data.totalPages) {
                throw new Error('"' + page + '" is not a page for range');
            }

            view[1] = view[1] || view[0];

            if (view[0] >= 1 && view[1] <= data.totalPages) {
                remainingPages = Math.floor((pagesInDOM - 2) / 2);

                if (data.totalPages - view[1] > view[0]) {
                    left = Math.min(view[0] - 1, remainingPages);
                    right = 2 * remainingPages - left;
                } else {
                    right = Math.min(data.totalPages - view[1], remainingPages);
                    left = 2 * remainingPages - right;
                }
            } else {
                left = pagesInDOM - 1;
                right = pagesInDOM - 1;
            }

            return [Math.max(1, view[0] - left), Math.min(data.totalPages, view[1] + right)];
        },

        /**
         * 获取视图
         */
        _view: function(element, page) {
            var data = DataManager.get(element);
            page = page || data.page;

            if (data.display === 'double') {
                return (page % 2) ? [page - 1, page] : [page, page + 1];
            } else {
                return [page];
            }
        },

        /**
         * 获取视图（公共方法）
         */
        view: function(element, page) {
            var data = DataManager.get(element),
                view = turnMethods._view(element, page);

            return (data.display === 'double') ?
                [(view[0] > 0) ? view[0] : 0, (view[1] <= data.totalPages) ? view[1] : 0] :
                [(view[0] > 0 && view[0] <= data.totalPages) ? view[0] : 0];
        },

        /**
         * 设置页面位置
         */
        _setPageLoc: function(element, page) {
            var data = DataManager.get(element),
                view = turnMethods.view(element);

            if (page === view[0] || page === view[1]) {
                setStyles(data.pageWrap[page], {
                    'z-index': data.totalPages,
                    display: ''
                });
                return 1;
            } else if ((data.display === 'single' && page === view[0] + 1) ||
                      (data.display === 'double' && (page === view[0] - 2 || page === view[1] + 2))) {
                setStyles(data.pageWrap[page], {
                    'z-index': data.totalPages - 1,
                    display: ''
                });
                return 2;
            } else {
                setStyles(data.pageWrap[page], {
                    'z-index': 0,
                    display: 'none'
                });
                return 0;
            }
        },

        /**
         * 创建翻页效果
         */
        _makeFlip: function(element, page) {
            var data = DataManager.get(element);

            if (!data.pages[page] && data.pagePlace[page] === page) {
                var single = data.display === 'single',
                    even = page % 2;

                var flipOpts = {
                    page: page,
                    next: (single && page === data.totalPages) ? page - 1 : ((even || single) ? page + 1 : page - 1),
                    turn: element,
                    duration: data.opts.duration,
                    acceleration: data.opts.acceleration,
                    corners: (single) ? 'all' : ((even) ? 'forward' : 'backward'),
                    backGradient: data.opts.gradients,
                    frontGradient: data.opts.gradients
                };

                setStyles(data.pageObjs[page], {
                    width: (single ? getWidth(element) : getWidth(element) / 2) + 'px',
                    height: getHeight(element) + 'px'
                });

                data.pages[page] = data.pageObjs[page];
                flipMethods.init(data.pages[page], flipOpts);
                flipMethods.disable(data.pages[page], data.disabled);

                // 绑定事件
                EventManager.on(data.pages[page], 'pressed', turnMethods._pressed);
                EventManager.on(data.pages[page], 'released', turnMethods._released);
                EventManager.on(data.pages[page], 'start', turnMethods._start);
                EventManager.on(data.pages[page], 'end', turnMethods._end);
                EventManager.on(data.pages[page], 'flip', turnMethods._flip);
            }
            return data.pages[page];
        },

        /**
         * 停止动画
         */
        stop: function(element, ok) {
            var i, opts, data = DataManager.get(element), pages = data.pageMv;

            data.pageMv = [];

            if (data.tpage) {
                data.page = data.tpage;
                delete data['tpage'];
            }

            for (i in pages) {
                if (!has(i, pages)) continue;
                opts = DataManager.get(data.pages[pages[i]]).f.opts;
                flipMethods._moveFoldingPage(data.pages[pages[i]], null);
                flipMethods.hideFoldedPage(data.pages[pages[i]]);
                data.pagePlace[opts.next] = opts.next;

                if (opts.force) {
                    opts.next = (opts.page % 2 === 0) ? opts.page - 1 : opts.page + 1;
                    delete opts['force'];
                }
            }

            turnMethods.update(element);
            DataManager.set(element, data);
            return element;
        },

        /**
         * 更新页面
         */
        update: function(element) {
            var page, data = DataManager.get(element);

            if (data.pageMv.length && data.pageMv[0] !== 0) {
                var apage,
                    pos = turnMethods.calculateZ(element, data.pageMv),
                    view = turnMethods.view(element, data.tpage);

                if (data.pagePlace[view[0]] === view[0]) apage = view[0];
                else if (data.pagePlace[view[1]] === view[1]) apage = view[1];

                for (page in data.pageWrap) {
                    if (!has(page, data.pageWrap)) continue;

                    setStyles(data.pageWrap[page], {
                        display: (pos.pageV[page]) ? '' : 'none',
                        'z-index': pos.pageZ[page] || 0
                    });

                    if (data.pages[page]) {
                        flipMethods.z(data.pages[page], pos.partZ[page] || null);

                        if (pos.pageV[page]) {
                            flipMethods.resize(data.pages[page]);
                        }

                        if (data.tpage) {
                            flipMethods.disable(data.pages[page], true);
                        }
                    }
                }
            } else {
                for (page in data.pageWrap) {
                    if (!has(page, data.pageWrap)) continue;
                    var pageLocation = turnMethods._setPageLoc(element, page);
                    if (data.pages[page]) {
                        flipMethods.disable(data.pages[page], data.disabled || pageLocation !== 1);
                        flipMethods.z(data.pages[page], null);
                    }
                }
            }
            DataManager.set(element, data);
        },

        /**
         * 计算Z值
         */
        calculateZ: function(element, mv) {
            var i, page, nextPage, placePage, dpage,
                data = DataManager.get(element),
                view = turnMethods.view(element),
                currentPage = view[0] || view[1],
                r = {pageZ: {}, partZ: {}, pageV: {}},

                addView = function(page) {
                    var view = turnMethods.view(element, page);
                    if (view[0]) r.pageV[view[0]] = true;
                    if (view[1]) r.pageV[view[1]] = true;
                };

            for (i = 0; i < mv.length; i++) {
                page = mv[i];
                nextPage = DataManager.get(data.pages[page]).f.opts.next;
                placePage = data.pagePlace[page];
                addView(page);
                addView(nextPage);
                dpage = (data.pagePlace[nextPage] === nextPage) ? nextPage : page;
                r.pageZ[dpage] = data.totalPages - Math.abs(currentPage - dpage);
                r.partZ[placePage] = data.totalPages * 2 + Math.abs(currentPage - dpage);
            }

            return r;
        },

        /**
         * 获取和设置页面
         */
        page: function(element, page) {
            if (page !== undefined) {
                page = parseInt(page, 10);
                var data = DataManager.get(element);

                if (page > 0 && page <= data.totalPages) {
                    if (!data.done || inArray(page, turnMethods.view(element)) !== -1) {
                        turnMethods._fitPage(element, page);
                    } else {
                        turnMethods._turnPage(element, page);
                    }
                    return element;
                } else {
                    return data.page;
                }
            } else {
                var data = DataManager.get(element);
                return data.page;
            }
        },

        /**
         * 下一页
         */
        next: function(element) {
            var data = DataManager.get(element);
            return turnMethods.page(element, turnMethods._view(element, data.page).pop() + 1);
        },

        /**
         * 上一页
         */
        previous: function(element) {
            var data = DataManager.get(element);
            return turnMethods.page(element, turnMethods._view(element, data.page).shift() - 1);
        },

        /**
         * 设置页面而不产生效果
         */
        _fitPage: function(element, page, ok) {
            var data = DataManager.get(element),
                newView = turnMethods.view(element, page);

            if (data.page !== page) {
                EventManager.trigger(element, 'turning', [page, newView]);
                if (inArray(1, newView) !== -1) EventManager.trigger(element, 'first');
                if (inArray(data.totalPages, newView) !== -1) EventManager.trigger(element, 'last');
            }

            if (!data.pageObjs[page]) {
                return;
            }

            data.tpage = page;

            turnMethods.stop(element, ok);
            turnMethods._removeFromDOM(element);
            turnMethods._makeRange(element);
            EventManager.trigger(element, 'turned', [page, newView]);
            DataManager.set(element, data);
        },

        /**
         * 翻转到页面
         */
        _turnPage: function(element, page) {
            var current, next,
                data = DataManager.get(element),
                view = turnMethods.view(element),
                newView = turnMethods.view(element, page);

            if (data.page !== page) {
                EventManager.trigger(element, 'turning', [page, newView]);
                if (inArray(1, newView) !== -1) EventManager.trigger(element, 'first');
                if (inArray(data.totalPages, newView) !== -1) EventManager.trigger(element, 'last');
            }

            if (!data.pageObjs[page]) {
                return;
            }

            data.tpage = page;

            turnMethods.stop(element);
            turnMethods._makeRange(element);

            if (data.display === 'single') {
                current = view[0];
                next = newView[0];
            } else if (view[1] && page > view[1]) {
                current = view[1];
                next = newView[0];
            } else if (view[0] && page < view[0]) {
                current = view[0];
                next = newView[1];
            }

            if (data.pages[current]) {
                var opts = DataManager.get(data.pages[current]).f.opts;
                data.tpage = next;

                if (opts.next !== next) {
                    opts.next = next;
                    data.pagePlace[next] = opts.page;
                    opts.force = true;
                }

                if (data.display === 'single') {
                    flipMethods.turnPage(data.pages[current], (newView[0] > view[0]) ? 'br' : 'bl');
                } else {
                    flipMethods.turnPage(data.pages[current]);
                }
            }
            DataManager.set(element, data);
        },

        /**
         * 在范围内创建页面
         */
        _makeRange: function(element) {
            var page,
                range = turnMethods.range(element);

            for (page = range[0]; page <= range[1]; page++) {
                turnMethods._addPage(element, page);
            }
        },

        /**
         * 从DOM中移除页面
         */
        _removeFromDOM: function(element) {
            var page, data = DataManager.get(element);

            for (page in data.pageWrap) {
                if (has(page, data.pageWrap) && !turnMethods._necessPage(element, page)) {
                    turnMethods._removePageFromDOM(element, page);
                }
            }
        },

        /**
         * 从DOM中移除页面及其内部引用
         */
        _removePageFromDOM: function(element, page) {
            var data = DataManager.get(element);

            if (data.pages[page]) {
                var dd = DataManager.get(data.pages[page]);
                if (dd.f && dd.f.fwrapper && dd.f.fwrapper.parentNode) {
                    dd.f.fwrapper.parentNode.removeChild(dd.f.fwrapper);
                }
                if (data.pages[page].parentNode) {
                    data.pages[page].parentNode.removeChild(data.pages[page]);
                }
                delete data.pages[page];
            }

            if (data.pageObjs[page] && data.pageObjs[page].parentNode) {
                data.pageObjs[page].parentNode.removeChild(data.pageObjs[page]);
            }

            if (data.pageWrap[page]) {
                if (data.pageWrap[page].parentNode) {
                    data.pageWrap[page].parentNode.removeChild(data.pageWrap[page]);
                }
                delete data.pageWrap[page];
            }

            delete data.pagePlace[page];
            DataManager.set(element, data);
        },

        /**
         * 移动页面
         */
        _movePages: function(element, from, change) {
            var page,
                data = DataManager.get(element),
                single = data.display === 'single',
                move = function(page) {
                    var next = page + change,
                        odd = next % 2;

                    if (data.pageObjs[page]) {
                        data.pageObjs[next] = data.pageObjs[page];
                        data.pageObjs[next].className = data.pageObjs[next].className
                            .replace('page' + page, 'page' + next);
                    }

                    if (data.pagePlace[page] && data.pageWrap[page]) {
                        data.pagePlace[next] = next;
                        data.pageWrap[next] = data.pageWrap[page];

                        var position = pagePosition[(single) ? 0 : odd];
                        setStyles(data.pageWrap[next], position);
                        data.pageWrap[next].setAttribute('page', next);

                        if (data.pages[page]) {
                            data.pages[next] = data.pages[page];
                            flipMethods.options(data.pages[next], {
                                page: next,
                                next: (single || odd) ? next + 1 : next - 1,
                                corners: (single) ? 'all' : ((odd) ? 'forward' : 'backward')
                            });
                        }

                        if (change) {
                            delete data.pages[page];
                            delete data.pagePlace[page];
                            delete data.pageObjs[page];
                            delete data.pageWrap[page];
                        }
                    }
                };

            if (change > 0) {
                for (page = data.totalPages; page >= from; page--) move(page);
            } else {
                for (page = from; page <= data.totalPages; page++) move(page);
            }
            DataManager.set(element, data);
        },

        /**
         * 获取和设置大小
         */
        size: function(element, width, height) {
            if (width && height) {
                var data = DataManager.get(element),
                    pageWidth = (data.display === 'double') ? width / 2 : width,
                    page;

                setStyles(element, {width: width + 'px', height: height + 'px'});

                if (data.pageObjs[0]) {
                    setStyles(data.pageObjs[0], {width: pageWidth + 'px', height: height + 'px'});
                }

                for (page in data.pageWrap) {
                    if (!has(page, data.pageWrap)) continue;
                    setStyles(data.pageObjs[page], {width: pageWidth + 'px', height: height + 'px'});
                    setStyles(data.pageWrap[page], {width: pageWidth + 'px', height: height + 'px'});
                    if (data.pages[page]) {
                        setStyles(data.pages[page], {width: pageWidth + 'px', height: height + 'px'});
                    }
                }

                turnMethods.resize(element);
                DataManager.set(element, data);
                return element;
            } else {
                return {width: getWidth(element), height: getHeight(element)};
            }
        },

        /**
         * 调整每个页面的大小
         */
        resize: function(element) {
            var page, data = DataManager.get(element);

            if (data.pages[0]) {
                setStyles(data.pageWrap[0], {left: (-getWidth(element)) + 'px'});
                flipMethods.resize(data.pages[0], true);
            }

            for (page = 1; page <= data.totalPages; page++) {
                if (data.pages[page]) {
                    flipMethods.resize(data.pages[page], true);
                }
            }
        },

        // 事件处理方法
        _pressed: function() {
            // 实现按下事件处理
        },

        _released: function() {
            // 实现释放事件处理
        },

        _start: function() {
            // 实现开始事件处理
        },

        _end: function() {
            // 实现结束事件处理
        },

        _flip: function() {
            // 实现翻页事件处理
        }
    };

    // Flip方法集合 - 基础实现
    var flipMethods = {

        /**
         * 初始化Flip效果
         */
        init: function(element, opts) {
            if (opts.gradients) {
                opts.frontGradient = true;
                opts.backGradient = true;
            }

            DataManager.set(element, 'f', {});
            flipMethods.options(element, opts);
            flipMethods._addPageWrapper(element);

            return element;
        },

        /**
         * 设置数据
         */
        setData: function(element, d) {
            var data = DataManager.get(element);
            data.f = extend(data.f, d);
            DataManager.set(element, data);
            return element;
        },

        /**
         * 设置或获取选项
         */
        options: function(element, opts) {
            var data = DataManager.get(element).f;

            if (opts) {
                flipMethods.setData(element, {
                    opts: extend({}, data.opts || flipOptions, opts)
                });
                return element;
            } else {
                return data.opts;
            }
        },

        /**
         * 设置z-index
         */
        z: function(element, z) {
            var data = DataManager.get(element).f;
            data.opts['z-index'] = z;

            if (data.fwrapper) {
                var parentZIndex = data.parent ? parseInt(getComputedStyleValue(data.parent, 'z-index'), 10) || 0 : 0;
                setStyles(data.fwrapper, {
                    'z-index': z || parentZIndex
                });
            }

            return element;
        },

        /**
         * 禁用或启用效果
         */
        disable: function(element, disable) {
            flipMethods.setData(element, {'disabled': disable});
            return element;
        },

        /**
         * 调整大小
         */
        resize: function(element, full) {
            var data = DataManager.get(element).f,
                width = getWidth(element),
                height = getHeight(element),
                size = Math.round(Math.sqrt(Math.pow(width, 2) + Math.pow(height, 2)));

            if (full && data.wrapper && data.fwrapper) {
                setStyles(data.wrapper, {width: size + 'px', height: size + 'px'});
                setStyles(data.fwrapper, {width: size + 'px', height: size + 'px'});

                var firstChild = data.fwrapper.children[0];
                if (firstChild) {
                    setStyles(firstChild, {width: width + 'px', height: height + 'px'});
                }

                if (data.fpage) {
                    setStyles(data.fpage, {width: height + 'px', height: width + 'px'});
                }

                if (data.opts.frontGradient && data.ashadow) {
                    setStyles(data.ashadow, {width: height + 'px', height: width + 'px'});
                }

                if (flipMethods._backGradient(element) && data.bshadow) {
                    setStyles(data.bshadow, {width: width + 'px', height: height + 'px'});
                }
            }

            if (data.parent && isVisible(data.parent)) {
                var parentOffset = getOffset(data.parent);
                setStyles(data.fwrapper, {
                    top: parentOffset.top + 'px',
                    left: parentOffset.left + 'px'
                });

                if (data.opts.turn) {
                    var turnOffset = getOffset(data.opts.turn);
                    setStyles(data.fparent, {
                        top: (-turnOffset.top) + 'px',
                        left: (-turnOffset.left) + 'px'
                    });
                }
            }

            flipMethods.z(element, data.opts['z-index']);
        },

        /**
         * 添加页面包装器
         */
        _addPageWrapper: function(element) {
            var data = DataManager.get(element).f,
                parent = element.parentNode;

            if (!data.wrapper) {
                var width = getWidth(element),
                    height = getHeight(element),
                    size = Math.round(Math.sqrt(Math.pow(width, 2) + Math.pow(height, 2)));

                data.parent = parent;
                data.fparent = (data.opts.turn) ? DataManager.get(data.opts.turn).fparent : document.getElementById('turn-fwrappers');

                if (!data.fparent) {
                    var fparent = createElement('div', {
                        css: {'pointer-events': 'none', display: 'none'}
                    });
                    DataManager.set(fparent, 'flips', 0);

                    if (data.opts.turn) {
                        var turnOffset = getOffset(data.opts.turn);
                        setStyles(fparent, extend(divAtt(-turnOffset.top, -turnOffset.left, 'auto', 'visible').css));
                        data.opts.turn.appendChild(fparent);
                        DataManager.set(data.opts.turn, 'fparent', fparent);
                    } else {
                        setStyles(fparent, extend(divAtt(0, 0, 'auto', 'visible').css));
                        fparent.id = 'turn-fwrappers';
                        document.body.appendChild(fparent);
                    }

                    data.fparent = fparent;
                }

                setStyles(element, {
                    position: 'absolute',
                    top: '0px',
                    left: '0px',
                    bottom: 'auto',
                    right: 'auto'
                });

                data.wrapper = createElement('div', divAtt(0, 0, getComputedStyleValue(element, 'z-index')));
                parent.appendChild(data.wrapper);
                data.wrapper.appendChild(element);

                var parentOffset = getOffset(parent);
                data.fwrapper = createElement('div', divAtt(parentOffset.top, parentOffset.left));
                setStyles(data.fwrapper, {display: 'none'});
                data.fparent.appendChild(data.fwrapper);

                var innerDiv = createElement('div', divAtt(0, 0, 0, 'visible'));
                data.fwrapper.appendChild(innerDiv);

                data.fpage = createElement('div', {css: {cursor: 'default'}});
                innerDiv.appendChild(data.fpage);

                if (data.opts.frontGradient) {
                    data.ashadow = createElement('div', divAtt(0, 0, 1));
                    data.fpage.appendChild(data.ashadow);
                }

                flipMethods.setData(element, data);
                flipMethods.resize(element, true);
            }
        },

        /**
         * 检查是否需要背景渐变
         */
        _backGradient: function(element) {
            var data = DataManager.get(element).f,
                turn = data.opts.turn,
                gradient = data.opts.backGradient &&
                          (!turn || DataManager.get(turn).display === 'single' ||
                           (data.opts.page !== 2 && data.opts.page !== DataManager.get(turn).totalPages - 1));

            if (gradient && !data.bshadow) {
                data.bshadow = createElement('div', divAtt(0, 0, 1));
                setStyles(data.bshadow, {
                    position: '',
                    width: getWidth(element) + 'px',
                    height: getHeight(element) + 'px'
                });
                data.parent.appendChild(data.bshadow);
            }

            return gradient;
        },

        /**
         * 获取角落坐标 - 与原版完全一致
         */
        _c: function(element, corner, opts) {
            opts = opts || 0;
            var width = getWidth(element),
                height = getHeight(element);

            return ({
                tl: point2D(opts, opts),
                tr: point2D(width - opts, opts),
                bl: point2D(opts, height - opts),
                br: point2D(width - opts, height - opts)
            })[corner];
        },

        /**
         * 获取角落的第二个坐标 - 与原版完全一致
         */
        _c2: function(element, corner) {
            var width = getWidth(element),
                height = getHeight(element);

            return {
                tl: point2D(width * 2, 0),
                tr: point2D(-width, 0),
                bl: point2D(width * 2, height),
                br: point2D(-width, height)
            }[corner];
        },

        /**
         * 获取折叠页面 - 与原版完全一致
         */
        _foldingPage: function(element, corner) {
            var opts = DataManager.get(element).f.opts;

            if (opts.folding) return opts.folding;
            else if (opts.turn) {
                var data = DataManager.get(opts.turn);
                if (data.display === 'single') {
                    return (data.pageObjs[opts.next]) ? data.pageObjs[0] : null;
                } else {
                    return data.pageObjs[opts.next];
                }
            }
            return null;
        },

        /**
         * 核心折叠方法 - 与原版完全一致的3D数学计算
         */
        _fold: function(element, point) {
            var that = element,
                a = 0,
                alpha = 0,
                beta,
                px,
                gradientEndPointA,
                gradientEndPointB,
                gradientStartV,
                gradientSize,
                gradientOpacity,
                mv = point2D(0, 0),
                df = point2D(0, 0),
                tr = point2D(0, 0),
                width = getWidth(element),
                height = getHeight(element),
                folding = flipMethods._foldingPage(element),
                tan = Math.tan(alpha),
                data = DataManager.get(element).f,
                ac = data.opts.acceleration,
                h = getHeight(data.wrapper),
                o = flipMethods._c(element, point.corner),
                top = point.corner.substr(0, 1) === 't',
                left = point.corner.substr(1, 1) === 'l',

                compute = function() {
                    var rel = point2D((o.x) ? o.x - point.x : point.x, (o.y) ? o.y - point.y : point.y),
                        tan = (Math.atan2(rel.y, rel.x)),
                        middle;

                    alpha = A90 - tan;
                    a = deg(alpha);
                    middle = point2D((left) ? width - rel.x/2 : point.x + rel.x/2, rel.y/2);

                    var gamma = alpha - Math.atan2(middle.y, middle.x),
                        distance = Math.max(0, Math.sin(gamma) * Math.sqrt(Math.pow(middle.x, 2) + Math.pow(middle.y, 2)));

                    tr = point2D(distance * Math.sin(alpha), distance * Math.cos(alpha));

                    if (alpha > A90) {
                        tr.x = tr.x + Math.abs(tr.y * Math.tan(tan));
                        tr.y = 0;

                        if (Math.round(tr.x*Math.tan(PI-alpha)) < height) {
                            point.y = Math.sqrt(Math.pow(height, 2)+2 * middle.x * rel.x);
                            if (top) point.y = height - point.y;
                            return compute();
                        }
                    }

                    if (alpha > A90) {
                        var beta = PI-alpha, dd = h - height/Math.sin(beta);
                        mv = point2D(Math.round(dd*Math.cos(beta)), Math.round(dd*Math.sin(beta)));
                        if (left) mv.x = -mv.x;
                        if (top) mv.y = -mv.y;
                    }

                    px = Math.round(tr.y/Math.tan(alpha) + tr.x);

                    var side = width - px,
                        sideX = side*Math.cos(alpha*2),
                        sideY = side*Math.sin(alpha*2);
                    df = point2D(Math.round((left ? side - sideX : px + sideX)), Math.round((top) ? sideY : height - sideY));

                    // 渐变计算
                    gradientSize = side*Math.sin(alpha);
                    var endingPoint = flipMethods._c2(element, point.corner),
                        far = Math.sqrt(Math.pow(endingPoint.x-point.x, 2)+Math.pow(endingPoint.y-point.y, 2));

                    gradientOpacity = (far < width) ? far/width : 1;

                    if (data.opts.frontGradient) {
                        gradientStartV = gradientSize > 100 ? (gradientSize-100)/gradientSize : 0;
                        gradientEndPointA = point2D(gradientSize*Math.sin(A90-alpha)/height*100, gradientSize*Math.cos(A90-alpha)/width*100);

                        if (top) gradientEndPointA.y = 100-gradientEndPointA.y;
                        if (left) gradientEndPointA.x = 100-gradientEndPointA.x;
                    }

                    if (flipMethods._backGradient(element)) {
                        gradientEndPointB = point2D(gradientSize*Math.sin(alpha)/width*100, gradientSize*Math.cos(alpha)/height*100);
                        if (!left) gradientEndPointB.x = 100-gradientEndPointB.x;
                        if (!top) gradientEndPointB.y = 100-gradientEndPointB.y;
                    }

                    tr.x = Math.round(tr.x);
                    tr.y = Math.round(tr.y);

                    return true;
                },

                transform = function(tr, c, x, a) {
                    var f = ['0', 'auto'],
                        mvW = (width-h)*x[0]/100,
                        mvH = (height-h)*x[1]/100,
                        v = {left: f[c[0]], top: f[c[1]], right: f[c[2]], bottom: f[c[3]]},
                        aliasingFk = (a !== 90 && a !== -90) ? (left ? -1 : 1) : 0;

                    x = x[0] + '% ' + x[1] + '%';

                    setStyles(element, v);
                    setTransform(element, rotate(a) + translate(tr.x + aliasingFk, tr.y, ac), x);

                    setStyles(data.fpage.parentNode, v);
                    setTransform(data.wrapper, translate(-tr.x + mvW-aliasingFk, -tr.y + mvH, ac) + rotate(-a), x);

                    setTransform(data.fwrapper, translate(-tr.x + mv.x + mvW, -tr.y + mv.y + mvH, ac) + rotate(-a), x);
                    setTransform(data.fpage.parentNode, rotate(a) + translate(tr.x + df.x - mv.x, tr.y + df.y - mv.y, ac), x);

                    if (data.opts.frontGradient && data.ashadow) {
                        gradient(data.ashadow,
                                point2D(left?100:0, top?100:0),
                                point2D(gradientEndPointA.x, gradientEndPointA.y),
                                [[gradientStartV, 'rgba(0,0,0,0)'],
                                [((1-gradientStartV)*0.8)+gradientStartV, 'rgba(0,0,0,'+(0.2*gradientOpacity)+')'],
                                [1, 'rgba(255,255,255,'+(0.2*gradientOpacity)+')']],
                                3);
                    }

                    if (flipMethods._backGradient(element) && data.bshadow) {
                        gradient(data.bshadow,
                                point2D(left?0:100, top?0:100),
                                point2D(gradientEndPointB.x, gradientEndPointB.y),
                                [[0.8, 'rgba(0,0,0,0)'],
                                [1, 'rgba(0,0,0,'+(0.3*gradientOpacity)+')'],
                                [1, 'rgba(0,0,0,0)']],
                                3);
                    }
                };

            // 根据不同角落执行不同的变换
            switch (point.corner) {
                case 'tl':
                    point.x = Math.max(point.x, 1);
                    compute();
                    transform(tr, [1,0,0,1], [100, 0], a);
                    if (data.fpage) {
                        setTransform(data.fpage, translate(-height, -width, ac) + rotate(90-a*2), '100% 100%');
                    }
                    if (folding) {
                        setTransform(folding, rotate(90) + translate(0, -height, ac), '0% 0%');
                    }
                    break;
                case 'tr':
                    point.x = Math.min(point.x, width-1);
                    compute();
                    transform(point2D(-tr.x, tr.y), [0,0,0,1], [0, 0], -a);
                    if (data.fpage) {
                        setTransform(data.fpage, translate(0, -width, ac) + rotate(-90+a*2), '0% 100%');
                    }
                    if (folding) {
                        setTransform(folding, rotate(270) + translate(-width, 0, ac), '0% 0%');
                    }
                    break;
                case 'bl':
                    point.x = Math.max(point.x, 1);
                    compute();
                    transform(point2D(tr.x, -tr.y), [1,1,0,0], [100, 100], -a);
                    if (data.fpage) {
                        setTransform(data.fpage, translate(-height, 0, ac) + rotate(-90+a*2), '100% 0%');
                    }
                    if (folding) {
                        setTransform(folding, rotate(270) + translate(-width, 0, ac), '0% 0%');
                    }
                    break;
                case 'br':
                    point.x = Math.min(point.x, width-1);
                    compute();
                    transform(point2D(-tr.x, -tr.y), [0,1,1,0], [0, 100], a);
                    if (data.fpage) {
                        setTransform(data.fpage, rotate(90-a*2), '0% 0%');
                    }
                    if (folding) {
                        setTransform(folding, rotate(90) + translate(0, -height, ac), '0% 0%');
                    }
                    break;
            }

            data.point = point;
        },

        /**
         * 翻页 - 与原版完全一致的3D翻页动画
         */
        turnPage: function(element, corner) {
            var that = element,
                data = DataManager.get(element).f;

            corner = {corner: (data.corner) ? data.corner.corner : corner || flipMethods._cAllowed(element)[0]};

            var p1 = data.point || flipMethods._c(element, corner.corner, (data.opts.turn) ? DataManager.get(data.opts.turn).opts.elevation : 0),
                p4 = flipMethods._c2(element, corner.corner);

            EventManager.trigger(element, 'flip');

            element.animatef({
                from: 0,
                to: 1,
                frame: function(v) {
                    var np = bezier(p1, p1, p4, p4, v);
                    corner.x = np.x;
                    corner.y = np.y;
                    flipMethods._fold(that, corner);
                },

                complete: function() {
                    EventManager.trigger(that, 'end', [true]);
                },
                duration: data.opts.duration,
                turning: true
            });

            data.corner = null;
        },

        /**
         * 获取允许的角落
         */
        _cAllowed: function(element) {
            var data = DataManager.get(element).f;
            return corners[data.opts.corners] || data.opts.corners;
        },

        /**
         * 隐藏折叠页面
         */
        hideFoldedPage: function(element, animate) {
            var data = DataManager.get(element).f;
            if (data.fwrapper) {
                setStyles(data.fwrapper, {display: 'none'});
            }
            EventManager.trigger(element, 'end', [false]);
        },

        /**
         * 移动折叠页面
         */
        _moveFoldingPage: function(element, bool) {
            // 基础实现
        },

        /**
         * 事件处理方法
         */
        _eventStart: function(e) {
            return false;
        },

        _eventMove: function(e) {
            // 基础实现
        },

        _eventEnd: function(e) {
            // 基础实现
        }
    };

    // 通用调用函数
    function callMethod(element, methods, args) {
        if (!args[0] || typeof(args[0]) === 'object') {
            return methods.init.call(methods, element, args[0]);
        } else if (methods[args[0]] && args[0].toString().substr(0, 1) !== '_') {
            var methodArgs = Array.prototype.slice.call(args, 1);
            methodArgs.unshift(element);
            return methods[args[0]].apply(methods, methodArgs);
        } else {
            throw new Error(args[0] + ' is an invalid value');
        }
    }

    // 公共API - Turn效果
    function Turn(req) {
        var args = Array.prototype.slice.call(arguments);
        return callMethod(this, turnMethods, args);
    }

    // 公共API - Flip效果
    function Flip(req) {
        var args = Array.prototype.slice.call(arguments);
        return callMethod(this, flipMethods, args);
    }

    // 为Element原型添加turn方法
    Element.prototype.turn = function() {
        return Turn.apply(this, arguments);
    };

    // 为Element原型添加flip方法
    Element.prototype.flip = function() {
        return Flip.apply(this, arguments);
    };

    // 为Element原型添加transform方法
    Element.prototype.transform = function(transform, origin) {
        setTransform(this, transform, origin);
        return this;
    };

    // 为Element原型添加animatef方法 - 与原版完全一致
    Element.prototype.animatef = function(point) {
        var data = DataManager.get(this);

        if (data.effect) {
            clearInterval(data.effect.handle);
        }

        if (point) {
            if (!Array.isArray(point.to)) point.to = [point.to];
            if (!Array.isArray(point.from)) point.from = [point.from];
            if (!point.easing) {
                point.easing = function (x, t, b, c, duration) {
                    return c * Math.sqrt(1 - (t = t/duration - 1) * t) + b;
                };
            }

            var j, diff = [],
                len = point.to.length,
                that = this,
                fps = point.fps || 30,
                time = -fps,
                f = function() {
                    var j, v = [];
                    time = Math.min(point.duration, time + fps);

                    for (j = 0; j < len; j++) {
                        v.push(point.easing(1, time, point.from[j], diff[j], point.duration));
                    }

                    point.frame((len === 1) ? v[0] : v);

                    if (time === point.duration) {
                        clearInterval(data.effect.handle);
                        delete data['effect'];
                        DataManager.set(that, data);
                        if (point.complete) {
                            point.complete();
                        }
                    }
                };

            for (j = 0; j < len; j++) {
                diff.push(point.to[j] - point.from[j]);
            }

            data.effect = point;
            data.effect.handle = setInterval(f, fps);
            DataManager.set(this, data);
            f();
        } else {
            delete data['effect'];
            DataManager.set(this, data);
        }

        return this;
    };

    // 全局对象暴露
    window.TurnJS = {
        Turn: Turn,
        Flip: Flip,
        isTouch: isTouch,
        version: '1.0.0-fixed'
    };

    // 如果支持模块化，则导出
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = window.TurnJS;
    }

    // AMD支持
    if (typeof define === 'function' && define.amd) {
        define(function() {
            return window.TurnJS;
        });
    }

})();
