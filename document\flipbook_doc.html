<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Flipbook - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#constants">常量</a>
    <a href="#options">选项</a>
    <a href="#methods">方法</a>
    <a href="#flowcharts">流程图</a>
    <a href="#examples">示例</a>
  </div>

  <h1>Flipbook 模块文档</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>Flipbook 是一个基于 jQuery 的 PDF 电子书翻页效果库，它提供了类似于实体书籍的翻页动画效果。该模块支持单页和双页模式，可以实现拖拽翻页、点击翻页等多种交互方式，并且提供了丰富的 API 接口。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 常量 -->
  <div id="constants">
    <h2>常量</h2>
    
    <p>Flipbook 使用以下常量：</p>
    
    <ul>
      <li><code>PI</code>: Math.PI，数学常量 π</li>
      <li><code>A90</code>: PI/2，90度对应的弧度值</li>
      <li><code>isTouch</code>: 判断是否为触摸设备</li>
      <li>
        <code>events</code>: 根据设备类型定义的事件映射
        <ul>
          <li>触摸设备: <code>{start: 'touchstart', move: 'touchmove', end: 'touchend'}</code></li>
          <li>非触摸设备: <code>{start: 'mousedown', move: 'mousemove', end: 'mouseup'}</code></li>
        </ul>
      </li>
      <li>
        <code>corners</code>: 定义翻页时的角落位置
        <ul>
          <li><code>backward</code>: ['bl', 'tl'] - 向后翻页（左上角和左下角）</li>
          <li><code>forward</code>: ['br', 'tr'] - 向前翻页（右上角和右下角）</li>
          <li><code>all</code>: ['tl', 'bl', 'tr', 'br'] - 所有角落</li>
        </ul>
      </li>
      <li>
        <code>displays</code>: 显示模式
        <ul>
          <li><code>'single'</code>: 单页模式</li>
          <li><code>'double'</code>: 双页模式</li>
        </ul>
      </li>
      <li><code>has3d</code>: 是否支持 CSS3 3D 变换</li>
      <li><code>vendor</code>: CSS 供应商前缀</li>
      <li><code>pagesInDOM</code>: DOM 中保留的页面数量，最小值为 6</li>
    </ul>
  </div>

  <!-- 选项 -->
  <div id="options">
    <h2>选项参数</h2>
    
    <h3>翻书效果选项 (turnOptions)</h3>
    <ul>
      <li><code>page</code>: 初始页码，默认值为 1</li>
      <li><code>gradients</code>: 是否启用渐变效果，默认为 true</li>
      <li><code>duration</code>: 翻页动画持续时间（毫秒），默认为 600</li>
      <li><code>acceleration</code>: 是否启用硬件加速，默认为 true</li>
      <li><code>display</code>: 显示模式，可选 'single' 或 'double'，默认为 'double'</li>
      <li><code>when</code>: 事件回调函数集，默认为 null</li>
    </ul>

    <h3>翻页效果选项 (flipOptions)</h3>
    <ul>
      <li><code>folding</code>: 折叠的页面，默认为 null</li>
      <li><code>corners</code>: 激活的角落，可选 'forward'、'backward' 或 'all'，默认为 'forward'</li>
      <li><code>cornerSize</code>: 角落激活区域大小，默认为 100</li>
      <li><code>gradients</code>: 是否启用渐变效果，默认为 true</li>
      <li><code>duration</code>: 翻页动画持续时间（毫秒），默认为 600</li>
      <li><code>acceleration</code>: 是否启用硬件加速，默认为 true</li>
    </ul>
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法列表</h2>
    
    <h3>核心方法（jQuery 插件方法）</h3>
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">turn(options)</h3>
      </div>
      <div class="method-content">
        <p>创建或控制翻书效果的主要方法。</p>
        <pre><code class="language-javascript">
// 初始化翻书效果
$('#selector').turn({
  width: 800,
  height: 600,
  display: 'double',
  acceleration: true,
  pages: 20
});

// 跳转到指定页面
$('#selector').turn('page', 5);

// 获取总页数
var totalPages = $('#selector').turn('pages');
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>options</code>: 初始化选项或命令字符串
            <ul>
              <li>作为对象时：初始化选项，如 <code>{width: 800, height: 600}</code></li>
              <li>作为字符串时：执行命令，如 'page', 'next', 'previous'</li>
            </ul>
          </li>
        </ul>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">flip(options)</h3>
      </div>
      <div class="method-content">
        <p>控制单个页面翻转效果的方法。</p>
        <pre><code class="language-javascript">
// 初始化翻页效果
$('.page').flip({
  cornerSize: 50,
  duration: 500
});

// 禁用翻页效果
$('.page').flip('disable', true);
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>options</code>: 初始化选项或命令字符串</li>
        </ul>
      </div>
    </div>

    <h3>TurnMethods - 翻书控制方法</h3>
    <ul>
      <li><code>init(opts)</code>: 初始化翻书效果</li>
      <li><code>addPage(element, page)</code>: 添加页面</li>
      <li><code>_addPage(page)</code>: 从内部数据添加页面</li>
      <li><code>hasPage(page)</code>: 检查页面是否存在</li>
      <li><code>_makeFlip(page)</code>: 准备页面的翻转效果</li>
      <li><code>_makeRange()</code>: 创建当前视图范围内的页面</li>
      <li><code>range(page)</code>: 返回应在 DOM 中的页面范围</li>
      <li><code>_necessPage(page)</code>: 检测页面是否在当前视图范围内</li>
      <li><code>_removeFromDOM()</code>: 通过从 DOM 中移除页面来释放内存</li>
      <li><code>_removePageFromDOM(page)</code>: 从 DOM 中移除指定页面</li>
      <li><code>removePage(page)</code>: 移除指定页面</li>
      <li><code>_movePages(from, change)</code>: 移动页面</li>
      <li><code>display(display)</code>: 设置或获取显示模式</li>
      <li><code>animating()</code>: 检测页面是否正在动画中</li>
      <li><code>disable(bool)</code>: 禁用或启用翻页效果</li>
      <li><code>size(width, height)</code>: 设置或获取尺寸</li>
      <li><code>resize()</code>: 调整每个页面的大小</li>
      <li><code>_removeMv(page)</code>: 从缓存中移除动画</li>
      <li><code>_addMv(page)</code>: 将动画添加到缓存</li>
      <li><code>_view(page)</code>: 获取视图索引</li>
      <li><code>view(page)</code>: 获取视图</li>
      <li><code>stop(ok)</code>: 停止动画</li>
      <li><code>pages(pages)</code>: 设置或获取页数</li>
      <li><code>_fitPage(page, ok)</code>: 无动画切换到指定页</li>
      <li><code>_turnPage(page)</code>: 带动画翻到指定页</li>
      <li><code>page(page)</code>: 设置或获取当前页</li>
      <li><code>next()</code>: 翻到下一页</li>
      <li><code>previous()</code>: 翻到上一页</li>
      <li><code>_addMotionPage()</code>: 将动作添加到内部列表</li>
      <li><code>_start(e, opts, corner)</code>: 开始翻页（在 flip 上下文中调用）</li>
      <li><code>_end(e, turned)</code>: 结束翻页（在 flip 上下文中调用）</li>
      <li><code>_pressed()</code>: 按下状态处理（在 flip 上下文中调用）</li>
      <li><code>_released(e, point)</code>: 释放状态处理（在 flip 上下文中调用）</li>
      <li><code>_flip()</code>: 执行翻页（在 flip 上下文中调用）</li>
      <li><code>calculateZ(mv)</code>: 计算动画期间页面的 z-index 值</li>
      <li><code>update()</code>: 更新每个页面的 z-index 和 display 属性</li>
      <li><code>_setPageLoc(page)</code>: 设置页面的 z-index 和 display 属性</li>
    </ul>

    <h3>FlipMethods - 页面翻转方法</h3>
    <ul>
      <li><code>init(opts)</code>: 初始化翻页效果</li>
      <li><code>setData(d)</code>: 设置数据</li>
      <li><code>options(opts)</code>: 设置或获取选项</li>
      <li><code>z(z)</code>: 设置 z-index</li>
      <li><code>_cAllowed()</code>: 获取允许的角落</li>
      <li><code>_cornerActivated(e)</code>: 检测角落是否被激活</li>
      <li><code>_c(corner, opts)</code>: 获取角落坐标</li>
      <li><code>_c2(corner)</code>: 获取另一组角落坐标</li>
      <li><code>_foldingPage(corner)</code>: 获取折叠页</li>
      <li><code>_backGradient()</code>: 处理背面渐变</li>
      <li><code>resize(full)</code>: 调整大小</li>
      <li><code>_addPageWrapper()</code>: 通过添加通用包装器和其他对象来准备页面</li>
      <li><code>_fold(point)</code>: 应用折叠变换</li>
      <li><code>_moveFoldingPage(bool)</code>: 移动折叠页</li>
      <li><code>_showFoldedPage(c, animate)</code>: 显示折叠页</li>
      <li><code>hide()</code>: 隐藏页面</li>
      <li><code>hideFoldedPage(animate)</code>: 隐藏折叠页</li>
      <li><code>turnPage(corner)</code>: 翻页</li>
      <li><code>moving()</code>: 检测是否在移动</li>
      <li><code>isTurning()</code>: 检测是否正在翻页</li>
      <li><code>_eventStart(e)</code>: 事件开始处理</li>
      <li><code>_eventMove(e)</code>: 事件移动处理</li>
      <li><code>_eventEnd()</code>: 事件结束处理</li>
      <li><code>disable(disable)</code>: 禁用或启用效果</li>
    </ul>

    <h3>辅助方法</h3>
    <ul>
      <li><code>transform(transform, origin)</code>: jQuery 扩展方法，应用 CSS 变换</li>
      <li><code>animatef(point)</code>: jQuery 扩展方法，创建动画效果</li>
      <li><code>bezier(p1, p2, p3, p4, t)</code>: 从四个点的贝塞尔曲线获取 2D 点</li>
      <li><code>rad(degrees)</code>: 将角度从度转换为弧度</li>
      <li><code>deg(radians)</code>: 将角度从弧度转换为度</li>
      <li><code>point2D(x, y)</code>: 获取 2D 点</li>
      <li><code>translate(x, y, use3d)</code>: 返回平移值</li>
      <li><code>rotate(degrees)</code>: 返回旋转值</li>
      <li><code>has(property, object)</code>: 检查属性是否属于对象</li>
      <li><code>getPrefix()</code>: 获取 CSS3 供应商前缀</li>
      <li><code>gradient(obj, p0, p1, colors, numColors)</code>: 添加渐变</li>
      <li><code>cla(that, methods, args)</code>: 方法调用助手</li>
    </ul>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <h3>初始化流程</h3>
    <div class="mermaid">
      graph TD
        A["初始化翻书<br>(turn.init)"] --> B["检查3D支持<br>(getPrefix)"]
        B --> C["获取CSS前缀<br>(getPrefix)"]
        C --> D["初始化数据对象<br>($.extend)"]
        D --> E["设置选项配置<br>($.extend)"]
        E --> F["绑定事件回调<br>(bind)"]
        F --> G["设置CSS样式<br>(css)"]
        G --> H["设置显示模式<br>(display)"]
        H --> I["应用硬件加速<br>(transform)"]
        I --> J["添加页面<br>(_addPage)"]
        J --> K["跳转到指定页面<br>(page)"]
        K --> L["初始化完成"]
    </div>

    <h3>翻页流程</h3>
    <div class="mermaid">
      graph TD
        A["开始翻页<br>(page)"] --> B{"检查页面有效性<br>(_turnPage)"}
        B -->|无效| C["返回"]
        B -->|有效| D["触发翻页事件<br>(trigger.turning)"]
        D --> E["检查页面是否存在<br>(pageObjs[page])"]
        E -->|不存在| F["返回"]
        E -->|存在| G["停止当前动画<br>(stop)"]
        G --> H["创建页面范围<br>(_makeRange)"]
        H --> I{"判断显示模式<br>(display)"}
        I -->|单页| J["单页翻转<br>(flip.turnPage)"]
        I -->|双页| K["双页翻转<br>(flip.turnPage)"]
        J --> L["执行翻页动画<br>(animatef)"]
        K --> L
        L --> M["触发完成事件<br>(trigger.turned)"]
        M --> N["翻页完成"]
    </div>

    <h3>页面折叠流程</h3>
    <div class="mermaid">
      graph TD
        A["开始折叠页面<br>(_fold)"] --> B["计算折叠角度<br>(Math.atan2)"]
        B --> C["计算变换参数<br>(translate,rotate)"]
        C --> D["应用页面变换<br>(transform)"]
        D --> E["设置容器样式<br>(css)"]
        E --> F["设置包装器变换<br>(transform)"]
        F --> G["设置折叠页变换<br>(transform)"]
        G --> H{"是否启用渐变<br>(opts.frontGradient)"}
        H -->|是| I["应用渐变效果<br>(gradient)"]
        H -->|否| J["折叠完成"]
        I --> J
    </div>

    <h3>事件处理流程</h3>
    <div class="mermaid">
      graph TD
        A["初始化事件<br>(bind)"] --> B["绑定事件监听<br>($(document).bind)"]
        B --> C{"接收事件类型<br>(events)"}
        C -->|按下| D["处理开始事件<br>(_eventStart)"]
        C -->|移动| E["处理移动事件<br>(_eventMove)"]
        C -->|释放| F["处理结束事件<br>(_eventEnd)"]
        D --> G["检测角落激活<br>(_cornerActivated)"]
        G --> H{"角落是否激活"}
        H -->|是| I["开始翻页处理<br>(_moveFoldingPage)"]
        H -->|否| J["事件处理完成"]
        I --> J
        E --> K["更新折叠位置<br>(_showFoldedPage)"]
        K --> J
        F --> L["完成翻页过程<br>(hideFoldedPage)"]
        L --> J
    </div>

    <h3>渲染和更新流程</h3>
    <div class="mermaid">
      graph TD
        A["开始更新<br>(update)"] --> B{"是否有动画页面<br>(pageMv.length)"}
        B -->|是| C["更新动画页面<br>(calculateZ)"]
        B -->|否| D["更新静态页面<br>(_setPageLoc)"]
        C --> E["设置层级和显示属性<br>(css)"]
        D --> E
        E --> F["应用翻页效果<br>(flip)"]
        F --> G{"是否需要调整尺寸"}
        G -->|是| H["调整页面尺寸<br>(resize)"]
        G -->|否| I["更新完成"]
        H --> I
    </div>
    
    <h3>页面管理流程</h3>
    <div class="mermaid">
      graph TD
        A["开始管理页面"] --> B["添加新页面<br>(addPage)"]
        B --> C["创建页面元素<br>(pageObjs)"]
        C --> D{"是否需要在DOM中<br>(_necessPage)"}
        D -->|是| E["创建页面包装器<br>(pageWrap)"]
        D -->|否| F["设置页面位置为0<br>(pagePlace=0)"]
        E --> G["设置页面位置<br>(pagePlace=page)"]
        G --> H["添加到容器<br>(append)"]
        H --> I["创建翻页效果<br>(_makeFlip)"]
        I --> J["更新视图<br>(update)"]
        F --> J
        J --> K["移除非必要页面<br>(_removeFromDOM)"]
        K --> L["页面管理完成"]
    </div>
  </div>

  <!-- 示例 -->
  <div id="examples">
    <h2>示例</h2>
    
    <h3>基本初始化</h3>
    <pre><code class="language-html">
&lt;div id="flipbook"&gt;
  &lt;div&gt;Page 1&lt;/div&gt;
  &lt;div&gt;Page 2&lt;/div&gt;
  &lt;div&gt;Page 3&lt;/div&gt;
  &lt;div&gt;Page 4&lt;/div&gt;
&lt;/div&gt;
    </code></pre>
    
    <pre><code class="language-javascript">
$("#flipbook").turn({
  width: 400,
  height: 300,
  autoCenter: true
});
    </code></pre>
    
    <h3>添加页面</h3>
    <pre><code class="language-javascript">
// 向翻书添加一个新页面
$("#flipbook").turn("addPage", $("<div>New Page</div>"), 5);
    </code></pre>
    
    <h3>事件处理</h3>
    <pre><code class="language-javascript">
$("#flipbook").turn({
  when: {
    turning: function(event, page, pageObj) {
      // 在页面翻转之前触发
      console.log("Turning to page " + page);
    },
    turned: function(event, page, pageObj) {
      // 页面翻转完成后触发
      console.log("Now on page " + page);
    },
    start: function(event, pageObj, corner) {
      // 开始拖动页面时触发
      console.log("Start dragging the page");
    }
  }
});

// 也可以使用标准 jQuery 事件绑定
$("#flipbook").on("turn", function(event, page, pageObj) {
  console.log("Page is turning to " + page);
});
    </code></pre>
    
    <h3>控制翻书</h3>
    <pre><code class="language-javascript">
// 跳转到指定页面
$("#flipbook").turn("page", 3);

// 下一页
$("#flipbook").turn("next");

// 上一页
$("#flipbook").turn("previous");

// 获取当前页码
var currentPage = $("#flipbook").turn("page");

// 获取总页数
var totalPages = $("#flipbook").turn("pages");

// 检查是否正在动画中
var isAnimating = $("#flipbook").turn("animating");
    </code></pre>
    
    <h3>页面布局</h3>
    <pre><code class="language-javascript">
// 设置为单页模式
$("#flipbook").turn("display", "single");

// 设置为双页模式
$("#flipbook").turn("display", "double");

// 调整尺寸
$("#flipbook").turn("size", 800, 600);
    </code></pre>
  </div>

  <!-- 返回顶部按钮 -->
  <button class="back-to-top">↑</button>

  <script>
    // 创建目录
    createTableOfContents();
    
    // 初始化流程图
    mermaid.initialize({
      startOnLoad: true,
      flowchart: {
        useMaxWidth: true,
        htmlLabels: true
      }
    });
  </script>
</body>
</html> 