<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PDFPageView - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#constants">常量</a>
    <a href="#properties">属性</a>
    <a href="#methods">方法</a>
    <a href="#flowcharts">流程图</a>
    <a href="#examples">示例</a>
    <a href="#notes">注意事项</a>
  </div>

  <h1>PDFPageView 模块文档</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>PDFPageView 是 PDF.js 库中负责单个 PDF 页面渲染和展示的组件。它继承自 BasePDFPageView 基础类，处理页面的缩放、旋转、注释、文本层和交互功能。PDFPageView 是 PDFViewer 中的核心渲染单元，负责将 PDF 页面转换为可视化的 HTML 元素。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 常量 -->
  <div id="constants">
    <h2>常量</h2>
    
    <p>PDFPageView 使用以下常量：</p>
    
    <ul>
      <li><code>DEFAULT_SCALE</code>: 默认缩放比例</li>
      <li>
        <code>LAYERS_ORDER</code>: 图层顺序映射，定义了各个图层的 z-index 顺序
        <ul>
          <li><code>canvasWrapper</code>: 0</li>
          <li><code>textLayer</code>: 1</li>
          <li><code>annotationLayer</code>: 2</li>
          <li><code>annotationEditorLayer</code>: 3</li>
          <li><code>xfaLayer</code>: 3</li>
        </ul>
      </li>
      <li>
        <code>TextLayerMode</code>: 文本层模式枚举
        <ul>
          <li><code>DISABLE</code>: 禁用文本层</li>
          <li><code>ENABLE</code>: 启用文本层</li>
          <li><code>ENABLE_PERMISSIONS</code>: 启用带权限的文本层</li>
        </ul>
      </li>
      <li>
        <code>AnnotationMode</code>: 注释模式枚举
        <ul>
          <li><code>DISABLE</code>: 禁用注释</li>
          <li><code>ENABLE</code>: 启用注释</li>
          <li><code>ENABLE_FORMS</code>: 启用表单注释</li>
          <li><code>ENABLE_STORAGE</code>: 启用注释存储</li>
        </ul>
      </li>
      <li>
        <code>RenderingStates</code>: 渲染状态枚举
        <ul>
          <li><code>INITIAL</code>: 初始状态</li>
          <li><code>RUNNING</code>: 运行中</li>
          <li><code>PAUSED</code>: 暂停</li>
          <li><code>FINISHED</code>: 完成</li>
        </ul>
      </li>
    </ul>
  </div>

  <!-- 属性 -->
  <div id="properties">
    <h2>属性</h2>
    
    <h3>公共属性</h3>
    <ul>
      <li><code>div</code>: 页面的 DOM 容器元素</li>
      <li><code>pdfPage</code>: 与此视图关联的 PDF 页面对象</li>
      <li><code>id</code>: 页面 ID (通常是页码)</li>
      <li><code>renderingId</code>: 渲染 ID</li>
      <li><code>scale</code>: 缩放比例</li>
      <li><code>viewport</code>: 页面视口对象</li>
      <li><code>rotation</code>: 旋转角度</li>
      <li><code>pdfPageRotate</code>: PDF 页面自身的旋转角度</li>
      <li><code>pageLabel</code>: 页面标签</li>
      <li><code>imageResourcesPath</code>: 图像资源路径</li>
      <li><code>eventBus</code>: 事件总线</li>
      <li><code>renderingQueue</code>: 渲染队列</li>
      <li><code>textLayer</code>: 文本层</li>
      <li><code>annotationLayer</code>: 注释层</li>
      <li><code>annotationEditorLayer</code>: 注释编辑器层</li>
      <li><code>xfaLayer</code>: XFA 层</li>
      <li><code>structTreeLayer</code>: 结构树层</li>
      <li><code>drawLayer</code>: 绘图层</li>
      <li><code>detailView</code>: 详细视图</li>
      <li><code>l10n</code>: 本地化对象</li>
      <li><code>maxCanvasPixels</code>: 最大画布像素数</li>
      <li><code>maxCanvasDim</code>: 最大画布尺寸</li>
      <li><code>enableDetailCanvas</code>: 是否启用详细画布</li>
    </ul>

    <h3>私有属性</h3>
    <ul>
      <li><code>#canvasWrapper</code>: 画布包装器</li>
      <li><code>#annotationMode</code>: 注释模式</li>
      <li><code>#textLayerMode</code>: 文本层模式</li>
      <li><code>#layerProperties</code>: 图层属性</li>
      <li><code>#enableAutoLinking</code>: 是否启用自动链接</li>
      <li><code>#hasRestrictedScaling</code>: 是否有受限缩放</li>
      <li><code>#needsRestrictedScaling</code>: 是否需要受限缩放</li>
      <li><code>#originalViewport</code>: 原始视口</li>
      <li><code>#previousRotation</code>: 前一个旋转角度</li>
      <li><code>#userUnit</code>: 用户单位</li>
      <li><code>#useThumbnailCanvas</code>: 缩略图画布使用选项</li>
      <li><code>#layers</code>: 图层数组</li>
      <li><code>#isEditing</code>: 是否正在编辑</li>
    </ul>
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法</h2>
    
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">构造函数</h3>
      </div>
      <div class="method-content">
        <p>创建一个新的 PDFPageView 实例。</p>
        <pre><code class="language-javascript">
constructor(options)
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>options</code>: 配置选项对象
            <ul>
              <li><code>container</code>: 容器元素</li>
              <li><code>defaultViewport</code>: 默认视口</li>
              <li><code>id</code>: 页面 ID</li>
              <li><code>scale</code>: 缩放比例 (可选，默认为 DEFAULT_SCALE)</li>
              <li><code>textLayerMode</code>: 文本层模式 (可选，默认为 TextLayerMode.ENABLE)</li>
              <li><code>annotationMode</code>: 注释模式 (可选，默认为 AnnotationMode.ENABLE_FORMS)</li>
              <li><code>imageResourcesPath</code>: 图像资源路径 (可选)</li>
              <li><code>maxCanvasPixels</code>: 最大画布像素数 (可选)</li>
              <li><code>eventBus</code>: 事件总线</li>
              <li><code>renderingQueue</code>: 渲染队列 (可选)</li>
              <li><code>layerProperties</code>: 图层属性 (可选)</li>
              <li><code>l10n</code>: 本地化对象 (可选)</li>
              <li><code>enableDetailCanvas</code>: 是否启用详细画布 (可选，默认为 true)</li>
              <li><code>pageColors</code>: 页面颜色 (可选)</li>
            </ul>
          </li>
        </ul>
      </div>
    </div>

    <h3>核心方法</h3>
    <ul>
      <li><code>setPdfPage(pdfPage)</code>: 设置 PDF 页面对象</li>
      <li><code>destroy()</code>: 销毁页面视图并释放资源</li>
      <li><code>reset()</code>: 重置页面视图到初始状态</li>
      <li><code>update(scale, rotation, optionalContentConfigPromise)</code>: 更新页面视图的缩放和旋转</li>
      <li><code>draw()</code>: 绘制页面内容</li>
      <li><code>paint(canvasWrapper, paintTask)</code>: 绘制页面到画布</li>
      <li><code>render(canvasContext, viewport, intent, transform, imageLayer, canvasFactory, annotationMode)</code>: 渲染页面到画布上下文</li>
      <li><code>getOperatorList()</code>: 获取页面的操作符列表</li>
      <li><code>buildSvg()</code>: 构建 SVG 版本的页面</li>
      <li><code>hasEditableAnnotations()</code>: 检查页面是否有可编辑的注释</li>
      <li><code>setPageLabel(label)</code>: 设置页面标签</li>
    </ul>
    
    <h3>私有方法</h3>
    <ul>
      <li><code>#addLayer(div, name)</code>: 添加图层到页面</li>
      <li><code>#setDimensions()</code>: 设置页面尺寸</li>
      <li><code>#dispatchLayerRendered(name, error)</code>: 分发图层渲染完成事件</li>
      <li><code>#renderAnnotationLayer()</code>: 渲染注释图层</li>
      <li><code>#renderAnnotationEditorLayer()</code>: 渲染注释编辑器图层</li>
      <li><code>#renderDrawLayer()</code>: 渲染绘图图层</li>
      <li><code>#renderXfaLayer()</code>: 渲染 XFA 图层</li>
      <li><code>#renderTextLayer()</code>: 渲染文本图层</li>
      <li><code>#buildXfaTextContentItems()</code>: 构建 XFA 文本内容项</li>
      <li><code>#finishPaintTask(paintTask, error)</code>: 完成绘制任务</li>
    </ul>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <h3>页面初始化与渲染流程</h3>
    <p>1. 创建 PDFPageView 实例<br>
    2. 调用 <code>setPdfPage()</code> 方法设置 PDF 页面对象<br>
    3. 调用 <code>draw()</code> 方法开始渲染过程<br>
    4. 创建各种图层 (画布、文本、注释等)<br>
    5. 渲染 PDF 内容到画布<br>
    6. 渲染辅助图层 (文本、注释等)<br>
    7. 分发渲染完成事件</p>
    
    <div class="mermaid">
      graph TD
          A[创建 PDFPageView 实例] --> B[设置 PDF 页面对象]
          B --> C[调用 draw 方法]
          C --> D[创建画布图层]
          C --> E[创建文本图层]
          C --> F[创建注释图层]
          D --> G[渲染 PDF 内容到画布]
          E --> H[渲染文本内容]
          F --> I[渲染注释]
          G --> J[分发页面渲染完成事件]
          H --> J
          I --> J
    </div>
    
    <h3>页面更新流程</h3>
    <p>1. 调用 <code>update()</code> 方法更新缩放或旋转<br>
    2. 更新视口对象<br>
    3. 设置新尺寸<br>
    4. 重置页面视图<br>
    5. 重新绘制页面内容</p>
    
    <div class="mermaid">
      graph TD
          A[调用 update 方法] --> B[更新视口对象]
          B --> C[设置新尺寸]
          C --> D[重置页面视图]
          D --> E[重新绘制页面内容]
    </div>
    
    <h3>文本内容处理流程</h3>
    <p>1. 获取页面文本内容<br>
    2. 创建文本图层<br>
    3. 渲染文本内容<br>
    4. 应用文本高亮 (如果有)<br>
    5. 分发文本图层渲染完成事件</p>
    
    <div class="mermaid">
      graph TD
          A[获取页面文本内容] --> B[创建文本图层]
          B --> C[渲染文本内容]
          C --> D[应用文本高亮]
          D --> E[分发文本图层渲染完成事件]
    </div>
  </div>

  <!-- 使用示例 -->
  <div id="examples">
    <h2>使用示例</h2>
    
    <h3>基本初始化与渲染</h3>
    <pre><code class="language-javascript">
// 创建必要的组件
const eventBus = new EventBus();
const viewport = new PageViewport({
  viewBox: [0, 0, 595.28, 841.89],
  scale: 1,
  rotation: 0
});

// 创建 PDFPageView 实例
const pageView = new PDFPageView({
  container: document.getElementById('pageContainer'),
  id: 1,
  scale: 1.0,
  defaultViewport: viewport,
  eventBus,
  textLayerMode: TextLayerMode.ENABLE,
  annotationMode: AnnotationMode.ENABLE_FORMS,
  imageResourcesPath: '/images/'
});

// 加载 PDF 页面并渲染
pdfDocument.getPage(1).then(pdfPage => {
  pageView.setPdfPage(pdfPage);
  return pageView.draw();
});
    </code></pre>
    
    <h3>更新页面缩放与旋转</h3>
    <pre><code class="language-javascript">
// 更新页面缩放比例
pageView.update(2.0, 0);  // 缩放比例为 2.0，旋转角度保持不变

// 更新页面旋转角度
pageView.update(pageView.scale, 90);  // 保持当前缩放比例，旋转 90 度

// 同时更新缩放和旋转
pageView.update(1.5, 180);  // 缩放比例为 1.5，旋转 180 度
    </code></pre>
    
    <h3>销毁页面视图</h3>
    <pre><code class="language-javascript">
// 在不再需要页面视图时销毁它，释放资源
pageView.destroy();
    </code></pre>
  </div>

  <!-- 注意事项 -->
  <div id="notes">
    <h2>注意事项</h2>
    
    <ol>
      <li>PDFPageView 是重资源组件，使用完毕后应调用 <code>destroy()</code> 方法释放资源。</li>
      <li>过大的缩放比例可能导致性能问题，特别是在移动设备上。</li>
      <li>文本层渲染取决于 PDF 文档中的文本内容，有些 PDF 可能没有可选择的文本。</li>
      <li>注释渲染受到 PDF 文档权限和注释模式设置的影响。</li>
      <li>页面渲染是异步过程，应使用事件监听器来处理渲染完成事件。</li>
    </ol>
  </div>

  <!-- 返回顶部按钮 -->
  <button class="back-to-top">↑</button>

  <script>
    // 创建目录
    createTableOfContents();
  </script>
</body>
</html> 