<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>OverlayManager - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#properties">属性</a>
    <a href="#methods">方法</a>
    <a href="#flowcharts">流程图</a>
    <a href="#examples">示例</a>
    <a href="#notes">注意事项</a>
  </div>

  <h1>OverlayManager 模块文档</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>OverlayManager 是 PDF.js 库中用于管理覆盖层（如模态对话框、弹出菜单等）的组件。它提供了一种机制，确保在任何给定时间只有一个覆盖层处于活动状态，并管理覆盖层的打开、关闭和层叠顺序。该组件负责处理覆盖层的显示逻辑，焦点管理和键盘事件处理。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 属性 -->
  <div id="properties">
    <h2>属性</h2>
    
    <h3>公共属性</h3>
    <ul>
      <li>无直接暴露的公共属性，OverlayManager 的所有交互通过方法完成</li>
    </ul>

    <h3>私有属性</h3>
    <ul>
      <li><code>#overlays</code>: 存储已注册覆盖层的映射表，键为覆盖层名称，值为覆盖层对象</li>
      <li><code>#active</code>: 当前激活的覆盖层名称</li>
      <li><code>#keyDownBound</code>: 键盘按下事件的绑定处理函数</li>
    </ul>
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法</h2>
    
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">构造函数</h3>
      </div>
      <div class="method-content">
        <p>创建一个新的 OverlayManager 实例。</p>
        <pre><code class="language-javascript">
constructor() {
  this.#overlays = new Map();
  this.#active = null;

  // 绑定键盘事件处理函数到实例
  this.#keyDownBound = this.#keyDown.bind(this);
}
        </code></pre>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">register(name, element, callerCloseMethod = null, canForceClose = false)</h3>
      </div>
      <div class="method-content">
        <p>注册一个新的覆盖层。</p>
        <pre><code class="language-javascript">
register(name, element, callerCloseMethod = null, canForceClose = false) {
  return new Promise((resolve) => {
    const container = {
      name,
      element,
      callerCloseMethod,
      canForceClose,
      resolve,
    };
    
    if (!this.#overlays.has(name)) {
      this.#overlays.set(name, container);
    }
  });
}
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>name</code>: 覆盖层的唯一名称</li>
          <li><code>element</code>: 覆盖层的DOM元素</li>
          <li><code>callerCloseMethod</code>: 调用者提供的关闭方法（可选）</li>
          <li><code>canForceClose</code>: 是否可以强制关闭（可选，默认为false）</li>
        </ul>
        <p><strong>返回:</strong> Promise，在覆盖层关闭时解析</p>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">unregister(name)</h3>
      </div>
      <div class="method-content">
        <p>注销已注册的覆盖层。</p>
        <pre><code class="language-javascript">
unregister(name) {
  if (this.#overlays.has(name)) {
    this.#overlays.delete(name);
  }
}
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>name</code>: 要注销的覆盖层名称</li>
        </ul>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">async open(name)</h3>
      </div>
      <div class="method-content">
        <p>打开指定的覆盖层。</p>
        <pre><code class="language-javascript">
async open(name) {
  if (!this.#overlays.has(name)) {
    throw new Error(`The overlay ${name} does not exist.`);
  } else if (this.#active) {
    if (this.#active === name) {
      throw new Error(`The overlay ${name} is already active.`);
    } else if (this.#overlays.get(this.#active).canForceClose) {
      await this.close();
    } else {
      throw new Error(`Another overlay ${this.#active} is currently active.`);
    }
  }
  
  this.#active = name;
  this.#overlays.get(name).element.classList.remove("hidden");
  
  window.addEventListener("keydown", this.#keyDownBound);
}
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>name</code>: 要打开的覆盖层名称</li>
        </ul>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">async close(name = null)</h3>
      </div>
      <div class="method-content">
        <p>关闭当前活动的覆盖层，或指定名称的覆盖层。</p>
        <pre><code class="language-javascript">
async close(name = null) {
  if (!this.#active) {
    return;
  }
  
  if (name && this.#active !== name) {
    throw new Error(`Another overlay ${this.#active} is currently active.`);
  }
  
  const overlay = this.#overlays.get(this.#active);
  overlay.element.classList.add("hidden");
  
  window.removeEventListener("keydown", this.#keyDownBound);
  
  this.#active = null;
  
  // 执行调用者提供的关闭方法（如果有）
  if (overlay.callerCloseMethod) {
    await overlay.callerCloseMethod();
  }
  
  // 解析注册时返回的Promise
  overlay.resolve();
}
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>name</code>: 要关闭的覆盖层名称（可选，默认为null）</li>
        </ul>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">active</h3>
      </div>
      <div class="method-content">
        <p>获取当前活动覆盖层的名称。</p>
        <pre><code class="language-javascript">
get active() {
  return this.#active;
}
        </code></pre>
        <p><strong>返回:</strong> 当前活动覆盖层的名称，如果没有活动覆盖层则返回null</p>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">#keyDown(evt)</h3>
      </div>
      <div class="method-content">
        <p>处理键盘按键事件，用于覆盖层的键盘交互。</p>
        <pre><code class="language-javascript">
#keyDown(evt) {
  // 处理ESC键，关闭可强制关闭的覆盖层
  if (this.#active && evt.keyCode === 27) { // Esc键
    const overlay = this.#overlays.get(this.#active);
    if (overlay.canForceClose) {
      this.close();
      evt.preventDefault();
    }
  }
}
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>evt</code>: 键盘事件对象</li>
        </ul>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">destroy()</h3>
      </div>
      <div class="method-content">
        <p>销毁 OverlayManager 实例，清理所有资源。</p>
        <pre><code class="language-javascript">
destroy() {
  // 关闭当前活动的覆盖层
  if (this.#active) {
    this.close().catch(console.error);
  }
  
  // 清空覆盖层映射
  this.#overlays.clear();
}
        </code></pre>
      </div>
    </div>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <h3>覆盖层注册和打开流程</h3>
    <p>1. 注册覆盖层<br>
    2. 检查是否可以打开<br>
    3. 显示覆盖层<br>
    4. 绑定事件处理函数</p>
    
    <div class="mermaid">
      graph TD
        A[注册覆盖层] --> B{覆盖层是否已存在?}
        B -->|否| C[创建新覆盖层容器]
        B -->|是| D[返回现有覆盖层的Promise]
        C --> D
        
        E[打开覆盖层] --> F{覆盖层是否已注册?}
        F -->|否| G[抛出错误]
        F -->|是| H{是否有活动覆盖层?}
        H -->|否| J[设置当前覆盖层为活动]
        H -->|是| I{当前覆盖层是要打开的覆盖层?}
        I -->|是| K[抛出错误: 覆盖层已活动]
        I -->|否| L{当前覆盖层可强制关闭?}
        L -->|是| M[关闭当前覆盖层]
        L -->|否| N[抛出错误: 另一个覆盖层活动中]
        M --> J
        J --> O[移除覆盖层的hidden类]
        O --> P[绑定键盘事件监听器]
    </div>
    
    <h3>覆盖层关闭流程</h3>
    <p>1. 检查活动状态<br>
    2. 隐藏覆盖层<br>
    3. 解绑事件处理函数<br>
    4. 执行回调和解析Promise</p>
    
    <div class="mermaid">
      graph TD
        A[关闭覆盖层] --> B{是否有活动覆盖层?}
        B -->|否| C[直接返回]
        B -->|是| D{指定了要关闭的覆盖层?}
        D -->|是| E{指定覆盖层是当前活动覆盖层?}
        D -->|否| G[获取当前活动覆盖层]
        E -->|否| F[抛出错误: 另一个覆盖层活动中]
        E -->|是| G
        G --> H[添加hidden类到覆盖层元素]
        H --> I[移除键盘事件监听器]
        I --> J[清除活动覆盖层引用]
        J --> K{覆盖层有关闭方法?}
        K -->|是| L[执行覆盖层的关闭方法]
        K -->|否| M[解析覆盖层的Promise]
        L --> M
    </div>
    
    <h3>键盘事件处理流程</h3>
    <p>1. 接收键盘事件<br>
    2. 检查是否为ESC键<br>
    3. 检查覆盖层是否可强制关闭<br>
    4. 关闭覆盖层</p>
    
    <div class="mermaid">
      graph TD
        A[键盘事件处理] --> B{是否有活动覆盖层?}
        B -->|否| C[不处理事件]
        B -->|是| D{是否为ESC键?}
        D -->|否| C
        D -->|是| E{当前覆盖层可强制关闭?}
        E -->|否| C
        E -->|是| F[关闭当前覆盖层]
        F --> G[阻止事件默认行为]
    </div>
  </div>

  <!-- 示例 -->
  <div id="examples">
    <h2>使用示例</h2>
    
    <h3>基本用法</h3>
    <pre><code class="language-javascript">
// 创建 OverlayManager 实例
const overlayManager = new OverlayManager();

// 获取模态对话框元素
const dialog = document.getElementById("dialog");
const dialogCloseButton = document.getElementById("dialogClose");

// 注册覆盖层
overlayManager.register(
  "myDialog", // 唯一名称
  dialog,     // DOM元素
  () => {     // 关闭方法（可选）
    console.log("对话框已关闭");
    return Promise.resolve();
  },
  true        // 可以通过ESC键强制关闭
);

// 打开对话框
async function openDialog() {
  try {
    await overlayManager.open("myDialog");
    console.log("对话框已打开");
  } catch (error) {
    console.error("无法打开对话框:", error);
  }
}

// 关闭对话框
async function closeDialog() {
  try {
    await overlayManager.close("myDialog");
    console.log("对话框已关闭");
  } catch (error) {
    console.error("无法关闭对话框:", error);
  }
}

// 绑定点击事件
document.getElementById("openButton").addEventListener("click", openDialog);
dialogCloseButton.addEventListener("click", closeDialog);

// 在组件销毁时注销覆盖层
function cleanup() {
  overlayManager.unregister("myDialog");
}
    </code></pre>
    
    <h3>多个覆盖层管理</h3>
    <pre><code class="language-javascript">
// 创建 OverlayManager 实例
const overlayManager = new OverlayManager();

// 定义覆盖层元素
const dialogs = {
  password: document.getElementById("passwordDialog"),
  preferences: document.getElementById("preferencesDialog"),
  info: document.getElementById("infoDialog")
};

// 注册所有覆盖层
for (const [name, element] of Object.entries(dialogs)) {
  const closeButton = element.querySelector(".close-button");
  const canForceClose = name !== "password"; // 密码对话框不允许ESC键关闭
  
  // 定义关闭方法
  const closeMethod = async () => {
    console.log(`${name} 对话框已关闭`);
    // 可以在这里执行一些清理操作
    return Promise.resolve();
  };
  
  // 注册覆盖层
  overlayManager.register(name, element, closeMethod, canForceClose);
  
  // 绑定关闭按钮
  if (closeButton) {
    closeButton.addEventListener("click", () => {
      overlayManager.close(name).catch(console.error);
    });
  }
}

// 创建打开覆盖层的函数
function createOpenHandler(name) {
  return async () => {
    try {
      await overlayManager.open(name);
      console.log(`${name} 对话框已打开`);
      
      // 根据不同对话框执行特定初始化
      if (name === "password") {
        document.getElementById("passwordInput").focus();
      } else if (name === "preferences") {
        loadPreferences();
      }
    } catch (error) {
      console.error(`无法打开 ${name} 对话框:`, error);
    }
  };
}

// 绑定打开按钮
document.getElementById("openPasswordButton").addEventListener(
  "click",
  createOpenHandler("password")
);

document.getElementById("openPreferencesButton").addEventListener(
  "click",
  createOpenHandler("preferences")
);

document.getElementById("openInfoButton").addEventListener(
  "click",
  createOpenHandler("info")
);

// 加载首选项的辅助函数
function loadPreferences() {
  console.log("加载用户首选项...");
  // 这里可以加载和显示用户首选项
}
    </code></pre>
    
    <h3>使用 Promise</h3>
    <pre><code class="language-javascript">
// 创建 OverlayManager 实例
const overlayManager = new OverlayManager();

// 创建确认对话框
const confirmDialog = document.getElementById("confirmDialog");
const confirmYesButton = document.getElementById("confirmYes");
const confirmNoButton = document.getElementById("confirmNo");
const confirmMessage = document.getElementById("confirmMessage");

// 注册确认对话框，注意没有提供关闭方法
overlayManager.register("confirm", confirmDialog, null, true);

// 创建一个Promise包装的确认函数
async function showConfirmDialog(message) {
  // 设置消息
  confirmMessage.textContent = message;
  
  // 打开对话框
  await overlayManager.open("confirm");
  
  // 返回一个新的Promise，用户选择后解析
  return new Promise((resolve) => {
    // 用户点击"是"
    const onYesClick = () => {
      cleanup();
      resolve(true);
    };
    
    // 用户点击"否"
    const onNoClick = () => {
      cleanup();
      resolve(false);
    };
    
    // 绑定按钮事件
    confirmYesButton.addEventListener("click", onYesClick);
    confirmNoButton.addEventListener("click", onNoClick);
    
    // 清理函数
    function cleanup() {
      // 移除事件监听器
      confirmYesButton.removeEventListener("click", onYesClick);
      confirmNoButton.removeEventListener("click", onNoClick);
      
      // 关闭对话框
      overlayManager.close("confirm").catch(console.error);
    }
  });
}

// 使用确认对话框
async function deleteDocument() {
  const confirmed = await showConfirmDialog("确定要删除此文档吗？");
  
  if (confirmed) {
    console.log("用户确认删除文档");
    // 执行删除操作
    await performDelete();
  } else {
    console.log("用户取消删除操作");
  }
}

// 模拟删除操作
async function performDelete() {
  return new Promise(resolve => {
    setTimeout(() => {
      console.log("文档已删除");
      resolve();
    }, 1000);
  });
}

// 绑定删除按钮
document.getElementById("deleteButton").addEventListener("click", deleteDocument);
    </code></pre>
  </div>

  <!-- 注意事项 -->
  <div id="notes">
    <h2>注意事项</h2>
    
    <ul>
      <li>OverlayManager 确保在任何时候只有一个覆盖层处于活动状态，这有助于维护清晰的用户界面层次结构。</li>
      <li>注册覆盖层时，应提供唯一的名称，以避免名称冲突。</li>
      <li>覆盖层的DOM元素应包含适当的CSS类来处理显示和隐藏，OverlayManager 只负责添加和移除 "hidden" 类。</li>
      <li>对于需要用户明确操作才能关闭的覆盖层（如未保存的表单），应将 canForceClose 设置为 false。</li>
      <li>在使用 open 方法时，如果另一个覆盖层已经活动，并且不能被强制关闭，则会抛出错误。代码应适当处理这些异常。</li>
      <li>提供的 callerCloseMethod 可以用于执行覆盖层关闭前的清理操作，如保存表单数据或重置状态。</li>
      <li>当应用程序的组件被销毁时，应确保调用 unregister 方法来注销相关覆盖层，以防止内存泄漏。</li>
      <li>OverlayManager 不负责覆盖层的内容、样式或行为，它只管理其显示状态和活动状态。</li>
      <li>在创建复杂的模态对话框时，建议将对话框的逻辑封装在专用的类中，然后使用 OverlayManager 管理其显示。</li>
    </ul>
  </div>

  <script>
    // 在页面加载完成后初始化 Mermaid
    document.addEventListener('DOMContentLoaded', function() {
      mermaid.initialize({ startOnLoad: true });
      
      // 生成目录
      generateTOC();
    });
  </script>
</body>
</html>