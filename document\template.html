<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>模块名称 - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#methods">方法列表</a>
    <a href="#flowcharts">流程图</a>
  </div>

  <h1>模块名称</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>模块描述内容，从源代码注释中提取</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法列表</h2>
    
    <!-- 方法块示例 -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">方法名称(参数)</h3>
      </div>
      <div class="method-content">
        <p>方法描述，从源代码注释中提取</p>
        <pre><code class="language-javascript">
// 方法代码，从源代码中提取
function methodName(param) {
  // 方法实现
}
        </code></pre>
      </div>
    </div>
    
    <!-- 可以有多个方法块 -->
    
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <div class="mermaid">
      <!-- Mermaid流程图代码 -->
      graph TD
        A[开始] --> B{判断条件}
        B -->|条件为真| C[处理步骤1]
        B -->|条件为假| D[处理步骤2]
        C --> E[结束]
        D --> E
    </div>
    
    <!-- 可以有多个流程图 -->
    
  </div>

  <!-- 返回顶部按钮 -->
  <button class="back-to-top">↑</button>

  <script>
    // 创建目录
    createTableOfContents();
  </script>
</body>
</html> 