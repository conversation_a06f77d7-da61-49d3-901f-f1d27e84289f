/**
 * Native_Simple.js - 简化版原生JavaScript翻页效果库
 * 专注于核心功能，确保稳定运行
 */

(function() {
    'use strict';
    
    // 全局变量
    var isTouch = 'ontouchstart' in window;
    var events = isTouch ? 
        {start: 'touchstart', move: 'touchmove', end: 'touchend'} :
        {start: 'mousedown', move: 'mousemove', end: 'mouseup'};

    // 数据存储管理器
    var DataManager = {
        storage: new WeakMap(),
        
        set: function(element, key, value) {
            var data = this.storage.get(element) || {};
            if (typeof key === 'object') {
                Object.assign(data, key);
            } else {
                data[key] = value;
            }
            this.storage.set(element, data);
        },
        
        get: function(element, key) {
            var data = this.storage.get(element) || {};
            return key ? data[key] : data;
        }
    };

    // 工具函数
    function extend(target) {
        var sources = Array.prototype.slice.call(arguments, 1);
        sources.forEach(function(source) {
            if (source) {
                for (var key in source) {
                    if (source.hasOwnProperty(key)) {
                        target[key] = source[key];
                    }
                }
            }
        });
        return target;
    }

    function setStyles(element, styles) {
        for (var key in styles) {
            if (styles.hasOwnProperty(key)) {
                element.style[key] = styles[key];
            }
        }
    }

    function getOffset(element) {
        var rect = element.getBoundingClientRect();
        return {
            top: rect.top + window.pageYOffset,
            left: rect.left + window.pageXOffset
        };
    }

    // 事件管理器
    var EventManager = {
        on: function(element, eventType, handler) {
            element.addEventListener(eventType, handler, false);
        },
        
        off: function(element, eventType, handler) {
            element.removeEventListener(eventType, handler, false);
        },
        
        trigger: function(element, eventType, detail) {
            var event = new CustomEvent(eventType, {
                detail: detail,
                bubbles: true,
                cancelable: true
            });
            element.dispatchEvent(event);
        }
    };

    // 简化的Turn方法
    var TurnMethods = {
        init: function(element, options) {
            var defaults = {
                width: 800,
                height: 600,
                display: 'double',
                page: 1,
                duration: 600,
                when: {}
            };
            
            var opts = extend({}, defaults, options);
            var data = {
                opts: opts,
                page: opts.page,
                totalPages: element.children.length,
                display: opts.display
            };
            
            DataManager.set(element, data);
            
            // 设置容器样式
            setStyles(element, {
                position: 'relative',
                width: opts.width + 'px',
                height: opts.height + 'px',
                overflow: 'hidden'
            });
            
            // 设置页面样式
            this.setupPages(element);
            
            // 绑定事件
            if (opts.when) {
                for (var eventName in opts.when) {
                    if (opts.when.hasOwnProperty(eventName)) {
                        EventManager.on(element, eventName, opts.when[eventName]);
                    }
                }
            }
            
            // 显示初始页面
            this.showPage(element, opts.page);
            
            return element;
        },
        
        setupPages: function(element) {
            var data = DataManager.get(element);
            var pages = Array.prototype.slice.call(element.children);
            var pageWidth = data.display === 'double' ? data.opts.width / 2 : data.opts.width;
            
            pages.forEach(function(page, index) {
                setStyles(page, {
                    position: 'absolute',
                    width: pageWidth + 'px',
                    height: data.opts.height + 'px',
                    top: '0px',
                    left: (index * pageWidth) + 'px',
                    transition: 'transform ' + data.opts.duration + 'ms ease-in-out',
                    transformOrigin: 'left center'
                });
                
                // 添加点击事件
                this.addPageEvents(page, index + 1);
            }, this);
        },
        
        addPageEvents: function(page, pageNumber) {
            var self = this;
            
            EventManager.on(page, 'click', function(e) {
                var element = page.parentNode;
                var rect = page.getBoundingClientRect();
                var x = e.clientX - rect.left;
                var isRightSide = x > rect.width / 2;
                
                if (isRightSide) {
                    self.next(element);
                } else {
                    self.previous(element);
                }
            });
        },
        
        showPage: function(element, pageNumber) {
            var data = DataManager.get(element);
            var pages = Array.prototype.slice.call(element.children);
            var pageWidth = data.display === 'double' ? data.opts.width / 2 : data.opts.width;
            
            // 触发turning事件
            EventManager.trigger(element, 'turning', [pageNumber]);
            
            pages.forEach(function(page, index) {
                var offset = (index + 1 - pageNumber) * pageWidth;
                page.style.transform = 'translateX(' + offset + 'px)';
                
                // 设置可见性
                if (data.display === 'double') {
                    page.style.display = (Math.abs(index + 1 - pageNumber) <= 1) ? 'block' : 'none';
                } else {
                    page.style.display = (index + 1 === pageNumber) ? 'block' : 'none';
                }
            });
            
            data.page = pageNumber;
            DataManager.set(element, data);
            
            // 触发turned事件
            setTimeout(function() {
                EventManager.trigger(element, 'turned', [pageNumber]);
            }, data.opts.duration);
        },
        
        page: function(element, pageNumber) {
            if (pageNumber !== undefined) {
                var data = DataManager.get(element);
                if (pageNumber >= 1 && pageNumber <= data.totalPages) {
                    this.showPage(element, pageNumber);
                }
                return element;
            } else {
                return DataManager.get(element).page;
            }
        },
        
        next: function(element) {
            var data = DataManager.get(element);
            var nextPage = Math.min(data.page + 1, data.totalPages);
            return this.page(element, nextPage);
        },
        
        previous: function(element) {
            var data = DataManager.get(element);
            var prevPage = Math.max(data.page - 1, 1);
            return this.page(element, prevPage);
        },
        
        size: function(element, width, height) {
            if (width && height) {
                var data = DataManager.get(element);
                data.opts.width = width;
                data.opts.height = height;
                DataManager.set(element, data);
                
                setStyles(element, {
                    width: width + 'px',
                    height: height + 'px'
                });
                
                this.setupPages(element);
                return element;
            } else {
                var data = DataManager.get(element);
                return {width: data.opts.width, height: data.opts.height};
            }
        }
    };

    // 通用调用函数
    function callMethod(element, methods, args) {
        if (!args[0] || typeof(args[0]) === 'object') {
            return methods.init.call(methods, element, args[0]);
        } else if (methods[args[0]]) {
            var methodArgs = Array.prototype.slice.call(args, 1);
            methodArgs.unshift(element);
            return methods[args[0]].apply(methods, methodArgs);
        } else {
            throw new Error(args[0] + ' is an invalid method');
        }
    }

    // 公共API
    function Turn() {
        var args = Array.prototype.slice.call(arguments);
        return callMethod(this, TurnMethods, args);
    }

    // 为Element原型添加turn方法
    Element.prototype.turn = function() {
        return Turn.apply(this, arguments);
    };

    // 全局对象暴露
    window.TurnJS = {
        Turn: Turn,
        isTouch: isTouch,
        version: '1.0.0-simple'
    };

    // 模块化支持
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = window.TurnJS;
    }

    if (typeof define === 'function' && define.amd) {
        define(function() {
            return window.TurnJS;
        });
    }

})();
