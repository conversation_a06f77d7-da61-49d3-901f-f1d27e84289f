<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>GrabToPan - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#properties">属性</a>
    <a href="#methods">方法</a>
    <a href="#flowcharts">流程图</a>
    <a href="#examples">示例</a>
    <a href="#notes">注意事项</a>
  </div>

  <h1>GrabToPan 模块文档</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>GrabToPan 是 PDF.js 库中的抓取平移工具组件，用于实现类似手形工具的交互功能。它允许用户通过按下鼠标并拖动来平移（滚动）文档内容，类似于许多 PDF 阅读器和图像查看器中的"手形工具"。这种交互方式对于浏览大型文档或图像特别有用，提供了直观的导航体验。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 常量 -->
  <div id="constants">
    <h2>常量</h2>
    
    <p>GrabToPan 定义了以下常量：</p>
    
    <ul>
      <li>
        <code>CSS_CLASS_GRAB</code>: 表示启用抓取模式的 CSS 类名 ("grab-to-pan-grab")
      </li>
    </ul>
  </div>

  <!-- 属性 -->
  <div id="properties">
    <h2>属性</h2>
    
    <h3>公共属性</h3>
    <ul>
      <li><code>element</code>: 要应用抓取平移功能的 HTML 元素</li>
      <li><code>document</code>: 元素所属的文档对象</li>
      <li><code>overlay</code>: 抓取平移时显示的覆盖层元素</li>
    </ul>

    <h3>私有属性</h3>
    <ul>
      <li><code>#activateAC</code>: 激活状态的中止控制器（AbortController）</li>
      <li><code>#mouseDownAC</code>: 鼠标按下事件的中止控制器</li>
      <li><code>#scrollAC</code>: 滚动事件的中止控制器</li>
      <li><code>scrollLeftStart</code>: 开始抓取时元素的水平滚动位置</li>
      <li><code>scrollTopStart</code>: 开始抓取时元素的垂直滚动位置</li>
      <li><code>clientXStart</code>: 开始抓取时鼠标的 X 坐标</li>
      <li><code>clientYStart</code>: 开始抓取时鼠标的 Y 坐标</li>
    </ul>
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法</h2>
    
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">构造函数</h3>
      </div>
      <div class="method-content">
        <p>创建一个新的 GrabToPan 实例。</p>
        <pre><code class="language-javascript">
constructor({ element }) {
  this.element = element;
  this.document = element.ownerDocument;
  const overlay = this.overlay = document.createElement("div");
  overlay.className = "grab-to-pan-grabbing";
}
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>element</code>: 要应用抓取平移功能的 HTML 元素</li>
        </ul>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">activate()</h3>
      </div>
      <div class="method-content">
        <p>激活抓取平移功能。</p>
        <pre><code class="language-javascript">
activate() {
  if (!this.#activateAC) {
    this.#activateAC = new AbortController();
    this.element.addEventListener("mousedown", this.#onMouseDown.bind(this), {
      capture: true,
      signal: this.#activateAC.signal
    });
    this.element.classList.add(CSS_CLASS_GRAB);
  }
}
        </code></pre>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">deactivate()</h3>
      </div>
      <div class="method-content">
        <p>停用抓取平移功能。</p>
        <pre><code class="language-javascript">
deactivate() {
  if (this.#activateAC) {
    this.#activateAC.abort();
    this.#activateAC = null;
    this.#endPan();
    this.element.classList.remove(CSS_CLASS_GRAB);
  }
}
        </code></pre>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">toggle()</h3>
      </div>
      <div class="method-content">
        <p>切换抓取平移功能的激活状态。</p>
        <pre><code class="language-javascript">
toggle() {
  if (this.#activateAC) {
    this.deactivate();
  } else {
    this.activate();
  }
}
        </code></pre>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">ignoreTarget(node)</h3>
      </div>
      <div class="method-content">
        <p>确定是否应忽略特定节点上的抓取平移操作。</p>
        <pre><code class="language-javascript">
ignoreTarget(node) {
  return node.matches("a[href], a[href] *, input, textarea, button, button *, select, option");
}
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>node</code>: 要检查的 DOM 节点</li>
        </ul>
        <p><strong>返回值:</strong></p>
        <ul>
          <li><code>Boolean</code>: 如果应忽略该节点上的抓取操作则返回 true</li>
        </ul>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">#onMouseDown(event)</h3>
      </div>
      <div class="method-content">
        <p>处理鼠标按下事件，开始抓取平移操作。</p>
        <pre><code class="language-javascript">
#onMouseDown(event) {
  // 仅处理左键点击，且不在忽略目标上的事件
  if (event.button !== 0 || this.ignoreTarget(event.target)) {
    return;
  }
  
  // 针对Firefox的特殊处理，检查originalTarget是否可访问
  if (event.originalTarget) {
    try {
      event.originalTarget.tagName;
    } catch {
      return;
    }
  }
  // 记录初始滚动位置和鼠标坐标
  this.scrollLeftStart = this.element.scrollLeft;
  this.scrollTopStart = this.element.scrollTop;
  this.clientXStart = event.clientX;
  this.clientYStart = event.clientY;
  
  // 创建鼠标事件中止控制器
  this.#mouseDownAC = new AbortController();
  const boundEndPan = this.#endPan.bind(this),
    mouseOpts = {
      capture: true, // 使用事件捕获
      signal: this.#mouseDownAC.signal // 关联中止控制器
    };
  
  // 添加鼠标移动和抬起事件监听器
  this.document.addEventListener("mousemove", this.#onMouseMove.bind(this), mouseOpts);
  this.document.addEventListener("mouseup", boundEndPan, mouseOpts);
  
  // 添加滚动事件监听器，如果滚动则结束平移
  this.#scrollAC = new AbortController();
  this.element.addEventListener("scroll", boundEndPan, {
    capture: true,
    signal: this.#scrollAC.signal
  });
  
  // 阻止默认行为和事件传播
  stopEvent(event);
  
  // 移除当前活动元素的焦点
  const focusedElement = document.activeElement;
  if (focusedElement && !focusedElement.contains(event.target)) {
    focusedElement.blur();
  }
}
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>event</code>: 鼠标按下事件对象</li>
        </ul>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">#onMouseMove(event)</h3>
      </div>
      <div class="method-content">
        <p>处理鼠标移动事件，执行抓取平移操作。</p>
        <pre><code class="language-javascript">
#onMouseMove(event) {
  // 中止滚动事件监听器，因为我们将手动滚动
  this.#scrollAC?.abort();
  this.#scrollAC = null;
  
  // 检查是否仍然按下左键，如果未按下则结束平移
  if (!(event.buttons & 1)) {
    this.#endPan();
    return;
  }
  
  // 计算鼠标移动距离
  const xDiff = event.clientX - this.clientXStart;
  const yDiff = event.clientY - this.clientYStart;
  
  // 滚动元素，方向与鼠标移动相反（拖动效果）
  this.element.scrollTo({
    top: this.scrollTopStart - yDiff,
    left: this.scrollLeftStart - xDiff,
    behavior: "instant" // 立即滚动，不使用平滑效果
  });
  
  // 显示抓取平移覆盖层
  if (!this.overlay.parentNode) {
    document.body.append(this.overlay);
  }
}
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>event</code>: 鼠标移动事件对象</li>
        </ul>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">#endPan()</h3>
      </div>
      <div class="method-content">
        <p>结束抓取平移操作。</p>
        <pre><code class="language-javascript">
#endPan() {
  // 移除鼠标事件监听器
  this.#mouseDownAC?.abort();
  this.#mouseDownAC = null;
  
  // 移除滚动事件监听器
  this.#scrollAC?.abort();
  this.#scrollAC = null;
  
  // 移除覆盖层
  this.overlay.remove();
}
        </code></pre>
      </div>
    </div>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <h3>抓取平移激活流程</h3>
    <p>1. 用户调用 activate() 方法<br>
    2. 添加鼠标按下事件监听器<br>
    3. 添加抓取样式类</p>
    
    <div class="mermaid">
      graph TD
        A[调用 activate 方法] --> B{是否已激活?}
        B -->|是| C[不执行任何操作]
        B -->|否| D[创建中止控制器]
        D --> E[添加鼠标按下事件监听器]
        E --> F[添加 CSS_CLASS_GRAB 类]
        F --> G[抓取平移功能激活]
    </div>
    
    <h3>抓取平移操作流程</h3>
    <p>1. 用户在目标元素上按下鼠标<br>
    2. 记录初始位置<br>
    3. 用户拖动鼠标<br>
    4. 计算偏移并滚动<br>
    5. 释放鼠标结束操作</p>
    
    <div class="mermaid">
      graph TD
        A[用户在元素上按下鼠标] --> B{是否为左键?}
        B -->|否| C[忽略事件]
        B -->|是| D{是否为忽略目标?}
        D -->|是| C
        D -->|否| E[记录初始滚动位置和鼠标坐标]
        E --> F[添加鼠标移动和抬起事件监听器]
        F --> G[添加滚动事件监听器]
        G --> H[阻止默认行为]
        H --> I[用户移动鼠标]
        I --> J[取消滚动事件监听器]
        J --> K{是否仍按下左键?}
        K -->|否| L[结束平移]
        K -->|是| M[计算鼠标移动距离]
        M --> N[按相反方向滚动元素]
        N --> O[显示覆盖层]
        O --> P[用户继续移动或释放鼠标]
        P --> Q{释放鼠标?}
        Q -->|是| L
        Q -->|否| I
        L --> R[移除事件监听器]
        R --> S[移除覆盖层]
    </div>
    
    <h3>切换与停用流程</h3>
    <p>1. 用户调用 toggle() 或 deactivate()<br>
    2. 清理事件监听器<br>
    3. 移除样式类</p>
    
    <div class="mermaid">
      graph TD
        A[调用 toggle 方法] --> B{当前是否激活?}
        B -->|是| C[调用 deactivate]
        B -->|否| D[调用 activate]
        C --> E[中止激活控制器]
        E --> F[调用 #endPan 方法]
        F --> G[移除 CSS_CLASS_GRAB 类]
        G --> H[抓取平移功能停用]
    </div>
  </div>

  <!-- 示例 -->
  <div id="examples">
    <h2>使用示例</h2>
    
    <h3>基本用法</h3>
    <pre><code class="language-javascript">
// 获取要应用抓取平移功能的容器元素
const container = document.getElementById('pdfContainer');

// 创建 GrabToPan 实例
const grabToPan = new GrabToPan({
  element: container
});

// 激活抓取平移功能
grabToPan.activate();

// 稍后停用抓取平移功能
// grabToPan.deactivate();

// 或者切换抓取平移状态
// grabToPan.toggle();
    </code></pre>
    
    <h3>与工具栏按钮集成</h3>
    <pre><code class="language-javascript">
// HTML:
// <button id="handToolButton" title="手形工具">
//   <span>手形工具</span>
// </button>

// 获取 PDF 容器和工具栏按钮
const pdfContainer = document.getElementById('pdfContainer');
const handToolButton = document.getElementById('handToolButton');

// 创建 GrabToPan 实例
const grabToPan = new GrabToPan({
  element: pdfContainer
});

// 为按钮添加点击事件处理程序
handToolButton.addEventListener('click', function() {
  grabToPan.toggle();
  
  // 更新按钮状态
  if (handToolButton.classList.contains('toggled')) {
    handToolButton.classList.remove('toggled');
  } else {
    handToolButton.classList.add('toggled');
  }
});
    </code></pre>
    
    <h3>与 PDFCursorTools 集成</h3>
    <pre><code class="language-javascript">
// 懒加载创建 GrabToPan 实例的示例
// 通常在 PDFCursorTools 类中使用此模式
class PDFCursorTools {
  constructor({ container }) {
    this.container = container;
    // 其他初始化代码...
  }
  
  // 使用 getter 实现懒加载
  get _handTool() {
    // 如果 _handTool 属性不存在，则创建并保存它
    if (!this._handToolInstance) {
      this._handToolInstance = new GrabToPan({
        element: this.container
      });
    }
    return this._handToolInstance;
  }
  
  // 切换为手形工具
  switchToHand() {
    // 停用其他工具...
    
    // 激活手形工具
    this._handTool.activate();
  }
  
  // 停用手形工具
  deactivateHand() {
    this._handTool.deactivate();
  }
}

// 使用示例
const cursorTools = new PDFCursorTools({
  container: document.getElementById('viewerContainer')
});

// 切换到手形工具
cursorTools.switchToHand();

// 稍后停用手形工具
// cursorTools.deactivateHand();
    </code></pre>
  </div>

  <!-- 注意事项 -->
  <div id="notes">
    <h2>注意事项</h2>
    
    <ul>
      <li>GrabToPan 组件使用私有字段和方法（以 # 开头），需要现代浏览器支持。</li>
      <li>通过 AbortController 管理事件监听器，确保在组件停用或销毁时正确清理资源。</li>
      <li>默认忽略交互元素（如链接、按钮、输入框等）上的抓取平移操作，确保这些元素仍可正常使用。</li>
      <li>使用 CSS 类 "grab-to-pan-grab" 修改元素的光标样式，通常显示为"手形"光标。</li>
      <li>激活抓取平移时，会创建一个覆盖层（overlay）以防止其他元素捕获鼠标事件，确保平滑的拖动体验。</li>
      <li>为获得最佳性能，滚动使用 behavior: "instant" 选项，而不是平滑滚动。</li>
      <li>该组件在 PDF.js 中通常与 PDFCursorTools 一起使用，作为手形工具模式的实现。</li>
      <li>在处理大型 PDF 文档或图像时，抓取平移功能特别有用，可提供更直观的导航体验。</li>
      <li>若在触摸设备上使用，可能需要额外添加触摸事件支持。</li>
    </ul>
  </div>

  <script>
    // 在页面加载完成后初始化 Mermaid
    document.addEventListener('DOMContentLoaded', function() {
      mermaid.initialize({ startOnLoad: true });
      
      // 生成目录
      createTableOfContents();
    });
  </script>
</body>
</html> 