<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PDFTextLayer - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#constants">常量</a>
    <a href="#properties">属性</a>
    <a href="#methods">方法</a>
    <a href="#flowcharts">流程图</a>
    <a href="#examples">示例</a>
    <a href="#notes">注意事项</a>
  </div>

  <h1>PDFTextLayer 模块文档</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>PDFTextLayer 是 PDF.js 库中负责文本内容提取和显示的组件。它将 PDF 页面中的文本内容渲染为可选择、可搜索的 HTML 文本元素，覆盖在 PDF 渲染的画布上。文本层使得用户可以复制、选择和搜索 PDF 中的文字，是实现 PDF 文档可访问性的关键组件。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 常量 -->
  <div id="constants">
    <h2>常量</h2>
    
    <p>PDFTextLayer 使用以下常量：</p>
    
    <ul>
      <li>
        <code>TextLayerMode</code>: 文本层模式枚举
        <ul>
          <li><code>DISABLE</code>: 禁用文本层</li>
          <li><code>ENABLE</code>: 启用文本层</li>
          <li><code>ENABLE_PERMISSIONS</code>: 基于文档权限启用文本层</li>
        </ul>
      </li>
      <li><code>DEFAULT_LINE_HEIGHT</code>: 默认行高</li>
      <li><code>TEXT_LAYER_ZINDEX</code>: 文本层的 z-index 值</li>
      <li><code>TEXT_SELECTION_MARGIN</code>: 文本选择边距</li>
    </ul>
  </div>

  <!-- 属性 -->
  <div id="properties">
    <h2>属性</h2>
    
    <h3>公共属性</h3>
    <ul>
      <li><code>textContentSource</code>: 文本内容来源</li>
      <li><code>textDivs</code>: 文本 div 元素数组</li>
      <li><code>textContentItemsStr</code>: 文本内容项字符串数组</li>
      <li><code>div</code>: 文本层容器元素</li>
      <li><code>viewport</code>: 视口对象</li>
      <li><code>textDivProperties</code>: 文本 div 属性数组</li>
      <li><code>isOffscreenCanvasSupported</code>: 是否支持离屏画布</li>
      <li><code>renderingDone</code>: 渲染是否完成</li>
      <li><code>capability</code>: 渲染能力对象</li>
      <li><code>accessibilityManager</code>: 辅助功能管理器</li>
      <li><code>highlighter</code>: 文本高亮器</li>
    </ul>

    <h3>私有属性</h3>
    <ul>
      <li><code>#textLayerFontSize</code>: 文本层字体大小</li>
      <li><code>#textLayerRenderTask</code>: 文本层渲染任务</li>
      <li><code>#bounds</code>: 文本边界数组</li>
      <li><code>#devicePixelRatio</code>: 设备像素比</li>
      <li><code>#canceled</code>: 是否已取消渲染</li>
    </ul>
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法</h2>
    
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">构造函数</h3>
      </div>
      <div class="method-content">
        <p>创建一个新的 PDFTextLayer 实例。</p>
        <pre><code class="language-javascript">
constructor({
  textContentSource,
  container,
  viewport,
  textDivs = null,
  textContentItemsStr = null,
  textDivProperties = null,
  isOffscreenCanvasSupported = true,
  accessibilityManager = null,
})
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>textContentSource</code>: 文本内容来源（可以是 Promise 或直接的文本内容对象）</li>
          <li><code>container</code>: 容器元素</li>
          <li><code>viewport</code>: 视口对象</li>
          <li><code>textDivs</code>: 文本 div 元素数组 (可选)</li>
          <li><code>textContentItemsStr</code>: 文本内容项字符串数组 (可选)</li>
          <li><code>textDivProperties</code>: 文本 div 属性数组 (可选)</li>
          <li><code>isOffscreenCanvasSupported</code>: 是否支持离屏画布 (可选，默认为 true)</li>
          <li><code>accessibilityManager</code>: 辅助功能管理器 (可选)</li>
        </ul>
      </div>
    </div>

    <h3>核心方法</h3>
    <ul>
      <li><code>render(timeout = 0)</code>: 渲染文本层</li>
      <li><code>cancel()</code>: 取消文本层渲染</li>
      <li><code>setTextContentSource(source)</code>: 设置文本内容来源</li>
      <li><code>hide()</code>: 隐藏文本层</li>
      <li><code>show()</code>: 显示文本层</li>
      <li><code>destroy()</code>: 销毁文本层</li>
      <li><code>setTextHighlighter(highlighter)</code>: 设置文本高亮器</li>
      <li><code>setAccessibilityManager(manager)</code>: 设置辅助功能管理器</li>
    </ul>
    
    <h3>私有方法</h3>
    <ul>
      <li><code>#renderTextLayer()</code>: 渲染文本层内容</li>
      <li><code>#setupRenderLayoutTimer()</code>: 设置渲染布局定时器</li>
      <li><code>#expandTextDivs(expandDivs)</code>: 展开文本 div 元素</li>
      <li><code>#appendText(geom, styles)</code>: 添加文本到文本层</li>
      <li><code>#calculateScalingFactor(width, height)</code>: 计算缩放因子</li>
      <li><code>#measureText(text, styles)</code>: 测量文本尺寸</li>
      <li><code>#layout(textDivs, textContentItemsStr)</code>: 布局文本元素</li>
      <li><code>#convertMatches(matches, begin, end)</code>: 转换匹配结果</li>
      <li><code>#areTextDivsInitialized()</code>: 检查文本 div 是否已初始化</li>
    </ul>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <h3>文本层初始化与渲染流程</h3>
    <p>1. 创建 PDFTextLayer 实例<br>
    2. 调用 <code>render()</code> 方法开始渲染<br>
    3. 获取文本内容数据<br>
    4. 创建文本 div 元素<br>
    5. 为每个文本项设置位置和样式<br>
    6. 添加文本元素到容器<br>
    7. 完成渲染并触发回调</p>
    
    <div class="mermaid">
      graph TD
          A[创建 PDFTextLayer 实例] --> B[调用 render 方法]
          B --> C[获取文本内容数据]
          C --> D[创建文本 div 元素]
          D --> E[计算每个文本元素的位置]
          E --> F[应用样式和变换]
          F --> G[添加到文本层容器]
          G --> H[渲染完成并触发回调]
    </div>
    
    <h3>文本选择流程</h3>
    <p>1. 用户在文本层上进行选择操作<br>
    2. 浏览器原生文本选择机制生效<br>
    3. 文本层中的文本被选中<br>
    4. 用户可以复制选中的文本</p>
    
    <div class="mermaid">
      graph TD
          A[用户在文本层上按下鼠标] --> B[用户拖动鼠标选择文本]
          B --> C[浏览器原生选择机制激活]
          C --> D[文本元素被选中]
          D --> E[用户可以复制选中文本]
    </div>
    
    <h3>文本搜索高亮流程</h3>
    <p>1. 搜索功能找到匹配文本<br>
    2. 调用文本高亮器<br>
    3. 高亮器标记匹配的文本 div<br>
    4. 应用高亮样式<br>
    5. 可选：滚动到高亮位置</p>
    
    <div class="mermaid">
      graph TD
          A[搜索功能找到匹配文本] --> B[计算匹配文本在文本层中的位置]
          B --> C[调用高亮器的高亮方法]
          C --> D[为匹配文本 div 添加高亮类]
          D --> E[应用高亮样式]
          E --> F[滚动视图到高亮位置]
    </div>
  </div>

  <!-- 示例 -->
  <div id="examples">
    <h2>使用示例</h2>
    
    <h3>基本用法</h3>
    <pre><code class="language-javascript">
// 创建 PDFTextLayer 实例
const textLayer = new PDFTextLayer({
  textContentSource: pdfPage.getTextContent(),
  container: document.getElementById('textLayerDiv'),
  viewport: viewport
});

// 渲染文本层
textLayer.render().then(() => {
  console.log('文本层渲染完成');
});
    </code></pre>
    
    <h3>与搜索功能集成</h3>
    <pre><code class="language-javascript">
// 创建文本高亮器
const textHighlighter = new TextHighlighter({
  findController: pdfFindController,
  eventBus: eventBus
});

// 设置文本高亮器
textLayer.setTextHighlighter(textHighlighter);

// 监听文本查找事件
eventBus.on("updatefindmatchescount", function(evt) {
  console.log(`找到 ${evt.matchesCount.current} 个匹配项，总共 ${evt.matchesCount.total} 个`);
});

// 执行搜索
pdfFindController.executeCommand("find", {
  query: "example",
  caseSensitive: false,
  entireWord: false,
  highlightAll: true,
  findPrevious: false
});
    </code></pre>
    
    <h3>文本层可访问性支持</h3>
    <pre><code class="language-javascript">
// 创建辅助功能管理器
const accessibilityManager = new TextAccessibilityManager({
  eventBus: eventBus
});

// 设置辅助功能管理器
textLayer.setAccessibilityManager(accessibilityManager);

// 当文本层渲染完成后，通知辅助功能管理器
textLayer.render().then(() => {
  // 通知屏幕阅读器页面内容已更新
  accessibilityManager.notify("textlayerrendered", {
    source: textLayer,
    pageNumber: pageNumber
  });
});
    </code></pre>
  </div>

  <!-- 注意事项 -->
  <div id="notes">
    <h2>注意事项</h2>
    
    <ul>
      <li>文本层的布局可能与原始 PDF 的文本布局略有不同，因为 PDF 文本定位和 HTML 文本渲染有本质差异。</li>
      <li>某些复杂的文本布局（如竖排文本、特殊字体、复杂排版等）可能无法在文本层中完全准确再现。</li>
      <li>文本层的渲染会消耗一定的性能，特别是对于包含大量文本的页面。对于大型文档，可以考虑延迟加载或视口内加载策略。</li>
      <li>如果 PDF 文档中的文本实际上是图像（如扫描文档），文本层将无法提供文本内容，除非文档已经过 OCR 处理。</li>
      <li>某些 PDF 文档可能具有复杂的字符映射或自定义编码，这可能导致文本提取不准确。</li>
      <li>文本层的文字选择行为依赖于浏览器的原生文本选择机制，在不同浏览器中可能有细微差异。</li>
      <li>高 DPI 显示器上的文本层可能需要特殊处理以保持清晰度和与底层 PDF 渲染的对齐。</li>
      <li>文本层的隐私设置应与 PDF 文档的权限设置保持一致，例如，如果 PDF 禁止复制，文本层应相应地禁用。</li>
    </ul>
  </div>

  <script>
    // 在页面加载完成后初始化 Mermaid
    document.addEventListener('DOMContentLoaded', function() {
      mermaid.initialize({ startOnLoad: true });
      
      // 生成目录
      generateTOC();
    });
  </script>
</body>
</html> 