<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PDFRenderingQueue - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#constants">常量</a>
    <a href="#properties">属性</a>
    <a href="#methods">方法</a>
    <a href="#flowcharts">流程图</a>
    <a href="#examples">示例</a>
    <a href="#notes">注意事项</a>
  </div>

  <h1>PDFRenderingQueue 模块文档</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>PDFRenderingQueue 是 PDF.js 库中负责管理页面渲染优先级和调度的组件。它控制 PDF 页面的渲染顺序，确保可见页面优先渲染，提升用户体验。渲染队列可以暂停、恢复和取消渲染任务，并智能地管理渲染资源，是 PDF.js 渲染系统的核心协调者。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 常量 -->
  <div id="constants">
    <h2>常量</h2>
    
    <p>PDFRenderingQueue 使用以下常量：</p>
    
    <ul>
      <li>
        <code>RenderingStates</code>: 渲染状态枚举
        <ul>
          <li><code>INITIAL</code>: 初始状态</li>
          <li><code>RUNNING</code>: 渲染运行中</li>
          <li><code>PAUSED</code>: 渲染已暂停</li>
          <li><code>FINISHED</code>: 渲染已完成</li>
        </ul>
      </li>
      <li>
        <code>SCROLLABLE_PAGES_HINT</code>: 可滚动页面提示，指示在滚动视图中保持多少页面渲染
      </li>
      <li>
        <code>CLEANUP_TIMEOUT</code>: 清理超时时间（毫秒），定义未使用的渲染页面何时被清理
      </li>
    </ul>
  </div>

  <!-- 属性 -->
  <div id="properties">
    <h2>属性</h2>
    
    <h3>公共属性</h3>
    <ul>
      <li><code>onIdle</code>: 队列空闲时的回调函数</li>
      <li><code>highestPriorityPage</code>: 当前最高优先级的页面</li>
    </ul>

    <h3>私有属性</h3>
    <ul>
      <li><code>#scheduled</code>: 是否已调度渲染任务</li>
      <li><code>#rendering</code>: 当前是否有渲染任务正在运行</li>
      <li><code>#pagesPromiseCapability</code>: 页面 Promise 能力对象映射</li>
      <li><code>#pagesRequests</code>: 页面请求映射</li>
      <li><code>#pages</code>: 注册到队列的页面视图映射</li>
      <li><code>#pagesRenderBatches</code>: 批量渲染信息</li>
      <li><code>#idleCallbacks</code>: 空闲回调函数集合</li>
      <li><code>#nextBatchTypes</code>: 下一批渲染类型</li>
      <li><code>#currentBatchType</code>: 当前批处理类型</li>
      <li><code>#callback</code>: 渲染回调函数</li>
      <li><code>#lastHighestPriorityPage</code>: 上一个最高优先级页面</li>
    </ul>
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法</h2>
    
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">构造函数</h3>
      </div>
      <div class="method-content">
        <p>创建一个新的 PDFRenderingQueue 实例。</p>
        <pre><code class="language-javascript">
constructor()
        </code></pre>
        <p>构造函数不需要参数，它初始化队列状态并设置默认属性值。</p>
      </div>
    </div>

    <h3>核心方法</h3>
    <ul>
      <li><code>setViewer(pdfViewer)</code>: 设置 PDF 查看器实例</li>
      <li><code>setThumbnailViewer(pdfThumbnailViewer)</code>: 设置 PDF 缩略图查看器实例</li>
      <li><code>isHighestPriority(view)</code>: 检查给定视图是否是最高优先级</li>
      <li><code>hasViewer()</code>: 检查是否有查看器设置</li>
      <li><code>renderHighestPriority(currentlyVisiblePages)</code>: 渲染最高优先级的页面</li>
      <li><code>getHighestPriority(visible, views, scrolledDown, preRenderExtra)</code>: 获取最高优先级的页面</li>
      <li><code>addRenderTask(renderTask, batchInfo)</code>: 添加渲染任务到队列</li>
      <li><code>finishRenderTask(renderingId)</code>: 完成渲染任务</li>
      <li><code>hasRenderingTasks()</code>: 检查是否有正在进行的渲染任务</li>
      <li><code>cleanup()</code>: 清理未使用的渲染页面</li>
    </ul>
    
    <h3>私有方法</h3>
    <ul>
      <li><code>#scheduleNextBatch()</code>: 调度下一批渲染任务</li>
      <li><code>#createBatch(type, pages)</code>: 创建渲染批次</li>
      <li><code>#processBatch(batchType)</code>: 处理渲染批次</li>
      <li><code>#renderView(view)</code>: 渲染特定视图</li>
      <li><code>#getPageView(id)</code>: 获取特定 ID 的页面视图</li>
      <li><code>#performCleanup()</code>: 执行清理操作</li>
      <li><code>#executeIdleCallbacks()</code>: 执行空闲回调函数</li>
      <li><code>#registerIdleCallback(callback)</code>: 注册空闲回调函数</li>
    </ul>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <h3>渲染优先级确定流程</h3>
    <p>1. 获取当前可见页面<br>
    2. 调用 <code>getHighestPriority()</code> 方法<br>
    3. 计算每个页面的优先级<br>
    4. 确定最高优先级的页面<br>
    5. 返回优先级最高的页面 ID</p>
    
    <div class="mermaid">
      graph TD
          A[获取当前可见页面] --> B[调用 getHighestPriority 方法]
          B --> C[计算每个页面的优先级分数]
          C --> D[检查页面是否在可视区域内]
          D --> E[考虑滚动方向]
          E --> F[考虑预渲染页面]
          F --> G[确定最高优先级页面]
    </div>
    
    <h3>渲染任务调度流程</h3>
    <p>1. 调用 <code>renderHighestPriority()</code> 方法<br>
    2. 获取最高优先级页面<br>
    3. 检查该页面是否需要渲染<br>
    4. 如果需要，调用页面的 draw() 方法<br>
    5. 将渲染任务添加到队列<br>
    6. 调度下一批渲染任务</p>
    
    <div class="mermaid">
      graph TD
          A[调用 renderHighestPriority 方法] --> B[获取最高优先级页面]
          B --> C{页面是否需要渲染?}
          C -->|是| D[调用页面的 draw 方法]
          C -->|否| E[检查下一优先级页面]
          D --> F[将渲染任务添加到队列]
          F --> G[调度下一批渲染任务]
          E --> G
    </div>
    
    <h3>批处理渲染流程</h3>
    <p>1. 调用 <code>#scheduleNextBatch()</code> 方法<br>
    2. 确定下一批渲染类型<br>
    3. 处理该批次的页面<br>
    4. 渲染批次中的页面<br>
    5. 完成渲染后，检查是否有更多批次需要处理</p>
    
    <div class="mermaid">
      graph TD
          A[调用 #scheduleNextBatch 方法] --> B[确定下一批渲染类型]
          B --> C[调用 #processBatch 方法]
          C --> D[获取该批次中的页面]
          D --> E[逐个渲染页面]
          E --> F[完成当前批次]
          F --> G{有更多批次?}
          G -->|是| A
          G -->|否| H[执行空闲回调]
    </div>
  </div>

  <!-- 示例 -->
  <div id="examples">
    <h2>使用示例</h2>
    
    <h3>基本用法</h3>
    <pre><code class="language-javascript">
// 创建渲染队列实例
const renderingQueue = new PDFRenderingQueue();

// 设置 PDF 查看器和缩略图查看器
renderingQueue.setViewer(pdfViewer);
renderingQueue.setThumbnailViewer(pdfThumbnailViewer);

// 设置空闲回调
renderingQueue.onIdle = function() {
  console.log('渲染队列空闲，所有可见页面已渲染完成');
  // 执行额外的操作，如预加载其他页面
};

// 在滚动事件中触发渲染
scrollContainer.addEventListener('scroll', function() {
  // 获取当前可见页面
  const visiblePages = pdfViewer.getVisiblePages();
  
  // 触发最高优先级页面的渲染
  renderingQueue.renderHighestPriority(visiblePages);
});
    </code></pre>
    
    <h3>自定义渲染优先级</h3>
    <pre><code class="language-javascript">
// 获取当前可见页面
const visiblePages = pdfViewer.getVisiblePages();

// 自定义决定要渲染的页面
function customRenderStrategy() {
  // 获取用户最近交互的页面
  const userFocusedPage = getUserFocusedPage();
  
  // 检查该页面是否需要渲染
  if (renderingQueue.isHighestPriority(userFocusedPage)) {
    // 如果是最高优先级，立即渲染
    userFocusedPage.draw();
  } else {
    // 否则让渲染队列决定渲染顺序
    renderingQueue.renderHighestPriority(visiblePages);
  }
}

// 当用户与特定页面交互时调用
documentElement.addEventListener('click', function(event) {
  // 更新用户焦点页面
  updateUserFocusedPage(event);
  
  // 应用自定义渲染策略
  customRenderStrategy();
});
    </code></pre>
    
    <h3>监控渲染状态</h3>
    <pre><code class="language-javascript">
// 定义页面渲染状态变化的处理函数
function handleRenderingStateChange(event) {
  const pageNumber = event.pageNumber;
  const state = event.state;
  
  // 根据渲染状态更新UI
  switch (state) {
    case RenderingStates.INITIAL:
      console.log(`页面 ${pageNumber} 准备渲染`);
      updatePageStatus(pageNumber, 'pending');
      break;
    case RenderingStates.RUNNING:
      console.log(`页面 ${pageNumber} 正在渲染`);
      updatePageStatus(pageNumber, 'rendering');
      showPageLoadingIndicator(pageNumber);
      break;
    case RenderingStates.PAUSED:
      console.log(`页面 ${pageNumber} 渲染已暂停`);
      updatePageStatus(pageNumber, 'paused');
      break;
    case RenderingStates.FINISHED:
      console.log(`页面 ${pageNumber} 渲染完成`);
      updatePageStatus(pageNumber, 'rendered');
      hidePageLoadingIndicator(pageNumber);
      break;
  }
}

// 注册到事件总线
eventBus.on('pagerendering', handleRenderingStateChange);
eventBus.on('pagerendered', handleRenderingStateChange);
    </code></pre>
  </div>

  <!-- 注意事项 -->
  <div id="notes">
    <h2>注意事项</h2>
    
    <ul>
      <li>渲染队列的性能对整个 PDF 查看器的响应速度有显著影响，特别是在处理大型文档时。</li>
      <li>过于频繁地触发渲染（如在快速滚动时）可能导致资源浪费。考虑使用节流（throttling）技术来限制渲染请求频率。</li>
      <li>优先级算法可能需要根据特定用例进行调整。例如，在某些应用中，可能需要优先考虑用户最近交互的页面，而不仅仅是可见页面。</li>
      <li>对于资源受限的设备，可能需要限制同时渲染的页面数量，以避免内存过度使用或性能下降。</li>
      <li>预渲染行为应根据用户的网络条件和设备性能进行调整。在低端设备上，减少预渲染可以提高主要内容的加载速度。</li>
      <li>渲染队列与页面缓冲区（如 PDFPageViewBuffer）紧密协作，确保两者配置协调一致可以优化内存使用和渲染性能。</li>
      <li>在实现自定义渲染策略时，务必考虑边缘情况，如用户快速滚动、缩放操作或页面跳转等交互。</li>
      <li>清理策略对长时间运行的应用很重要，特别是在内存有限的环境中。可以根据应用需求调整清理超时和策略。</li>
    </ul>
  </div>

  <script>
    // 在页面加载完成后初始化 Mermaid
    document.addEventListener('DOMContentLoaded', function() {
      mermaid.initialize({ startOnLoad: true });
      
      // 生成目录
      generateTOC();
    });
  </script>
</body>
</html> 