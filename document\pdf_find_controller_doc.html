<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PDFFindController - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#constants">常量</a>
    <a href="#properties">属性</a>
    <a href="#methods">方法</a>
    <a href="#flowcharts">流程图</a>
    <a href="#examples">示例</a>
    <a href="#notes">注意事项</a>
  </div>

  <h1>PDFFindController 模块文档</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>PDFFindController 是 PDF.js 库中用于在文档中搜索和高亮文本的组件。它提供了在 PDF 文档中查找文本、导航匹配项、高亮显示匹配内容等功能。该控制器支持各种搜索选项，如区分大小写、全字匹配、匹配发音符号等，同时管理搜索结果的状态和导航。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 常量 -->
  <div id="constants">
    <h2>常量</h2>
    
    <p>PDFFindController 使用以下常量：</p>
    
    <ul>
      <li>
        <code>FindState</code>: 查找状态枚举
        <ul>
          <li><code>FOUND</code>: 找到匹配项</li>
          <li><code>NOT_FOUND</code>: 未找到匹配项</li>
          <li><code>WRAPPED</code>: 搜索已环绕到文档开始或结束</li>
          <li><code>PENDING</code>: 搜索正在进行中</li>
        </ul>
      </li>
      <li>
        <code>FindDirection</code>: 查找方向枚举
        <ul>
          <li><code>FORWARD</code>: 向前搜索</li>
          <li><code>BACKWARD</code>: 向后搜索</li>
        </ul>
      </li>
      <li>
        <code>FIND_TIMEOUT</code>: 查找超时时间（毫秒）
      </li>
      <li>
        <code>MATCH_SCROLL_OFFSET_TOP</code>: 匹配项滚动顶部偏移量
      </li>
      <li>
        <code>MATCH_SCROLL_OFFSET_LEFT</code>: 匹配项滚动左侧偏移量
      </li>
      <li>
        <code>FIND_UPDATED_STATE</code>: 查找状态更新事件
      </li>
      <li>
        <code>FIND_RESULT_PRECISION</code>: 查找结果精度
      </li>
    </ul>
  </div>

  <!-- 属性 -->
  <div id="properties">
    <h2>属性</h2>
    
    <h3>公共属性</h3>
    <ul>
      <li><code>state</code>: 当前查找状态</li>
      <li><code>pageMatches</code>: 每页匹配项位置数组</li>
      <li><code>pageMatchesLength</code>: 每页匹配项长度数组</li>
      <li><code>selected</code>: 当前选中的匹配项信息</li>
      <li><code>offset</code>: 当前匹配项的页面偏移量</li>
      <li><code>resumePageIdx</code>: 恢复搜索的页面索引</li>
      <li><code>matchCount</code>: 匹配项总数</li>
      <li><code>rawQuery</code>: 原始查询文本</li>
      <li><code>highlightMatches</code>: 是否高亮匹配项</li>
    </ul>

    <h3>私有属性</h3>
    <ul>
      <li><code>#scrollMatches</code>: 是否滚动到匹配项</li>
      <li><code>#pdfViewer</code>: PDF 查看器实例</li>
      <li><code>#eventBus</code>: 事件总线</li>
      <li><code>#pageMatches</code>: 内部页面匹配数组</li>
      <li><code>#pageMatchesLength</code>: 内部页面匹配长度数组</li>
      <li><code>#linkService</code>: 链接服务实例</li>
      <li><code>#selected</code>: 内部选中匹配项信息</li>
      <li><code>#state</code>: 内部查找状态</li>
      <li><code>#dirtyMatch</code>: 匹配是否已改变</li>
      <li><code>#findTimeout</code>: 查找超时计时器</li>
      <li><code>#highlightMatches</code>: 内部高亮匹配标志</li>
      <li><code>#resumePageIdx</code>: 内部恢复页面索引</li>
      <li><code>#firstPageCapability</code>: 第一页就绪能力对象</li>
      <li><code>#offset</code>: 内部匹配偏移量</li>
      <li><code>#extractTextPromises</code>: 文本提取 Promise 数组</li>
      <li><code>#pendingFindMatches</code>: 等待查找匹配的页面集合</li>
      <li><code>#active</code>: 查找控制器是否活动</li>
      <li><code>#previousTextDiv</code>: 前一个文本 div 元素</li>
    </ul>
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法</h2>
    
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">构造函数</h3>
      </div>
      <div class="method-content">
        <p>创建一个新的 PDFFindController 实例。</p>
        <pre><code class="language-javascript">
constructor({
  linkService,
  eventBus,
  updateMatchesCountOnProgress = true,
})
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>linkService</code>: 链接服务实例</li>
          <li><code>eventBus</code>: 事件总线</li>
          <li><code>updateMatchesCountOnProgress</code>: 是否在搜索过程中更新匹配计数 (可选，默认为 true)</li>
        </ul>
      </div>
    </div>

    <h3>核心方法</h3>
    <ul>
      <li><code>setDocument(pdfDocument)</code>: 设置要搜索的 PDF 文档</li>
      <li><code>executeCommand(cmd, state)</code>: 执行查找命令</li>
      <li><code>findFromPrevPage(pageIndex, matchIdx)</code>: 从前一页开始查找</li>
      <li><code>findMultipleSearchTexts(queries)</code>: 查找多个搜索文本</li>
      <li><code>nextMatch()</code>: 跳转到下一个匹配项</li>
      <li><code>previousMatch()</code>: 跳转到上一个匹配项</li>
      <li><code>highlightMatches(enable)</code>: 启用或禁用匹配项高亮</li>
      <li><code>updateUIState(state, previous = false)</code>: 更新 UI 状态</li>
    </ul>
    
    <h3>私有方法</h3>
    <ul>
      <li><code>#reset()</code>: 重置查找状态</li>
      <li><code>#scrollMatchIntoView({ element = null, selectedLeft = 0, pageIndex = -1, matchIndex = -1 })</code>: 滚动匹配项到视图</li>
      <li><code>#updatePage(index)</code>: 更新页面匹配状态</li>
      <li><code>#extractText()</code>: 提取文档文本</li>
      <li><code>#pageMatchesCount</code>: 计算页面匹配数量</li>
      <li><code>#updateAllPages()</code>: 更新所有页面匹配状态</li>
      <li><code>#nextPageMatch()</code>: 查找下一页匹配项</li>
      <li><code>#advanceOffsetPage(previous)</code>: 前进或后退页面偏移</li>
      <li><code>#updateMatch(found = false)</code>: 更新匹配状态</li>
      <li><code>#onFindBarClose()</code>: 处理查找栏关闭事件</li>
      <li><code>#requestMatchesCount()</code>: 请求匹配项计数</li>
      <li><code>#updateUIResultsCount()</code>: 更新 UI 结果计数</li>
      <li><code>#isEntireWord(content, startIdx, length)</code>: 检查是否为完整单词</li>
      <li><code>#calculateMatch(pageIndex)</code>: 计算页面匹配项</li>
      <li><code>#convertToRegExpString(query, caseSensitive, entireWord)</code>: 将查询转换为正则表达式字符串</li>
    </ul>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <h3>查找流程</h3>
    <p>1. 用户输入搜索文本<br>
    2. 执行查找命令<br>
    3. 提取文档文本<br>
    4. 应用搜索选项<br>
    5. 计算匹配项<br>
    6. 更新 UI 显示<br>
    7. 高亮当前匹配项</p>
    
    <div class="mermaid">
      graph TD
          A[用户输入搜索文本] --> B[调用 executeCommand 方法]
          B --> C[重置之前的搜索结果]
          C --> D[提取文档文本]
          D --> E[创建搜索正则表达式]
          E --> F[计算所有页面匹配项]
          F --> G{找到匹配项?}
          G -->|是| H[更新匹配状态为已找到]
          G -->|否| I[更新匹配状态为未找到]
          H --> J[高亮第一个匹配项]
          I --> K[显示未找到消息]
          J --> L[更新 UI 状态]
          K --> L
    </div>
    
    <h3>匹配项导航流程</h3>
    <p>1. 用户点击下一个/上一个按钮<br>
    2. 调用 <code>nextMatch()</code> 或 <code>previousMatch()</code><br>
    3. 计算下一个/上一个匹配项位置<br>
    4. 更新当前选中匹配项<br>
    5. 滚动到匹配位置<br>
    6. 更新 UI 状态</p>
    
    <div class="mermaid">
      graph TD
          A[用户点击下一个/上一个按钮] --> B{操作类型?}
          B -->|下一个| C[调用 nextMatch 方法]
          B -->|上一个| D[调用 previousMatch 方法]
          C --> E[计算下一个匹配项位置]
          D --> F[计算上一个匹配项位置]
          E --> G[更新选中匹配项]
          F --> G
          G --> H[滚动到匹配位置]
          H --> I{是否环绕搜索?}
          I -->|是| J[更新状态为环绕]
          I -->|否| K[更新状态为已找到]
          J --> L[更新 UI 状态]
          K --> L
    </div>
    
    <h3>高亮匹配项流程</h3>
    <p>1. 找到匹配项<br>
    2. 调用 <code>highlightMatches(true)</code><br>
    3. 设置高亮标志<br>
    4. 遍历所有页面<br>
    5. 触发文本层更新<br>
    6. 绘制高亮效果</p>
    
    <div class="mermaid">
      graph TD
          A[找到匹配项] --> B[调用 highlightMatches 方法]
          B --> C[设置高亮标志]
          C --> D[遍历所有页面]
          D --> E[触发 updateTextLayer 事件]
          E --> F[文本层绘制高亮背景]
          F --> G[当前匹配项应用不同样式]
    </div>
  </div>

  <!-- 示例 -->
  <div id="examples">
    <h2>使用示例</h2>
    
    <h3>基本用法</h3>
    <pre><code class="language-javascript">
// 创建 PDFFindController 实例
const findController = new PDFFindController({
  linkService: pdfLinkService,
  eventBus: eventBus
});

// 设置要搜索的 PDF 文档
pdfDocument.getPage(1).then(function() {
  findController.setDocument(pdfDocument);
});

// 执行搜索
findController.executeCommand('find', {
  query: 'search text',
  phraseSearch: true,
  caseSensitive: false,
  entireWord: false,
  highlightAll: true,
  findPrevious: false
});

// 导航到下一个匹配项
document.getElementById('findNext').addEventListener('click', function() {
  findController.nextMatch();
});

// 导航到上一个匹配项
document.getElementById('findPrevious').addEventListener('click', function() {
  findController.previousMatch();
});
    </code></pre>
    
    <h3>搜索选项处理</h3>
    <pre><code class="language-javascript">
// 处理搜索表单提交
document.getElementById('findForm').addEventListener('submit', function(event) {
  event.preventDefault();
  
  const query = document.getElementById('findInput').value;
  const caseSensitive = document.getElementById('findMatchCase').checked;
  const entireWord = document.getElementById('findEntireWord').checked;
  const highlightAll = document.getElementById('findHighlightAll').checked;
  const findPrevious = event.submitter === document.getElementById('findPrevious');
  
  findController.executeCommand('find', {
    query,
    phraseSearch: true,
    caseSensitive,
    entireWord,
    highlightAll,
    findPrevious
  });
});

// 监听匹配项更新事件
eventBus.on('updatefindmatchescount', function(data) {
  document.getElementById('findResultsCount').textContent = 
    `${data.current} of ${data.total} matches`;
});

// 监听查找状态更新事件
eventBus.on('updatefindcontrolstate', function(data) {
  const findMsg = document.getElementById('findMsg');
  const findInput = document.getElementById('findInput');
  
  switch (data.state) {
    case FindState.FOUND:
      findMsg.textContent = '';
      findMsg.classList.remove('error');
      break;
    case FindState.NOT_FOUND:
      findMsg.textContent = 'Phrase not found';
      findMsg.classList.add('error');
      break;
    case FindState.WRAPPED:
      findMsg.textContent = 'Reached end of document, continued from top';
      findMsg.classList.remove('error');
      break;
    case FindState.PENDING:
      findMsg.textContent = 'Searching...';
      findMsg.classList.remove('error');
      break;
  }
});
    </code></pre>
    
    <h3>高级搜索功能</h3>
    <pre><code class="language-javascript">
// 多关键词搜索
function searchMultipleTerms() {
  const terms = document.getElementById('multiTermsInput').value
    .split(',')
    .map(term => term.trim())
    .filter(term => term.length > 0);
  
  if (terms.length > 0) {
    findController.findMultipleSearchTexts(terms);
  }
}

// 使用正则表达式搜索
function searchWithRegex() {
  const pattern = document.getElementById('regexInput').value;
  
  try {
    // 验证正则表达式有效性
    new RegExp(pattern);
    
    findController.executeCommand('find', {
      query: pattern,
      phraseSearch: false,
      regex: true,
      caseSensitive: document.getElementById('regexMatchCase').checked,
      entireWord: false,
      highlightAll: true,
      findPrevious: false
    });
  } catch (e) {
    alert('Invalid regular expression: ' + e.message);
  }
}

// 跳转到指定索引的匹配项
function goToMatch(matchIndex) {
  if (matchIndex < 0 || matchIndex >= findController.matchCount) {
    return;
  }
  
  let currentPage = 0;
  let matchesCount = 0;
  
  // 找到匹配项所在的页面
  for (let i = 0; i < findController.pageMatches.length; i++) {
    const pageMatches = findController.pageMatches[i] || [];
    const matchesOnPage = pageMatches.length;
    
    if (matchIndex < matchesCount + matchesOnPage) {
      // 匹配项在当前页面
      currentPage = i;
      const matchIndexOnPage = matchIndex - matchesCount;
      
      // 更新选中的匹配项并滚动到视图
      findController.selected = {
        pageIdx: currentPage,
        matchIdx: matchIndexOnPage
      };
      
      findController.updateMatch(true);
      break;
    }
    
    matchesCount += matchesOnPage;
  }
}
    </code></pre>
  </div>

  <!-- 注意事项 -->
  <div id="notes">
    <h2>注意事项</h2>
    
    <ul>
      <li>在大型文档中，文本提取和搜索可能会很耗时。考虑在后台线程中执行搜索或实现增量搜索以提高响应性。</li>
      <li>某些 PDF 文档可能不包含可搜索的文本（例如扫描文档），在这种情况下，查找功能将无法找到匹配项。</li>
      <li>如果 PDF 文档中包含复杂的文本布局（如多列或复杂表格），文本提取顺序可能与视觉顺序不同，可能导致搜索结果看起来不按预期顺序。</li>
      <li>使用正则表达式搜索时，复杂的正则表达式可能会显著影响性能，特别是在大型文档中。</li>
      <li>高亮所有匹配项可能会对性能产生影响，特别是在匹配项很多的情况下。考虑为用户提供禁用全部高亮的选项。</li>
      <li>在处理包含非拉丁字符（如中文、日文、阿拉伯文等）的文档时，全字匹配和区分大小写功能可能需要特殊处理。</li>
      <li>搜索状态应通过事件系统传播，以便 UI 组件（如查找栏）可以正确显示当前状态和结果计数。</li>
      <li>对于长时间运行的搜索操作，考虑添加取消功能，允许用户随时中断搜索过程。</li>
    </ul>
  </div>

  <script>
    // 在页面加载完成后初始化 Mermaid
    document.addEventListener('DOMContentLoaded', function() {
      mermaid.initialize({ startOnLoad: true });
      
      // 生成目录
      generateTOC();
    });
  </script>
</body>
</html> 