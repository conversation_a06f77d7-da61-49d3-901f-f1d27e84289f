<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>EditorUndoBar - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#properties">属性</a>
    <a href="#methods">方法</a>
    <a href="#flowcharts">流程图</a>
    <a href="#examples">示例</a>
    <a href="#notes">注意事项</a>
  </div>

  <h1>EditorUndoBar 模块文档</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>EditorUndoBar 是 PDF.js 库中的撤销栏组件，用于在用户执行编辑操作（如添加注释、绘图、高亮等）后提供即时的撤销选项。它在 PDF 编辑过程中显示为一个通知栏，提示用户刚刚完成的操作可以撤销，增强用户体验和编辑灵活性。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 属性 -->
  <div id="properties">
    <h2>属性</h2>
    
    <h3>公共属性</h3>
    <ul>
      <li><code>isOpen</code>: 表示撤销栏是否处于打开状态的布尔值</li>
    </ul>

    <h3>私有属性</h3>
    <ul>
      <li><code>#container</code>: 撤销栏的容器元素</li>
      <li><code>#message</code>: 显示撤销消息的元素</li>
      <li><code>#undoButton</code>: 执行撤销操作的按钮</li>
      <li><code>#closeButton</code>: 关闭撤销栏的按钮</li>
      <li><code>#eventBus</code>: 事件总线，用于事件通信</li>
      <li><code>#initController</code>: 初始化阶段的中止控制器</li>
      <li><code>#showController</code>: 显示状态的中止控制器</li>
      <li><code>#focusTimeout</code>: 聚焦延时的定时器 ID</li>
    </ul>

    <h3>静态属性</h3>
    <ul>
      <li><code>#l10nMessages</code>: 不同编辑操作类型对应的本地化消息 ID 映射</li>
    </ul>
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法</h2>
    
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">构造函数</h3>
      </div>
      <div class="method-content">
        <p>创建一个新的 EditorUndoBar 实例。</p>
        <pre><code class="language-javascript">
constructor({
  container,
  message,
  undoButton,
  closeButton
}, eventBus) {
  this.#container = container;
  this.#message = message;
  this.#undoButton = undoButton;
  this.#closeButton = closeButton;
  this.#eventBus = eventBus;
}
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>container</code>: 撤销栏的容器元素</li>
          <li><code>message</code>: 显示撤销消息的元素</li>
          <li><code>undoButton</code>: 执行撤销操作的按钮</li>
          <li><code>closeButton</code>: 关闭撤销栏的按钮</li>
          <li><code>eventBus</code>: 事件总线实例，用于事件处理</li>
        </ul>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">destroy()</h3>
      </div>
      <div class="method-content">
        <p>销毁撤销栏实例，清理资源并隐藏界面。</p>
        <pre><code class="language-javascript">
destroy() {
  // 中止初始化控制器
  this.#initController?.abort();
  this.#initController = null;
  
  // 隐藏撤销栏
  this.hide();
}
        </code></pre>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">show(undoAction, messageData)</h3>
      </div>
      <div class="method-content">
        <p>显示撤销栏，提供对最近编辑操作的撤销选项。</p>
        <pre><code class="language-javascript">
show(undoAction, messageData) {
  // 如果初始化控制器不存在，进行首次初始化
  if (!this.#initController) {
    // 创建中止控制器
    this.#initController = new AbortController();
    const opts = {
      signal: this.#initController.signal
    };
    
    // 绑定隐藏方法
    const boundHide = this.hide.bind(this);
    
    // 添加事件监听器
    this.#container.addEventListener("contextmenu", noContextMenu, opts);
    this.#closeButton.addEventListener("click", boundHide, opts);
    this.#eventBus._on("beforeprint", boundHide, opts);
    this.#eventBus._on("download", boundHide, opts);
  }
  
  // 先隐藏当前可能显示的撤销栏
  this.hide();
  
  // 设置消息文本
  if (typeof messageData === "string") {
    // 如果是字符串，使用对应的本地化消息ID
    this.#message.setAttribute("data-l10n-id", EditorUndoBar.#l10nMessages[messageData]);
  } else {
    // 如果是数字，使用多个操作的消息格式
    this.#message.setAttribute("data-l10n-id", EditorUndoBar.#l10nMessages._multiple);
    this.#message.setAttribute("data-l10n-args", JSON.stringify({
      count: messageData
    }));
  }
  
  // 更新状态并显示容器
  this.isOpen = true;
  this.#container.hidden = false;
  
  // 创建显示控制器
  this.#showController = new AbortController();
  
  // 添加撤销按钮点击事件
  this.#undoButton.addEventListener("click", () => {
    // 执行撤销操作
    undoAction();
    
    // 隐藏撤销栏
    this.hide();
  }, {
    signal: this.#showController.signal
  });
  
  // 设置延时聚焦，提高可访问性
  this.#focusTimeout = setTimeout(() => {
    this.#container.focus();
    this.#focusTimeout = null;
  }, 100); // 延时100毫秒
}
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>undoAction</code>: 执行撤销操作的回调函数</li>
          <li><code>messageData</code>: 消息数据，字符串表示操作类型（如 'highlight', 'freetext' 等），数字表示多个操作的数量</li>
        </ul>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">hide()</h3>
      </div>
      <div class="method-content">
        <p>隐藏撤销栏并清理相关资源。</p>
        <pre><code class="language-javascript">
hide() {
  // 如果撤销栏未打开，直接返回
  if (!this.isOpen) {
    return;
  }
  
  // 取消聚焦超时
  if (this.#focusTimeout !== null) {
    clearTimeout(this.#focusTimeout);
    this.#focusTimeout = null;
  }
  
  // 中止显示控制器
  this.#showController?.abort();
  this.#showController = null;
  
  // 更新状态并隐藏容器
  this.isOpen = false;
  this.#container.hidden = true;
}
        </code></pre>
      </div>
    </div>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <h3>撤销栏显示流程</h3>
    <p>1. 用户执行编辑操作<br>
    2. 系统准备撤销函数<br>
    3. 调用 show 方法显示撤销栏<br>
    4. 用户可选择撤销或关闭</p>
    
    <div class="mermaid">
      graph TD
        A[用户执行编辑操作] --> B[系统准备撤销函数]
        B --> C[调用 show 方法]
        C --> D{是否首次初始化?}
        D -->|是| E[创建初始化控制器]
        D -->|否| F[隐藏可能已显示的撤销栏]
        E --> G[绑定事件监听器]
        G --> F
        F --> H{消息数据类型?}
        H -->|字符串| I[设置对应操作类型消息]
        H -->|数字| J[设置多操作数量消息]
        I --> K[更新状态并显示容器]
        J --> K
        K --> L[创建显示控制器]
        L --> M[绑定撤销按钮点击事件]
        M --> N[设置延时聚焦]
        N --> O[撤销栏显示完成]
    </div>
    
    <h3>撤销操作流程</h3>
    <p>1. 用户点击撤销按钮<br>
    2. 执行撤销回调函数<br>
    3. 隐藏撤销栏</p>
    
    <div class="mermaid">
      graph TD
        A[用户点击撤销按钮] --> B[触发点击事件处理函数]
        B --> C[执行 undoAction 回调]
        C --> D[调用 hide 方法]
        D --> E[隐藏撤销栏]
        E --> F[撤销操作完成]
    </div>
    
    <h3>撤销栏隐藏流程</h3>
    <p>1. 调用 hide 方法<br>
    2. 清理资源<br>
    3. 隐藏界面元素</p>
    
    <div class="mermaid">
      graph TD
        A[调用 hide 方法] --> B{撤销栏是否打开?}
        B -->|否| C[直接返回]
        B -->|是| D{是否有聚焦超时?}
        D -->|是| E[清除超时定时器]
        D -->|否| F[中止显示控制器]
        E --> F
        F --> G[更新状态为关闭]
        G --> H[隐藏容器元素]
        H --> I[隐藏完成]
    </div>
  </div>

  <!-- 示例 -->
  <div id="examples">
    <h2>使用示例</h2>
    
    <h3>基本用法</h3>
    <pre><code class="language-javascript">
// HTML 结构:
// <div id="editorUndoBar" class="hidden" tabindex="-1">
//   <div class="editorUndoBarInner">
//     <span id="editorUndoBarMessage"></span>
//     <button id="editorUndoBarUndoButton" data-l10n-id="pdfjs-editor-undo-bar-undo-button"></button>
//     <button id="editorUndoBarCloseButton" class="toolbarButton" data-l10n-id="pdfjs-editor-undo-bar-close-button">
//       <span data-l10n-id="pdfjs-editor-undo-bar-close-button-label"></span>
//     </button>
//   </div>
// </div>

// 创建事件总线
const eventBus = new EventBus();

// 获取 DOM 元素
const container = document.getElementById("editorUndoBar");
const message = document.getElementById("editorUndoBarMessage");
const undoButton = document.getElementById("editorUndoBarUndoButton");
const closeButton = document.getElementById("editorUndoBarCloseButton");

// 创建 EditorUndoBar 实例
const editorUndoBar = new EditorUndoBar({
  container,
  message,
  undoButton,
  closeButton
}, eventBus);

// 模拟添加高亮注释并显示撤销栏
function addHighlightAnnotation() {
  // 添加高亮注释的代码...
  
  // 定义撤销操作
  const undoAction = () => {
    console.log("撤销高亮注释");
    // 实际的撤销高亮操作代码...
  };
  
  // 显示撤销栏，使用 'highlight' 类型消息
  editorUndoBar.show(undoAction, "highlight");
}

// 调用添加高亮注释函数
addHighlightAnnotation();
    </code></pre>
    
    <h3>与注释编辑器集成</h3>
    <pre><code class="language-javascript">
class AnnotationEditor {
  constructor(editorUndoBar) {
    this.editorUndoBar = editorUndoBar;
    this.editorType = "freetext"; // 或其他类型
  }
  
  // 添加文本注释
  addTextAnnotation(text, position) {
    // 添加文本注释的代码...
    const annotation = {
      text,
      position,
      id: `text_${Date.now()}`
    };
    
    // 保存到注释列表
    this.annotations = this.annotations || [];
    this.annotations.push(annotation);
    
    // 创建撤销函数
    const undoAction = () => {
      // 从列表中移除注释
      const index = this.annotations.findIndex(a => a.id === annotation.id);
      if (index !== -1) {
        this.annotations.splice(index, 1);
      }
      
      // 从视图中删除注释
      this.removeAnnotationFromView(annotation.id);
    };
    
    // 显示撤销栏
    this.editorUndoBar.show(undoAction, this.editorType);
    
    return annotation;
  }
  
  // 从视图中移除注释
  removeAnnotationFromView(id) {
    // 移除注释元素的代码...
    console.log(`已移除注释: ${id}`);
  }
}

// 使用示例
const editor = new AnnotationEditor(editorUndoBar);
editor.addTextAnnotation("这是一个注释", { x: 100, y: 200 });
    </code></pre>
    
    <h3>处理多个操作</h3>
    <pre><code class="language-javascript">
class AnnotationEditorUIManager {
  constructor() {
    // 获取 DOM 元素
    const container = document.getElementById("editorUndoBar");
    const message = document.getElementById("editorUndoBarMessage");
    const undoButton = document.getElementById("editorUndoBarUndoButton");
    const closeButton = document.getElementById("editorUndoBarCloseButton");
    
    // 创建事件总线
    this.eventBus = new EventBus();
    
    // 创建撤销栏
    this._editorUndoBar = new EditorUndoBar({
      container,
      message,
      undoButton,
      closeButton
    }, this.eventBus);
    
    // 已选中的编辑器
    this.selectedEditors = new Set();
  }
  
  // 选择编辑器
  selectEditor(editor) {
    this.selectedEditors.add(editor);
  }
  
  // 取消选择编辑器
  deselectEditor(editor) {
    this.selectedEditors.delete(editor);
  }
  
  // 删除选中的编辑器
  deleteSelectedEditors() {
    // 如果没有选中的编辑器，直接返回
    if (this.selectedEditors.size === 0) {
      return;
    }
    
    // 复制选中的编辑器列表
    const editorsToDelete = [...this.selectedEditors];
    
    // 创建撤销函数
    const undoAction = () => {
      console.log(`撤销删除 ${editorsToDelete.length} 个编辑器`);
      
      // 恢复已删除的编辑器
      for (const editor of editorsToDelete) {
        this.restoreEditor(editor);
      }
    };
    
    // 执行删除
    for (const editor of editorsToDelete) {
      this.removeEditor(editor);
    }
    
    // 清空选中集合
    this.selectedEditors.clear();
    
    // 显示撤销栏，传入删除的数量
    this._editorUndoBar.show(undoAction, editorsToDelete.length);
  }
  
  // 移除编辑器
  removeEditor(editor) {
    // 删除编辑器的代码...
    console.log(`删除编辑器: ${editor.id}`);
  }
  
  // 恢复编辑器
  restoreEditor(editor) {
    // 恢复编辑器的代码...
    console.log(`恢复编辑器: ${editor.id}`);
  }
}

// 使用示例
const uiManager = new AnnotationEditorUIManager();

// 模拟选择三个编辑器
uiManager.selectEditor({ id: "editor1", type: "highlight" });
uiManager.selectEditor({ id: "editor2", type: "freetext" });
uiManager.selectEditor({ id: "editor3", type: "ink" });

// 删除选中的编辑器并显示撤销栏
uiManager.deleteSelectedEditors();
    </code></pre>
  </div>

  <!-- 注意事项 -->
  <div id="notes">
    <h2>注意事项</h2>
    
    <ul>
      <li>EditorUndoBar 组件需要特定的 HTML 结构才能正常工作，包括容器、消息元素、撤销按钮和关闭按钮。</li>
      <li>组件使用本地化消息 ID 来支持多语言界面，确保这些 ID 与应用的本地化系统匹配。</li>
      <li>当在短时间内多次调用 show 方法时，组件会自动处理先隐藏当前撤销栏再显示新撤销栏的过程。</li>
      <li>撤销栏会在特定事件发生时自动隐藏，如打印前（beforeprint）和下载时（download）。</li>
      <li>组件使用延时聚焦（100毫秒）来提高可访问性，确保屏幕阅读器能够识别撤销栏的出现。</li>
      <li>通过 AbortController 管理事件监听器，确保在隐藏撤销栏时正确清理资源，避免内存泄漏。</li>
      <li>撤销栏支持两种消息数据类型：字符串（表示单一操作类型）和数字（表示多个操作的数量）。</li>
      <li>组件的设计遵循即时反馈原则，在用户执行操作后立即提供撤销选项，而不是依赖传统的撤销菜单或快捷键。</li>
      <li>在移动设备上使用时，应确保撤销栏不会被虚拟键盘遮挡，可能需要调整其位置。</li>
    </ul>
  </div>

  <script>
    // 在页面加载完成后初始化 Mermaid
    document.addEventListener('DOMContentLoaded', function() {
      mermaid.initialize({ startOnLoad: true });
      
      // 生成目录
      createTableOfContents();
    });
  </script>
</body>
</html> 