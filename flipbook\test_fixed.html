<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Native_Fixed.js 测试 - 完全兼容原版的3D翻页效果</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            text-align: center;
        }

        h1 {
            color: #333;
            margin-bottom: 30px;
        }

        #flipbook {
            margin: 50px auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            border-radius: 10px;
            overflow: hidden;
            background: white;
        }

        .page {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            position: relative;
        }

        .page:nth-child(even) {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .page:nth-child(3n) {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .page:nth-child(4n) {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .page-number {
            position: absolute;
            bottom: 20px;
            right: 20px;
            font-size: 18px;
            opacity: 0.8;
        }

        .page-content {
            text-align: center;
        }

        .page-title {
            font-size: 32px;
            margin-bottom: 20px;
        }

        .page-text {
            font-size: 16px;
            max-width: 300px;
            line-height: 1.6;
            opacity: 0.9;
        }

        .controls {
            margin: 30px 0;
        }

        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 0 10px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .info {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .status {
            font-weight: bold;
            color: #667eea;
        }

        .instructions {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }

        .instructions h3 {
            margin-top: 0;
            color: #667eea;
        }

        .instructions ul {
            text-align: left;
            margin: 10px 0;
        }

        .error {
            background: #ffe6e6;
            color: #d63031;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #d63031;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Native_Fixed.js 测试 - 完全兼容原版的3D翻页效果</h1>
        
        <div class="instructions">
            <h3>使用说明</h3>
            <ul>
                <li>拖拽页面角落可以翻页（与原版index.js完全一致）</li>
                <li>支持真实的3D翻页动画效果</li>
                <li>支持渐变阴影效果</li>
                <li>使用下方按钮控制翻页</li>
                <li>支持键盘左右箭头键翻页</li>
            </ul>
        </div>

        <div id="flipbook">
            <div class="page">
                <div class="page-content">
                    <div class="page-title">欢迎</div>
                    <div class="page-text">这是与原版index.js完全兼容的3D翻页效果</div>
                </div>
                <div class="page-number">1</div>
            </div>
            <div class="page">
                <div class="page-content">
                    <div class="page-title">3D效果</div>
                    <div class="page-text">✓ 真实的3D数学计算<br>✓ 完整的折叠动画<br>✓ 渐变阴影效果<br>✓ 硬件加速</div>
                </div>
                <div class="page-number">2</div>
            </div>
            <div class="page">
                <div class="page-content">
                    <div class="page-title">兼容性</div>
                    <div class="page-text">与原版jQuery版本<br>功能完全一致<br>无任何依赖</div>
                </div>
                <div class="page-number">3</div>
            </div>
            <div class="page">
                <div class="page-content">
                    <div class="page-title">交互</div>
                    <div class="page-text">支持鼠标拖拽<br>支持触摸操作<br>支持角落翻页</div>
                </div>
                <div class="page-number">4</div>
            </div>
            <div class="page">
                <div class="page-content">
                    <div class="page-title">性能</div>
                    <div class="page-text">使用CSS3变换<br>硬件加速<br>流畅的动画</div>
                </div>
                <div class="page-number">5</div>
            </div>
            <div class="page">
                <div class="page-content">
                    <div class="page-title">完成</div>
                    <div class="page-text">感谢使用Native_Fixed.js<br>完全兼容原版的翻页库</div>
                </div>
                <div class="page-number">6</div>
            </div>
        </div>

        <div class="controls">
            <button class="btn" onclick="previousPage()">← 上一页</button>
            <button class="btn" onclick="nextPage()">下一页 →</button>
            <button class="btn" onclick="goToPage(1)">首页</button>
            <button class="btn" onclick="goToPage(3)">第3页</button>
        </div>

        <div class="info">
            <div class="status" id="status">正在初始化...</div>
        </div>

        <div class="error" id="error">
            <strong>错误：</strong><span id="error-message"></span>
        </div>
    </div>

    <script src="Native_Fixed.js"></script>
    <script>
        let flipbook;
        let currentPage = 1;

        // 显示错误信息
        function showError(message) {
            document.getElementById('error-message').textContent = message;
            document.getElementById('error').style.display = 'block';
        }

        // 更新状态
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }

        // 初始化翻页书
        function initFlipbook() {
            try {
                flipbook = document.getElementById('flipbook');
                
                // 检查Native_Fixed.js是否加载
                if (typeof TurnJS === 'undefined') {
                    throw new Error('Native_Fixed.js 未正确加载');
                }

                // 初始化翻页效果 - 与原版参数完全一致
                flipbook.turn({
                    width: 800,
                    height: 600,
                    display: 'double',
                    gradients: true,
                    duration: 600,
                    acceleration: true,
                    pages: 6,
                    when: {
                        turning: function(event, page, view) {
                            updateStatus(`正在翻到第 ${page} 页...`);
                        },
                        turned: function(event, page, view) {
                            currentPage = page;
                            updateStatus(`当前页面：第 ${page} 页`);
                        },
                        start: function(event, pageObject, corner) {
                            updateStatus('开始翻页...');
                        },
                        end: function(event, pageObject) {
                            updateStatus(`翻页完成，当前第 ${currentPage} 页`);
                        }
                    }
                });

                updateStatus('翻页书初始化成功！当前第 1 页 - 可以拖拽页面角落翻页');

            } catch (error) {
                console.error('初始化错误:', error);
                showError(error.message);
                updateStatus('初始化失败');
            }
        }

        // 翻页控制函数
        function nextPage() {
            try {
                if (flipbook && flipbook.turn) {
                    flipbook.turn('next');
                }
            } catch (error) {
                showError('翻页失败: ' + error.message);
            }
        }

        function previousPage() {
            try {
                if (flipbook && flipbook.turn) {
                    flipbook.turn('previous');
                }
            } catch (error) {
                showError('翻页失败: ' + error.message);
            }
        }

        function goToPage(page) {
            try {
                if (flipbook && flipbook.turn) {
                    flipbook.turn('page', page);
                }
            } catch (error) {
                showError('跳转失败: ' + error.message);
            }
        }

        // 键盘控制
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') {
                previousPage();
            } else if (e.key === 'ArrowRight') {
                nextPage();
            }
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(initFlipbook, 100);
        });
    </script>
</body>
</html>
