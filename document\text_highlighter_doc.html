<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>TextHighlighter 文档 - PDF.js</title>
  <link rel="stylesheet" href="doc_styles.css">
</head>
<body>
  <header>
    <h1>TextHighlighter</h1>
    <p class="module-description">文本高亮器，负责PDF中文本搜索结果的高亮显示</p>
    <a href="index.html" class="back-link">返回模块列表</a>
  </header>

  <div class="content">
    <section class="module-intro">
      <h2>模块介绍</h2>
      <p>TextHighlighter 是 PDF.js 中负责处理文本搜索结果高亮显示的组件。它与查找控制器（FindController）紧密协作，将搜索匹配项在文本层中以高亮方式呈现，帮助用户快速定位和识别搜索结果。文本高亮器会跟踪文本内容和文本 div 元素，在查找匹配项时动态地添加高亮样式，并处理匹配项的选择和滚动操作。</p>
      
      <div class="module-diagram">
        <h3>组件位置与工作流程</h3>
        <div class="mermaid">
          graph TD
            PDFViewer[PDFViewer] --> PDFPageView[PDFPageView]
            PDFPageView --> TextLayer[TextLayer]
            TextLayer --> TextHighlighter[TextHighlighter]
            PDFViewer --> FindController[FindController]
            FindController -->|匹配结果| TextHighlighter
            EventBus -->|updatetextlayermatches| TextHighlighter
            TextHighlighter -->|高亮样式| TextLayer
        </div>
      </div>
    </section>

    <section class="properties">
      <h2>属性</h2>
      
      <h3>公共属性</h3>
      <div class="property">
        <h4>findController</h4>
        <p>查找控制器实例，提供搜索匹配结果。</p>
      </div>

      <div class="property">
        <h4>matches</h4>
        <p>当前页面的匹配结果数组，包含每个匹配项的位置信息。</p>
      </div>

      <div class="property">
        <h4>eventBus</h4>
        <p>事件总线实例，用于接收搜索结果更新的事件。</p>
      </div>

      <div class="property">
        <h4>pageIdx</h4>
        <p>当前页面的索引，从 0 开始。</p>
      </div>

      <div class="property">
        <h4>textDivs</h4>
        <p>文本 div 元素数组，包含页面上的所有文本元素。</p>
      </div>

      <div class="property">
        <h4>textContentItemsStr</h4>
        <p>文本内容字符串数组，与 textDivs 对应。</p>
      </div>

      <div class="property">
        <h4>enabled</h4>
        <p>标志位，表示文本高亮器是否已启用。</p>
      </div>

      <h3>私有属性</h3>
      <div class="property">
        <h4>#eventAbortController</h4>
        <p>事件中止控制器，用于在禁用高亮器时中止事件监听。</p>
      </div>
    </section>

    <section class="methods">
      <h2>方法</h2>

      <div class="method">
        <h3>constructor({ findController, eventBus, pageIndex })</h3>
        <p>创建文本高亮器实例。</p>
        <h4>参数：</h4>
        <ul>
          <li><code>findController</code> - 查找控制器实例</li>
          <li><code>eventBus</code> - 事件总线实例</li>
          <li><code>pageIndex</code> - 当前页面的索引</li>
        </ul>
        
        <pre><code>// 创建文本高亮器
const textHighlighter = new TextHighlighter({
  findController,
  eventBus,
  pageIndex: pageIdx
});</code></pre>
      </div>

      <div class="method">
        <h3>setTextMapping(divs, texts)</h3>
        <p>设置文本映射，将文本 div 元素和文本内容字符串数组保存到高亮器中。</p>
        <h4>参数：</h4>
        <ul>
          <li><code>divs</code> - 文本 div 元素数组</li>
          <li><code>texts</code> - 文本内容字符串数组</li>
        </ul>
        
        <pre><code>// 设置文本映射
textHighlighter.setTextMapping(textDivs, textContentItemsStr);</code></pre>
      </div>

      <div class="method">
        <h3>enable()</h3>
        <p>启用文本高亮功能，开始监听匹配更新事件并立即更新匹配项。</p>
        <pre><code>// 启用文本高亮器
textHighlighter.enable();</code></pre>

        <div class="mermaid">
          sequenceDiagram
            participant App as 应用程序
            participant TH as TextHighlighter
            participant EB as EventBus
            
            App->>TH: enable()
            alt 未设置textDivs或textContentItemsStr
              TH-->>App: 抛出错误
            else 已经启用
              TH-->>App: 抛出错误
            else 正常启用
              TH->>TH: enabled = true
              alt 没有事件中止控制器
                TH->>TH: 创建事件中止控制器
                TH->>EB: 监听updatetextlayermatches事件
              end
              TH->>TH: _updateMatches()
            end
        </div>
      </div>

      <div class="method">
        <h3>disable()</h3>
        <p>禁用文本高亮功能，停止监听匹配更新事件并清除高亮效果。</p>
        <pre><code>// 禁用文本高亮器
textHighlighter.disable();</code></pre>
      </div>

      <div class="method">
        <h3>_convertMatches(matches, matchesLength)</h3>
        <p>私有方法，将查找控制器中的匹配项索引转换为文本层中的位置信息。</p>
        <h4>参数：</h4>
        <ul>
          <li><code>matches</code> - 匹配项索引数组</li>
          <li><code>matchesLength</code> - 匹配项长度数组</li>
        </ul>
        <h4>返回：</h4>
        <p>转换后的匹配对象数组，每个对象包含 begin 和 end 位置信息</p>
      </div>

      <div class="method">
        <h3>_renderMatches(matches)</h3>
        <p>私有方法，渲染匹配结果，为匹配文本添加高亮样式。</p>
        <h4>参数：</h4>
        <ul>
          <li><code>matches</code> - 匹配对象数组</li>
        </ul>
      </div>

      <div class="method">
        <h3>_updateMatches(reset = false)</h3>
        <p>私有方法，更新匹配项，清除旧的高亮效果并添加新的高亮效果。</p>
        <h4>参数：</h4>
        <ul>
          <li><code>reset</code> - 是否重置模式，如果为 true，则只清除高亮效果不添加新的高亮</li>
        </ul>
      </div>
    </section>

    <section class="usage">
      <h2>使用示例</h2>

      <div class="example">
        <h3>基本用法</h3>
        <pre><code>// 创建文本高亮器
const textHighlighter = new TextHighlighter({
  findController,
  eventBus,
  pageIndex: pageIdx
});

// 设置文本映射
textHighlighter.setTextMapping(textDivs, textContentItemsStr);

// 启用文本高亮器
textHighlighter.enable();

// 当不再需要高亮时禁用
textHighlighter.disable();</code></pre>
      </div>

      <div class="example">
        <h3>与 TextLayer 结合使用</h3>
        <pre><code>// 在 TextLayerBuilder 中使用 TextHighlighter
class TextLayerBuilder {
  constructor(options) {
    // ...其他初始化代码...
    
    this.highlighter = options.highlighter;
  }
  
  async render({ viewport, textContentParams = null }) {
    // ...渲染文本层...
    
    const { textDivs, textContentItemsStr } = this.#textLayer;
    
    // 为高亮器设置文本映射
    this.highlighter?.setTextMapping(textDivs, textContentItemsStr);
    
    // ...完成渲染...
    
    // 启用高亮器
    this.highlighter?.enable();
  }
  
  hide() {
    if (!this.div.hidden && this.#renderingDone) {
      // 隐藏时禁用高亮器
      this.highlighter?.disable();
      this.div.hidden = true;
    }
  }
  
  show() {
    if (this.div.hidden && this.#renderingDone) {
      this.div.hidden = false;
      // 显示时重新启用高亮器
      this.highlighter?.enable();
    }
  }
}</code></pre>
      </div>

      <div class="example">
        <h3>与 PDFPageView 结合使用</h3>
        <pre><code>// 在 PDFPageView 中使用 TextHighlighter
class PDFPageView {
  constructor(options) {
    // ...其他初始化代码...
    
    // 创建文本高亮器
    this._textHighlighter = new TextHighlighter({
      pageIndex: this.id - 1,
      eventBus: this.eventBus,
      findController: options.findController
    });
  }
  
  // 渲染文本层时使用高亮器
  _renderTextLayer() {
    const textLayer = new TextLayerBuilder({
      highlighter: this._textHighlighter,
      // ...其他选项...
    });
    
    // ...渲染文本层...
  }
  
  // 处理XFA文本内容
  async #buildXfaTextContentItems(textDivs) {
    const text = await this.pdfPage.getTextContent();
    const items = [];
    for (const item of text.items) {
      items.push(item.str);
    }
    
    // 设置文本高亮器的文本映射
    this._textHighlighter.setTextMapping(textDivs, items);
    
    // 启用文本高亮器
    this._textHighlighter.enable();
  }
}</code></pre>
      </div>
    </section>

    <section class="implementation-notes">
      <h2>实现说明</h2>
      <div class="note">
        <h3>工作原理</h3>
        <p>TextHighlighter 的工作过程如下：</p>
        <ol>
          <li>初始化时，接收查找控制器、事件总线和页面索引</li>
          <li>通过 setTextMapping 方法，接收文本 div 元素和文本内容字符串数组</li>
          <li>启用后，开始监听 "updatetextlayermatches" 事件，该事件在搜索匹配结果变化时触发</li>
          <li>当收到事件或调用 enable() 时，调用 _updateMatches() 方法进行处理：
            <ul>
              <li>首先清除旧的匹配高亮效果</li>
              <li>从查找控制器获取当前页的匹配项</li>
              <li>通过 _convertMatches() 方法将匹配项索引转换为文本层中的位置信息</li>
              <li>通过 _renderMatches() 方法为匹配文本添加高亮样式</li>
            </ul>
          </li>
          <li>禁用时，中止事件监听并清除所有高亮效果</li>
        </ol>
      </div>
      
      <div class="note">
        <h3>匹配项转换</h3>
        <p>_convertMatches 方法将查找控制器中的匹配索引（基于整个文本的偏移量）转换为文本层中的位置信息（基于 div 索引和在 div 内的偏移量）。转换过程需要遍历文本内容，累加每个文本项的长度，确定匹配项在哪个 div 中以及在该 div 中的具体位置。</p>
        <p>转换后的匹配对象结构如下：</p>
        <pre><code>{
  begin: {
    divIdx: 数字,  // 匹配项开始位置所在的 div 索引
    offset: 数字   // 在该 div 中的偏移量
  },
  end: {
    divIdx: 数字,  // 匹配项结束位置所在的 div 索引
    offset: 数字   // 在该 div 中的偏移量
  }
}</code></pre>
      </div>
      
      <div class="note">
        <h3>渲染匹配项</h3>
        <p>_renderMatches 方法负责实际渲染匹配项。它会根据匹配项的位置信息，在文本层中添加高亮样式：</p>
        <ul>
          <li>如果匹配项在单个 div 中，为该 div 中的匹配文本添加 "highlight" 类</li>
          <li>如果匹配项跨越多个 div，为开始 div 添加 "highlight begin" 类，中间 div 添加 "highlight middle" 类，结束 div 添加 "highlight end" 类</li>
          <li>当前选中的匹配项还会额外添加 " selected" 类后缀，以区分其他匹配项</li>
        </ul>
        <p>渲染过程中，会创建新的 span 元素包裹匹配文本，并为其添加适当的类名，以实现高亮效果。</p>
      </div>
    </section>

    <section class="best-practices">
      <h2>最佳实践</h2>
      <div class="practice">
        <h3>生命周期管理</h3>
        <p>TextHighlighter 的生命周期管理非常重要：</p>
        <ul>
          <li>在文本层渲染完成后，先调用 setTextMapping 设置文本映射</li>
          <li>然后调用 enable 启用高亮功能</li>
          <li>在页面隐藏或不需要高亮时调用 disable 释放资源</li>
          <li>在页面销毁前确保调用 disable 清理事件监听器，避免内存泄漏</li>
        </ul>
      </div>
      
      <div class="practice">
        <h3>性能优化</h3>
        <p>文本高亮处理可能在大型文档中影响性能，特别是有大量匹配项时：</p>
        <ul>
          <li>仅在可见页面上启用文本高亮器</li>
          <li>在页面滚动或缩放等操作期间，可以暂时禁用高亮器，操作结束后再重新启用</li>
          <li>避免不必要的高亮更新，只在匹配结果真正变化时才触发 _updateMatches</li>
        </ul>
      </div>
      
      <div class="practice">
        <h3>错误处理</h3>
        <p>使用 TextHighlighter 时，注意以下可能的错误情况：</p>
        <ul>
          <li>在调用 setTextMapping 前调用 enable 方法会抛出错误</li>
          <li>重复调用 enable 方法会抛出错误</li>
          <li>如果查找控制器中的匹配项索引超出了文本内容范围，_convertMatches 方法可能会输出错误消息</li>
        </ul>
      </div>
      
      <div class="practice">
        <h3>无障碍性</h3>
        <p>高亮功能对于无障碍访问很重要：</p>
        <ul>
          <li>确保高亮样式同时有颜色和背景对比，以适应色盲用户</li>
          <li>在添加高亮样式时，不要改变文本的原始内容和顺序，以确保屏幕阅读器正确朗读</li>
          <li>与 TextAccessibilityManager 协作，确保高亮内容的可访问性</li>
        </ul>
      </div>
    </section>
  </div>

  <script src="doc_script.js"></script>
  <script src="js/mermaid.js"></script>
  <script>
    mermaid.initialize({ startOnLoad: true, theme: 'neutral' });
  </script>
</body>
</html> 