<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PDFViewerApplication - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#methods">方法列表</a>
    <a href="#flowcharts">流程图</a>
  </div>

  <h1>PDFViewerApplication</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>PDFViewerApplication是PDF.js的核心应用程序对象，作为整个PDF查看器的控制中心。它协调所有组件的初始化和交互，管理文档加载、渲染、用户界面和各种操作。该对象负责创建和维护诸如PDF查看器、链接服务、缩略图查看器、大纲查看器、工具栏等所有PDF.js组件，并处理用户输入、导航和打印等功能。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>主要属性</h2>
    
    <!-- 核心组件属性 -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">核心组件属性</h3>
      </div>
      <div class="method-content">
        <p>PDFViewerApplication包含以下核心组件属性：</p>
        <ul>
          <li><strong>pdfDocument</strong> - 当前加载的PDF文档对象</li>
          <li><strong>pdfViewer</strong> - 负责渲染和显示PDF页面的查看器组件</li>
          <li><strong>pdfLinkService</strong> - 处理PDF内部和外部链接的服务</li>
          <li><strong>pdfRenderingQueue</strong> - 管理PDF页面渲染队列和优先级</li>
          <li><strong>pdfThumbnailViewer</strong> - 显示PDF页面缩略图的组件</li>
          <li><strong>pdfOutlineViewer</strong> - 显示PDF文档大纲结构的组件</li>
          <li><strong>pdfSidebar</strong> - 管理侧边栏和其中的各个面板</li>
          <li><strong>pdfHistory</strong> - 管理浏览历史和状态</li>
          <li><strong>eventBus</strong> - 用于组件间通信的事件总线</li>
          <li><strong>l10n</strong> - 处理界面本地化的对象</li>
        </ul>
      </div>
    </div>
    
    <!-- 状态和配置属性 -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">状态和配置属性</h3>
      </div>
      <div class="method-content">
        <p>这些属性存储应用程序的状态和配置信息：</p>
        <ul>
          <li><strong>appConfig</strong> - 包含UI元素引用和配置的对象</li>
          <li><strong>url</strong> - 当前加载的PDF文档URL</li>
          <li><strong>baseUrl</strong> - 基础URL，用于相对路径解析</li>
          <li><strong>documentInfo</strong> - PDF文档元信息</li>
          <li><strong>metadata</strong> - PDF文档元数据</li>
          <li><strong>isViewerEmbedded</strong> - 查看器是否作为嵌入式组件运行</li>
          <li><strong>initialBookmark</strong> - 初始书签（从URL哈希获取）</li>
        </ul>
      </div>
    </div>

    <h2>主要方法</h2>
    
    <!-- initialize方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">async initialize(appConfig)</h3>
      </div>
      <div class="method-content">
        <p>初始化PDF查看器应用程序，设置应用配置并初始化所有必要的组件。</p>
        <pre><code class="language-javascript">
async initialize(appConfig) { // 初始化方法
  this.appConfig = appConfig; // 设置应用程序配置
  try { // 尝试
    await this.preferences.initializedPromise; // 等待首选项初始化
  } catch (ex) { // 捕获可能的错误
    console.error("initialize:", ex); // 输出初始化错误
  }
  
  // 设置CSS主题
  // 初始化本地化服务
  // 初始化查看器组件
  // 绑定事件
  
  this._initializedCapability.settled = true; // 设置初始化能力已解决
  this._initializedCapability.resolve(); // 解析初始化能力
}
        </code></pre>
      </div>
    </div>
    
    <!-- _initializeViewerComponents方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">async _initializeViewerComponents()</h3>
      </div>
      <div class="method-content">
        <p>初始化PDF查看器的所有组件，包括事件总线、链接服务、渲染队列、查找控制器等。</p>
        <pre><code class="language-javascript">
async _initializeViewerComponents() { // 初始化查看器组件的异步方法
  const eventBus = new EventBus(); // 创建事件总线
  this.eventBus = AppOptions.eventBus = eventBus; // 设置事件总线
  
  // 创建各种组件：
  // - 覆盖层管理器
  // - 渲染队列
  // - 链接服务
  // - 查找控制器
  // - 侧边栏与大纲查看器
  // - 查看器和缩略图查看器
  // - 工具栏等
}
        </code></pre>
      </div>
    </div>
    
    <!-- run方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">async run(config)</h3>
      </div>
      <div class="method-content">
        <p>运行PDF查看器应用程序，初始化并加载文档。</p>
        <pre><code class="language-javascript">
async run(config) { // 运行应用程序
  await this.initialize(config); // 等待初始化完成
  
  // 处理URL参数
  // 设置文件输入和拖放处理
  // 检查功能支持
  
  if (file) { // 如果有文件
    this.open({ // 打开文件
      url: file // 文件URL
    });
  } else { // 否则
    this._hideViewBookmark(); // 隐藏视图书签
  }
}
        </code></pre>
      </div>
    </div>
    
    <!-- open方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">async open(params)</h3>
      </div>
      <div class="method-content">
        <p>打开并加载PDF文档，处理加载过程和错误情况。</p>
        <pre><code class="language-javascript">
async open(params) {
  // 处理加载参数
  // 取消之前的加载任务
  // 显示加载进度条
  
  try {
    // 创建PDF加载任务
    // 加载文档
    // 初始化页面视图
    // 设置事件监听器
    // 处理初始视图设置
  } catch (reason) {
    // 处理加载错误
  }
}
        </code></pre>
      </div>
    </div>
    
    <!-- 缩放相关方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">缩放控制方法</h3>
      </div>
      <div class="method-content">
        <p>用于控制PDF文档缩放的方法：</p>
        <pre><code class="language-javascript">
// 更新缩放比例
updateZoom(steps, scaleFactor, origin) {
  if (this.pdfViewer.isInPresentationMode) { // 如果在演示模式中
    return; // 不执行缩放
  }
  this.pdfViewer.updateScale({
    drawingDelay: AppOptions.get("defaultZoomDelay"),
    steps,
    scaleFactor,
    origin
  });
}

// 放大
zoomIn() {
  this.updateZoom(1); // 更新缩放，步数为1
}

// 缩小
zoomOut() {
  this.updateZoom(-1); // 更新缩放，步数为-1
}

// 重置缩放
zoomReset() {
  if (this.pdfViewer.isInPresentationMode) {
    return;
  }
  this.pdfViewer.currentScaleValue = DEFAULT_SCALE_VALUE;
}
        </code></pre>
      </div>
    </div>
    
    <!-- 功能支持检测 -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">功能支持检测</h3>
      </div>
      <div class="method-content">
        <p>检测浏览器环境对各种功能的支持情况：</p>
        <pre><code class="language-javascript">
get supportsPrinting() {
  return shadow(this, "supportsPrinting", 
    AppOptions.get("supportsPrinting") && PDFPrintServiceFactory.supportsPrinting);
}

get supportsFullscreen() {
  return shadow(this, "supportsFullscreen", document.fullscreenEnabled);
}

get supportsPinchToZoom() {
  return shadow(this, "supportsPinchToZoom", 
    AppOptions.get("supportsPinchToZoom"));
}

get supportsIntegratedFind() {
  return shadow(this, "supportsIntegratedFind", 
    AppOptions.get("supportsIntegratedFind"));
}
        </code></pre>
      </div>
    </div>
    
    <!-- 事件处理方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">事件处理方法</h3>
      </div>
      <div class="method-content">
        <p>用于绑定和处理各种应用程序事件的方法：</p>
        <pre><code class="language-javascript">
bindEvents() {
  // 绑定PDF相关事件
  const { eventBus } = this;
  eventBus.on("pagesinit", this._onDocumentPagesInit);
  eventBus.on("docattachments", this._onDocumentAttachments);
  // ...其他事件绑定
}

bindWindowEvents() {
  // 绑定窗口级事件
  const { eventBus, _boundEvents } = this;
  _boundEvents.windowResize = () => {
    eventBus.dispatch("resize", { source: window });
  };
  // ...其他窗口事件绑定
}
        </code></pre>
      </div>
    </div>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <!-- 应用程序初始化流程图 -->
    <div class="mermaid">
      graph TD
        A["开始初始化"] --> B["加载应用配置"]
        B --> C["初始化首选项"]
        C --> D["解析URL参数"]
        D --> E["设置主题"]
        E --> F["创建本地化服务"]
        F --> G["初始化查看器组件"]
        G --> H["创建事件总线"]
        H --> I["创建各种服务和组件"]
        I --> J["绑定事件"]
        J --> K["初始化完成"]
    </div>
    
    <!-- 文档加载流程图 -->
    <div class="mermaid">
      graph TD
        A["调用open方法"] --> B["准备加载参数"]
        B --> C["取消之前的加载任务"]
        C --> D["显示加载进度条"]
        D --> E["创建PDF加载任务"]
        E --> F["加载PDF文档"]
        F --> G["成功?"]
        G -->|"是"| H["初始化文档视图"]
        G -->|"否"| I["显示错误信息"]
        H --> J["设置初始缩放"]
        J --> K["设置初始页面"]
        K --> L["处理哈希参数"]
        L --> M["完成加载"]
        I --> N["加载失败"]
    </div>
    
    <!-- 组件关系图 -->
    <div class="mermaid">
      graph LR
        A["PDFViewerApplication"] --> B["EventBus"]
        A --> C["PDFLinkService"]
        A --> D["PDFViewer"]
        A --> E["PDFRenderingQueue"]
        A --> F["PDFSidebar"]
        A --> G["PDFThumbnailViewer"]
        A --> H["PDFOutlineViewer"]
        A --> I["PDFAttachmentViewer"]
        A --> J["PDFLayerViewer"]
        A --> K["PDFHistory"]
        A --> L["PDFFindController"]
        A --> M["Toolbar & SecondaryToolbar"]
        A --> N["PasswordPrompt"]
        A --> O["PDFPresentationMode"]
    </div>
  </div>

  <!-- 使用示例 -->
  <div id="usage-example">
    <h2>使用示例</h2>
    <p>下面是PDFViewerApplication的典型使用方式：</p>
    <pre><code class="language-javascript">
// 配置PDF.js应用程序
const appConfig = {
  mainContainer: document.getElementById("viewerContainer"),
  viewerContainer: document.getElementById("viewer"),
  toolbar: {
    container: document.getElementById("toolbarViewer"),
    zoomIn: document.getElementById("zoomIn"),
    zoomOut: document.getElementById("zoomOut"),
    // ... 其他工具栏元素
  },
  sidebar: {
    // 侧边栏元素配置
    container: document.getElementById("sidebarContainer"),
    outlineView: document.getElementById("outlineView"),
    attachmentsView: document.getElementById("attachmentsView"),
    layersView: document.getElementById("layersView")
  },
  // ... 其他配置
};

// 运行应用程序
PDFViewerApplication.run(appConfig);

// 加载PDF文档
PDFViewerApplication.open({
  url: "document.pdf"
});

// 使用应用程序功能
document.getElementById("zoomIn").addEventListener("click", function() {
  PDFViewerApplication.zoomIn();
});

document.getElementById("print").addEventListener("click", function() {
  PDFViewerApplication.print();
});

document.getElementById("download").addEventListener("click", function() {
  PDFViewerApplication.download();
});
    </code></pre>
  </div>

  <!-- 实现细节 -->
  <div id="implementation-details">
    <h2>实现细节</h2>
    <p>PDFViewerApplication的实现采用了以下设计策略：</p>
    <ul>
      <li><strong>模块化设计</strong>：应用程序被分解为多个专门的组件，每个组件负责特定功能。</li>
      <li><strong>事件驱动架构</strong>：使用EventBus进行组件间的松耦合通信，允许组件独立运行并通过事件响应状态变化。</li>
      <li><strong>渐进式渲染</strong>：通过PDFRenderingQueue实现页面渲染的优先级管理，确保可见页面首先渲染。</li>
      <li><strong>延迟初始化</strong>：许多组件和功能只在需要时才被初始化，以优化启动性能。</li>
      <li><strong>功能检测</strong>：通过getter方法实现的功能检测，确定浏览器环境支持的功能（如打印、全屏等）。</li>
      <li><strong>配置驱动</strong>：通过AppOptions系统提供广泛的配置选项，允许定制PDF查看器的行为。</li>
    </ul>
  </div>

  <!-- 注意事项 -->
  <div id="notes">
    <h2>注意事项</h2>
    <p>使用PDFViewerApplication时需要注意以下事项：</p>
    <ul>
      <li>PDFViewerApplication是单例对象，在一个页面中只应有一个实例。</li>
      <li>初始化时必须提供正确配置的appConfig对象，包含所有必要的UI元素引用。</li>
      <li>在调用任何方法之前，应确保通过initializedPromise检查应用程序是否已完成初始化。</li>
      <li>某些功能（如打印、全屏）可能受到浏览器限制，应通过支持检测方法确认其可用性。</li>
      <li>要扩展或自定义PDF查看器，可以在初始化后修改PDFViewerApplication的组件或使用事件总线监听事件。</li>
      <li>PDF.js在浏览器兼容性方面有一定限制，在使用高级功能前应检查浏览器支持情况。</li>
    </ul>
  </div>

  <!-- 返回顶部按钮 -->
  <button class="back-to-top">↑</button>

  <script>
    // 创建目录
    createTableOfContents();
  </script>
</body>
</html> 