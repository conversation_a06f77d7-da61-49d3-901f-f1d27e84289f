<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>EventBus - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#constants">常量</a>
    <a href="#properties">属性</a>
    <a href="#methods">方法</a>
    <a href="#flowcharts">流程图</a>
    <a href="#examples">示例</a>
    <a href="#notes">注意事项</a>
  </div>

  <h1>EventBus 模块文档</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>EventBus 是 PDF.js 库中的事件总线组件，提供了一个中心化的事件分发机制。它允许 PDF.js 中的不同组件通过发布-订阅模式进行通信，而不需要直接依赖或引用彼此。这种松散耦合的设计提高了代码的可维护性和可测试性，使组件间能够高效地交换信息。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 常量 -->
  <div id="constants">
    <h2>常量</h2>
    
    <p>EventBus 定义了以下常量：</p>
    
    <ul>
      <li>
        <code>EventBusOptions</code>: 事件总线选项
        <ul>
          <li><code>dispatchToDOM</code>: 是否将事件分发到 DOM（默认为 false）</li>
          <li><code>enableExternalEvents</code>: 是否启用外部事件（默认为 false）</li>
          <li><code>disableCaretBrowsing</code>: 是否禁用插入符浏览（默认为 false）</li>
        </ul>
      </li>
      <li>
        <code>EventMap</code>: 内部常量，定义了支持的事件类型映射
      </li>
    </ul>
  </div>

  <!-- 属性 -->
  <div id="properties">
    <h2>属性</h2>
    
    <h3>公共属性</h3>
    <ul>
      <li>无直接暴露的公共属性，事件总线的所有交互通过方法完成</li>
    </ul>

    <h3>私有属性</h3>
    <ul>
      <li><code>#listeners</code>: 存储事件监听器的 Map 对象，键为事件类型，值为监听器数组</li>
      <li><code>#options</code>: 事件总线配置选项</li>
      <li><code>#externalListerners</code>: 外部事件监听器的引用集合</li>
      <li><code>#dispatchToDOM</code>: 是否将事件分发到 DOM</li>
      <li><code>#boundEvents</code>: 已绑定事件的集合</li>
    </ul>
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法</h2>
    
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">构造函数</h3>
      </div>
      <div class="method-content">
        <p>创建一个新的 EventBus 实例。</p>
        <pre><code class="language-javascript">
constructor({ dispatchToDOM = false, enableExternalEvents = false, disableCaretBrowsing = false } = {})
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>dispatchToDOM</code>: 是否将事件分发到 DOM（可选，默认为 false）</li>
          <li><code>enableExternalEvents</code>: 是否启用外部事件（可选，默认为 false）</li>
          <li><code>disableCaretBrowsing</code>: 是否禁用插入符浏览（可选，默认为 false）</li>
        </ul>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">on(eventName, listener, { external = false } = {})</h3>
      </div>
      <div class="method-content">
        <p>注册一个事件监听器。</p>
        <pre><code class="language-javascript">
on(eventName, listener, { external = false } = {}) {
  let eventListeners = this.#listeners.get(eventName);
  if (!eventListeners) {
    this.#listeners.set(eventName, (eventListeners = []));
  }
  eventListeners.push(listener);

  if (external && this.#options.enableExternalEvents) {
    this.#externalListerners.add(listener);
  }
}
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>eventName</code>: 事件名称</li>
          <li><code>listener</code>: 事件监听器函数</li>
          <li><code>external</code>: 是否为外部事件监听器（可选，默认为 false）</li>
        </ul>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">off(eventName, listener, { external = false } = {})</h3>
      </div>
      <div class="method-content">
        <p>移除一个事件监听器。</p>
        <pre><code class="language-javascript">
off(eventName, listener, { external = false } = {}) {
  const eventListeners = this.#listeners.get(eventName);
  if (!eventListeners) {
    return;
  }

  const index = eventListeners.indexOf(listener);
  if (index < 0) {
    return;
  }
  eventListeners.splice(index, 1);

  if (eventListeners.length === 0) {
    this.#listeners.delete(eventName);
  }

  if (external && this.#options.enableExternalEvents) {
    this.#externalListerners.delete(listener);
  }
}
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>eventName</code>: 事件名称</li>
          <li><code>listener</code>: 要移除的事件监听器函数</li>
          <li><code>external</code>: 是否为外部事件监听器（可选，默认为 false）</li>
        </ul>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">dispatch(eventName, data)</h3>
      </div>
      <div class="method-content">
        <p>分发一个事件，触发所有相关的监听器。</p>
        <pre><code class="language-javascript">
dispatch(eventName, data = null) {
  const eventListeners = this.#listeners.get(eventName);
  if (!eventListeners || eventListeners.length === 0) {
    if (this.#dispatchToDOM) {
      this.#dispatchDOMEvent(eventName, data);
    }
    return;
  }

  let externalListenersException = null;
  // 复制一份监听器列表，防止在调用过程中修改原列表导致问题
  const listeners = eventListeners.slice(0);
  for (const listener of listeners) {
    try {
      listener(data);
    } catch (ex) {
      if (this.#externalListerners.has(listener)) {
        // 记录外部监听器的异常，但不影响其他监听器执行
        externalListenersException ||= ex;
      } else {
        throw ex;
      }
    }
  }

  if (this.#dispatchToDOM) {
    this.#dispatchDOMEvent(eventName, data);
  }

  if (externalListenersException) {
    throw externalListenersException;
  }
}
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>eventName</code>: 事件名称</li>
          <li><code>data</code>: 要传递给监听器的数据（可选，默认为 null）</li>
        </ul>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">#dispatchDOMEvent(eventName, data = null)</h3>
      </div>
      <div class="method-content">
        <p>将事件分发到 DOM。这是一个私有方法，仅在 dispatchToDOM 选项为 true 时使用。</p>
        <pre><code class="language-javascript">
#dispatchDOMEvent(eventName, data = null) {
  const details = Object.create(null);
  if (data) {
    for (const key in data) {
      const value = data[key];
      if (key === "source") {
        // Event 对象不能包含循环引用
        if (value === window || value === document) {
          continue;
        }
      }
      details[key] = value;
    }
  }

  // 创建并分发自定义事件
  const event = new CustomEvent(eventName, {
    detail: details,
    bubbles: true,
    cancelable: true,
  });
  
  // 如果存在与事件相关的元素，在该元素上分发事件，否则在 document 上分发
  const container = data?.source?.div || data?.source?.container?.div || document;
  container.dispatchEvent(event);
}
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>eventName</code>: 事件名称</li>
          <li><code>data</code>: 要传递给 DOM 事件的数据（可选，默认为 null）</li>
        </ul>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">bindEvents(eventMap)</h3>
      </div>
      <div class="method-content">
        <p>批量绑定事件映射。</p>
        <pre><code class="language-javascript">
bindEvents(eventMap) {
  // 记录已绑定的事件，用于后续解绑
  if (!this.#boundEvents) {
    this.#boundEvents = Object.create(null);
  }

  for (const [eventName, callbackMap] of Object.entries(eventMap)) {
    if (!this.#boundEvents[eventName]) {
      this.#boundEvents[eventName] = [];
    }

    for (const [source, callback] of Object.entries(callbackMap)) {
      this.#boundEvents[eventName].push({ source, callback });
      this.on(eventName, callback);
    }
  }
}
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>eventMap</code>: 事件映射对象，结构为 { eventName: { source: callback } }</li>
        </ul>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">unbindEvents()</h3>
      </div>
      <div class="method-content">
        <p>解绑所有通过 bindEvents 方法绑定的事件。</p>
        <pre><code class="language-javascript">
unbindEvents() {
  if (!this.#boundEvents) {
    return;
  }

  for (const [eventName, callbackInfos] of Object.entries(this.#boundEvents)) {
    for (const { callback } of callbackInfos) {
      this.off(eventName, callback);
    }
  }
  delete this.#boundEvents;
}
        </code></pre>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">destroy()</h3>
      </div>
      <div class="method-content">
        <p>销毁 EventBus 实例，清理所有监听器和绑定。</p>
        <pre><code class="language-javascript">
destroy() {
  // 解绑所有事件
  this.unbindEvents();
  
  // 清除所有监听器
  this.#listeners.clear();
  
  // 清除外部监听器集合
  if (this.#externalListerners) {
    this.#externalListerners.clear();
  }
}
        </code></pre>
      </div>
    </div>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <h3>事件分发流程</h3>
    <p>1. 调用 dispatch 方法<br>
    2. 获取相关事件的监听器列表<br>
    3. 依次调用每个监听器<br>
    4. 可选择分发到 DOM<br>
    5. 处理异常</p>
    
    <div class="mermaid">
      graph TD
        A[调用 dispatch 方法] --> B{有注册的监听器?}
        B -->|是| C[复制监听器列表]
        B -->|否| D{是否分发到DOM?}
        C --> E[遍历监听器列表]
        E --> F{是否是外部监听器?}
        F -->|是| G[调用监听器，捕获异常]
        F -->|否| H[调用监听器]
        G --> I{还有更多监听器?}
        H --> I
        I -->|是| E
        I -->|否| D
        D -->|是| J[调用 #dispatchDOMEvent]
        D -->|否| K[结束分发]
        J --> K
        K --> L{有外部监听器异常?}
        L -->|是| M[抛出异常]
        L -->|否| N[正常返回]
        M --> N
    </div>
    
    <h3>事件注册与解绑流程</h3>
    <p>1. 通过 on 方法注册监听器<br>
    2. 通过 off 方法解绑监听器<br>
    3. 通过 bindEvents 批量注册<br>
    4. 通过 unbindEvents 批量解绑</p>
    
    <div class="mermaid">
      graph TD
        A[on: 注册单个监听器] --> B{事件是否已有监听器列表?}
        B -->|是| C[添加到现有列表]
        B -->|否| D[创建新列表并添加]
        C --> E{是否为外部事件?}
        D --> E
        E -->|是且启用外部事件| F[添加到外部监听器集合]
        E -->|否| G[完成注册]
        F --> G
        
        H[off: 解绑单个监听器] --> I{事件是否有监听器列表?}
        I -->|是| J[查找监听器索引]
        I -->|否| K[直接返回]
        J --> L{是否找到监听器?}
        L -->|是| M[从列表中移除]
        L -->|否| N[直接返回]
        M --> O{列表是否为空?}
        O -->|是| P[删除事件对应的监听器列表]
        O -->|否| Q{是否为外部事件?}
        P --> Q
        Q -->|是且启用外部事件| R[从外部监听器集合移除]
        Q -->|否| S[完成解绑]
        R --> S
        N --> S
        K --> S
    </div>
    
    <h3>DOM 事件分发流程</h3>
    <p>1. 处理事件数据<br>
    2. 创建 CustomEvent<br>
    3. 确定分发目标<br>
    4. 分发事件</p>
    
    <div class="mermaid">
      graph TD
        A[#dispatchDOMEvent 方法] --> B[创建 details 对象]
        B --> C{是否有数据?}
        C -->|是| D[遍历数据属性]
        C -->|否| E[使用空 details]
        D --> F{key 是否为 source 且 value 是 window/document?}
        F -->|是| G[跳过该属性]
        F -->|否| H[添加属性到 details]
        G --> I{还有更多属性?}
        H --> I
        I -->|是| D
        I -->|否| E
        E --> J[创建 CustomEvent]
        J --> K[确定事件目标元素]
        K --> L[在目标上分发事件]
    </div>
  </div>

  <!-- 示例 -->
  <div id="examples">
    <h2>使用示例</h2>
    
    <h3>基本用法</h3>
    <pre><code class="language-javascript">
// 创建 EventBus 实例
const eventBus = new EventBus();

// 注册事件监听器
eventBus.on("documentloaded", function(data) {
  console.log("文档已加载:", data.source);
});

eventBus.on("pagechanged", function(data) {
  console.log("页面已切换至:", data.pageNumber);
});

// 分发事件
eventBus.dispatch("documentloaded", {
  source: "example.pdf"
});

eventBus.dispatch("pagechanged", {
  pageNumber: 5,
  pageLabel: "5"
});

// 解绑特定事件
const pageRotationListener = (data) => {
  console.log("页面旋转:", data.angle);
};

eventBus.on("pagerotation", pageRotationListener);
eventBus.dispatch("pagerotation", { angle: 90 });

// 之后解绑该监听器
eventBus.off("pagerotation", pageRotationListener);
    </code></pre>
    
    <h3>分发到 DOM</h3>
    <pre><code class="language-javascript">
// 创建支持 DOM 分发的 EventBus
const domEventBus = new EventBus({ dispatchToDOM: true });

// 在 DOM 中监听事件
document.addEventListener("pagechanged", function(evt) {
  console.log("DOM 事件: 页面已切换至", evt.detail.pageNumber);
});

// 通过 EventBus 分发事件
domEventBus.dispatch("pagechanged", {
  pageNumber: 3,
  pageLabel: "3"
});

// 使用特定元素作为事件源
const pageContainer = document.getElementById("pageContainer");
domEventBus.dispatch("textlayerrendered", {
  source: {
    div: pageContainer
  },
  pageNumber: 3
});

// 此时事件将在 pageContainer 元素上分发，而不是在 document 上
    </code></pre>
    
    <h3>批量绑定事件</h3>
    <pre><code class="language-javascript">
// 创建 PDF 查看器组件
class PDFViewer {
  constructor() {
    this.eventBus = new EventBus();
    this.pageNumber = 1;
    this.pagesCount = 0;
  }
  
  init() {
    // 初始化时批量绑定事件
    this.eventBus.bindEvents({
      // documentloaded 事件映射
      "documentloaded": {
        // this 指向 PDFViewer 实例
        "viewer": this.onDocumentLoaded.bind(this)
      },
      // pagechanged 事件映射
      "pagechanged": {
        "viewer": this.onPageChanged.bind(this)
      },
      // pagesloaded 事件映射
      "pagesloaded": {
        "viewer": this.onPagesLoaded.bind(this)
      }
    });
  }
  
  onDocumentLoaded(data) {
    console.log("文档已加载, 页数:", data.pagesCount);
    this.pagesCount = data.pagesCount;
  }
  
  onPageChanged(data) {
    console.log("切换到页面:", data.pageNumber);
    this.pageNumber = data.pageNumber;
  }
  
  onPagesLoaded(data) {
    console.log("所有页面已加载");
  }
  
  destroy() {
    // 销毁时解绑所有事件
    this.eventBus.unbindEvents();
  }
}

// 使用示例
const viewer = new PDFViewer();
viewer.init();

// 模拟事件分发
viewer.eventBus.dispatch("documentloaded", {
  pagesCount: 10
});

viewer.eventBus.dispatch("pagechanged", {
  pageNumber: 3
});

// 销毁时自动解绑所有事件
viewer.destroy();
    </code></pre>
    
    <h3>使用外部事件</h3>
    <pre><code class="language-javascript">
// 创建支持外部事件的 EventBus
const eventBus = new EventBus({
  enableExternalEvents: true
});

// 注册普通事件监听器
eventBus.on("internalEvent", (data) => {
  console.log("内部事件处理:", data);
});

// 注册外部事件监听器 (可能来自插件或第三方代码)
eventBus.on("externalEvent", (data) => {
  // 这个监听器可能抛出异常，但不会影响其他监听器
  console.log("外部事件处理:", data);
  if (data.shouldThrow) {
    throw new Error("外部监听器异常");
  }
}, { external: true });

// 注册另一个普通监听器
eventBus.on("externalEvent", (data) => {
  console.log("另一个内部处理:", data);
});

try {
  // 分发事件，即使外部监听器抛出异常，也不会影响其他监听器
  eventBus.dispatch("externalEvent", {
    message: "测试外部事件",
    shouldThrow: true
  });
} catch (error) {
  console.error("捕获到外部事件异常:", error);
}

// 内部事件可以正常分发
eventBus.dispatch("internalEvent", {
  message: "内部事件继续工作"
});
    </code></pre>
  </div>

  <!-- 注意事项 -->
  <div id="notes">
    <h2>注意事项</h2>
    
    <ul>
      <li>EventBus 的事件名称应该使用统一的命名约定，通常使用全小写的 camelCase 格式，如 'pagechanged'、'documentloaded' 等。</li>
      <li>在组件销毁时，应确保调用 <code>unbindEvents()</code> 或 <code>destroy()</code> 方法清理事件监听器，防止内存泄漏。</li>
      <li>当启用外部事件时，外部监听器的异常会被捕获，并在所有监听器执行完毕后才抛出，以确保所有监听器都有机会执行。</li>
      <li>使用 <code>dispatchToDOM: true</code> 选项可以使 EventBus 事件同时作为 DOM 事件分发，便于与其他框架和库集成。</li>
      <li>当多次添加同一个事件监听器时，该监听器将被多次调用，EventBus 不会自动去重。</li>
      <li>事件数据应该是纯对象，避免包含循环引用，特别是当启用 DOM 分发时。</li>
      <li>对于需要频繁分发的事件，应考虑性能优化，如使用防抖或节流技术。</li>
      <li>EventBus 通常作为单例在整个应用中共享，以确保所有组件可以通过同一个事件总线进行通信。</li>
      <li>在复杂应用中，可以考虑为不同的功能区域创建多个 EventBus 实例，以避免事件命名冲突和提高代码的可维护性。</li>
    </ul>
  </div>

  <script>
    // 在页面加载完成后初始化 Mermaid
    document.addEventListener('DOMContentLoaded', function() {
      mermaid.initialize({ startOnLoad: true });
      
      // 生成目录
      generateTOC();
    });
  </script>
</body>
</html> 