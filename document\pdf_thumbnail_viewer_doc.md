# PDFThumbnailViewer 模块文档

## 模块简介

PDFThumbnailViewer 是一个用于管理和显示 PDF 文档缩略图的组件。它负责创建、渲染和管理 PDF 页面的缩略图，支持页面选择、滚动同步等功能。

## 常量

### THUMBNAIL_SCROLL_MARGIN
缩略图滚动边距，用于控制缩略图滚动到视图中的位置偏移。
```javascript
const THUMBNAIL_SCROLL_MARGIN = -19;
```

### THUMBNAIL_SELECTED_CLASS
缩略图选中状态的 CSS 类名，用于标记当前选中的缩略图。
```javascript
const THUMBNAIL_SELECTED_CLASS = "selected";
```

## 属性

- `container`: 缩略图容器元素
- `eventBus`: 事件总线，用于事件处理
- `linkService`: 链接服务，用于页面导航
- `renderingQueue`: 渲染队列，管理渲染优先级
- `maxCanvasPixels`: 最大画布像素数
- `maxCanvasDim`: 最大画布尺寸
- `pageColors`: 页面颜色配置
- `enableHWA`: 是否启用硬件加速
- `_thumbnails`: 缩略图数组，存储所有页面的缩略图
- `_currentPageNumber`: 当前页码
- `_pageLabels`: 页面标签数组
- `_pagesRotation`: 页面旋转角度

## 方法列表

### constructor({container, eventBus, linkService, renderingQueue, maxCanvasPixels, maxCanvasDim, pageColors, abortSignal, enableHWA})

创建新的 PDFThumbnailViewer 实例。

```javascript
constructor({
  container, // 容器元素
  eventBus, // 事件总线
  linkService, // 链接服务
  renderingQueue, // 渲染队列
  maxCanvasPixels, // 最大画布像素数
  maxCanvasDim, // 最大画布尺寸
  pageColors, // 页面颜色
  abortSignal, // 中止信号
  enableHWA // 是否启用硬件加速
}) {
  this.container = container; // 设置容器
  this.eventBus = eventBus; // 设置事件总线
  this.linkService = linkService; // 设置链接服务
  this.renderingQueue = renderingQueue; // 设置渲染队列
  this.maxCanvasPixels = maxCanvasPixels; // 设置最大画布像素数
  this.maxCanvasDim = maxCanvasDim; // 设置最大画布尺寸
  this.pageColors = pageColors || null; // 设置页面颜色，默认为null
  this.enableHWA = enableHWA || false; // 设置是否启用硬件加速，默认为false
  this.scroll = watchScroll(this.container, this.#scrollUpdated.bind(this), abortSignal); // 监听容器的滚动事件
  this.#resetView(); // 重置视图
}
```

### #scrollUpdated()

私有方法，处理滚动更新事件。

```javascript
#scrollUpdated() { // 滚动更新的私有方法
  this.renderingQueue.renderHighestPriority(); // 渲染最高优先级的缩略图
}
```

### getThumbnail(index)

获取指定索引的缩略图。

```javascript
/**
 * 获取指定索引的缩略图
 * @param {number} index - 缩略图索引
 * @returns {PDFThumbnailView} 缩略图视图对象
 */
getThumbnail(index) { // 获取指定索引的缩略图方法
  return this._thumbnails[index]; // 返回对应索引的缩略图
}
```

### #getVisibleThumbs()

私有方法，获取当前可见的缩略图。

```javascript
#getVisibleThumbs() { // 获取可见缩略图的私有方法
  return getVisibleElements({ // 调用获取可见元素的函数
    scrollEl: this.container, // 滚动容器
    views: this._thumbnails // 缩略图数组
  });
}
```

### scrollThumbnailIntoView(pageNumber)

将指定页码的缩略图滚动到可见区域。

```javascript
/**
 * 将缩略图滚动到可见区域
 * @param {number} pageNumber - 页码
 */
scrollThumbnailIntoView(pageNumber) { // 将缩略图滚动到可见区域的方法
  if (!this.pdfDocument) { // 如果没有PDF文档
    return; // 直接返回
  }
  const thumbnailView = this._thumbnails[pageNumber - 1]; // 获取指定页码的缩略图视图
  if (!thumbnailView) { // 如果缩略图不存在
    console.error('scrollThumbnailIntoView: Invalid "pageNumber" parameter.'); // 输出错误信息
    return; // 直接返回
  }
  if (pageNumber !== this._currentPageNumber) { // 如果请求的页码与当前页码不同
    const prevThumbnailView = this._thumbnails[this._currentPageNumber - 1]; // 获取当前页码的缩略图视图
    prevThumbnailView.div.classList.remove(THUMBNAIL_SELECTED_CLASS); // 移除当前缩略图的选中样式
    thumbnailView.div.classList.add(THUMBNAIL_SELECTED_CLASS); // 为新缩略图添加选中样式
  }
  const {
    first, // 第一个可见缩略图
    last, // 最后一个可见缩略图
    views // 所有可见缩略图
  } = this.#getVisibleThumbs(); // 获取可见缩略图
  if (views.length > 0) { // 如果有可见缩略图
    let shouldScroll = false; // 初始化是否需要滚动的标志
    if (pageNumber <= first.id || pageNumber >= last.id) { // 如果请求的页码在可见区域之外
      shouldScroll = true; // 需要滚动
    } else { // 如果请求的页码可能在可见区域内
      for (const {
        id, // 缩略图ID
        percent // 可见百分比
      } of views) {
        if (id !== pageNumber) { // 如果不是请求的页码
          continue; // 继续下一个
        }
        shouldScroll = percent < 100; // 如果可见百分比小于100%，则需要滚动
        break; // 找到了请求的页码，跳出循环
      }
    }
    if (shouldScroll) { // 如果需要滚动
      scrollIntoView(thumbnailView.div, { // 滚动缩略图到可见区域
        top: THUMBNAIL_SCROLL_MARGIN // 设置顶部边距
      });
    }
  }
  this._currentPageNumber = pageNumber; // 更新当前页码
}
```

### get pagesRotation()

获取页面旋转角度。

```javascript
/**
 * 获取页面旋转角度
 * @returns {number} 旋转角度
 */
get pagesRotation() { // 获取页面旋转角度的getter方法
  return this._pagesRotation; // 返回当前页面的旋转角度
}
```

### set pagesRotation(rotation)

设置页面旋转角度。

```javascript
/**
 * 设置页面旋转角度
 * @param {number} rotation - 旋转角度
 */
set pagesRotation(rotation) { // 设置页面旋转角度的setter方法
  if (!isValidRotation(rotation)) { // 检查旋转角度是否有效
    throw new Error("Invalid thumbnails rotation angle."); // 如果无效，抛出错误
  }
  if (!this.pdfDocument) { // 检查是否存在PDF文档
    return; // 如果不存在，直接返回
  }
  if (this._pagesRotation === rotation) { // 检查新的旋转角度是否与当前角度相同
    return; // 如果相同，不进行操作
  }
  this._pagesRotation = rotation; // 更新旋转角度
  const updateArgs = { // 创建更新参数对象
    rotation // 设置旋转参数
  };
  for (const thumbnail of this._thumbnails) { // 遍历所有缩略图
    thumbnail.update(updateArgs); // 使用新的旋转角度更新每个缩略图
  }
}
```

### cleanup()

清理缩略图资源。

```javascript
/**
 * 清理资源
 */
cleanup() { // 清理方法，用于清理资源
  for (const thumbnail of this._thumbnails) { // 遍历所有缩略图
    if (thumbnail.renderingState !== RenderingStates.FINISHED) { // 检查缩略图是否渲染完成
      thumbnail.reset(); // 如果未完成，重置缩略图
    }
  }
  TempImageFactory.destroyCanvas(); // 销毁临时画布
}
```

### #resetView()

私有方法，重置缩略图视图。

```javascript
#resetView() { // 重置视图的私有方法
  this._thumbnails = []; // 清空缩略图数组
  this._currentPageNumber = 1; // 重置当前页码为1
  this._pageLabels = null; // 清空页面标签
  this._pagesRotation = 0; // 重置旋转角度为0
  this.container.textContent = ""; // 清空容器内容
}
```

### setDocument(pdfDocument)

设置 PDF 文档，并初始化缩略图。

```javascript
/**
 * 设置PDF文档
 * @param {PDFDocumentProxy} pdfDocument - PDF文档
 */
setDocument(pdfDocument) { // 设置PDF文档的方法
  if (this.pdfDocument) { // 检查是否已存在PDF文档
    this.#cancelRendering(); // 取消正在进行的渲染
    this.#resetView(); // 重置视图
  }
  this.pdfDocument = pdfDocument; // 设置新的PDF文档
  if (!pdfDocument) { // 检查是否提供了有效的PDF文档
    return; // 如果没有，直接返回
  }
  const firstPagePromise = pdfDocument.getPage(1); // 获取第一页的Promise
  const optionalContentConfigPromise = pdfDocument.getOptionalContentConfig({ // 获取可选内容配置的Promise
    intent: "display" // 设置意图为显示
  });
  firstPagePromise.then(firstPdfPage => { // 处理第一页加载完成后的操作
    const pagesCount = pdfDocument.numPages; // 获取PDF文档的总页数
    const viewport = firstPdfPage.getViewport({ // 获取第一页的视口
      scale: 1 // 设置缩放比例为1
    });
    for (let pageNum = 1; pageNum <= pagesCount; ++pageNum) { // 遍历所有页面
      const thumbnail = new PDFThumbnailView({ // 为每页创建缩略图视图
        container: this.container, // 设置容器
        eventBus: this.eventBus, // 设置事件总线
        id: pageNum, // 设置页码作为ID
        defaultViewport: viewport.clone(), // 克隆视口作为默认视口
        optionalContentConfigPromise, // 设置可选内容配置
        linkService: this.linkService, // 设置链接服务
        renderingQueue: this.renderingQueue, // 设置渲染队列
        maxCanvasPixels: this.maxCanvasPixels, // 设置最大画布像素
        maxCanvasDim: this.maxCanvasDim, // 设置最大画布尺寸
        pageColors: this.pageColors, // 设置页面颜色
        enableHWA: this.enableHWA // 设置是否启用硬件加速
      });
      this._thumbnails.push(thumbnail); // 将创建的缩略图添加到数组中
    }
    this._thumbnails[0]?.setPdfPage(firstPdfPage); // 为第一个缩略图设置PDF页面（使用可选链操作符）
    const thumbnailView = this._thumbnails[this._currentPageNumber - 1]; // 获取当前页码对应的缩略图
    thumbnailView.div.classList.add(THUMBNAIL_SELECTED_CLASS); // 为当前缩略图添加选中样式类
  }).catch(reason => { // 捕获可能发生的错误
    console.error("Unable to initialize thumbnail viewer", reason); // 输出错误信息到控制台
  });
}
```

### #cancelRendering()

私有方法，取消缩略图渲染。

```javascript
#cancelRendering() { // 取消渲染的私有方法
  for (const thumbnail of this._thumbnails) { // 遍历所有缩略图
    thumbnail.cancelRendering(); // 取消每个缩略图的渲染
  }
}
```

### setPageLabels(labels)

设置页面标签。

```javascript
/**
 * 设置页面标签
 * @param {Array<string>} labels - 页面标签数组
 */
setPageLabels(labels) { // 设置页面标签的方法
  if (!this.pdfDocument) { // 检查是否存在PDF文档
    return; // 如果不存在，直接返回
  }
  if (!labels) { // 检查是否提供了标签
    this._pageLabels = null; // 如果没有提供标签，设置页面标签为null
  } else if (!(Array.isArray(labels) && this.pdfDocument.numPages === labels.length)) { // 检查标签是否是数组且长度与页数相同
    this._pageLabels = null; // 如果不符合条件，设置页面标签为null
    console.error("PDFThumbnailViewer_setPageLabels: Invalid page labels."); // 输出错误信息
  } else { // 如果标签有效
    this._pageLabels = labels; // 设置页面标签
  }
  for (let i = 0, ii = this._thumbnails.length; i < ii; i++) { // 遍历所有缩略图
    this._thumbnails[i].setPageLabel(this._pageLabels?.[i] ?? null); // 为每个缩略图设置对应的页面标签，使用可选链和空值合并操作符
  }
}
```

### #ensurePdfPageLoaded(thumbView)

私有方法，确保 PDF 页面已加载。

```javascript
/**
 * 确保PDF页面已加载
 * @param {PDFThumbnailView} thumbView - 缩略图视图
 * @returns {Promise<PDFPageProxy>} 页面对象Promise
 */
async #ensurePdfPageLoaded(thumbView) { // 确保PDF页面已加载的异步私有方法
  if (thumbView.pdfPage) { // 检查缩略图是否已有PDF页面
    return thumbView.pdfPage; // 如果已有，直接返回
  }
  try { // 尝试加载PDF页面
    const pdfPage = await this.pdfDocument.getPage(thumbView.id); // 异步获取对应ID的PDF页面
    if (!thumbView.pdfPage) { // 再次检查缩略图是否已有PDF页面（防止并发操作）
      thumbView.setPdfPage(pdfPage); // 如果仍然没有，设置PDF页面
    }
    return pdfPage; // 返回获取的PDF页面
  } catch (reason) { // 捕获可能的错误
    console.error("Unable to get page for thumb view", reason); // 输出错误信息
    return null; // 发生错误时返回null
  }
}
```

### #getScrollAhead(visible)

私有方法，获取滚动方向。

```javascript
#getScrollAhead(visible) { // 获取滚动方向的私有方法
  if (visible.first?.id === 1) { // 检查可见区域的第一个元素是否为第一页（使用可选链）
    return true; // 如果是第一页，向前滚动
  }
  if (visible.last?.id === this._thumbnails.length) { // 检查可见区域的最后一个元素是否为最后一页
    return false; // 如果是最后一页，向后滚动
  }
  return this._currentPageNumber >= visible.first?.id; // 根据当前页码与可见区域第一个元素的关系决定滚动方向
}
```

## 流程图

### 初始化缩略图流程

```mermaid
graph TD
  A[调用setDocument] --> B{检查是否已有PDF文档}
  B -->|有| C[取消渲染]
  C --> D[重置视图]
  B -->|无| E[设置新PDF文档]
  D --> E
  E --> F{检查PDF文档是否有效}
  F -->|无效| G[返回]
  F -->|有效| H[获取第一页]
  H --> I[获取可选内容配置]
  I --> J[获取文档页数]
  J --> K[获取第一页视口]
  K --> L[创建所有页面的缩略图]
  L --> M[为第一个缩略图设置页面]
  M --> N[为当前缩略图添加选中样式]
```

### 滚动缩略图到视图流程

```mermaid
graph TD
  A[调用scrollThumbnailIntoView] --> B{检查PDF文档是否存在}
  B -->|不存在| C[返回]
  B -->|存在| D{检查缩略图是否存在}
  D -->|不存在| E[输出错误并返回]
  D -->|存在| F{是否是当前页码}
  F -->|否| G[移除之前缩略图的选中样式]
  G --> H[为新缩略图添加选中样式]
  F -->|是| I[获取可见缩略图]
  H --> I
  I --> J{是否有可见缩略图}
  J -->|无| K[更新当前页码]
  J -->|有| L{目标页码是否在可见区域外}
  L -->|是| M[设置需要滚动]
  L -->|否| N[检查目标缩略图可见性]
  N --> O{是否完全可见}
  O -->|否| P[设置需要滚动]
  O -->|是| Q[不需要滚动]
  M --> R{是否需要滚动}
  P --> R
  Q --> R
  R -->|是| S[滚动缩略图到视图]
  R -->|否| K
  S --> K
  K --> T[结束]
``` 