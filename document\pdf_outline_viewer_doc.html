<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PDFOutlineViewer - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#constants">常量</a>
    <a href="#properties">属性</a>
    <a href="#methods">方法</a>
    <a href="#flowcharts">流程图</a>
    <a href="#examples">示例</a>
    <a href="#notes">注意事项</a>
  </div>

  <h1>PDFOutlineViewer 模块文档</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>PDFOutlineViewer 是 PDF.js 库中负责显示和管理 PDF 文档大纲（目录结构）的组件。它解析和渲染 PDF 文档的大纲内容，提供可交互的目录树结构，允许用户通过点击大纲项目快速导航到文档的不同部分。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 常量 -->
  <div id="constants">
    <h2>常量</h2>
    
    <p>PDFOutlineViewer 使用以下常量：</p>
    
    <ul>
      <li>
        <code>OutlineStateManager.INITIAL_STATE</code>: 初始状态对象
        <ul>
          <li><code>outlineDict</code>: null</li>
          <li><code>pageNumberToDestHashCapability</code>: null</li>
        </ul>
      </li>
      <li><code>EXPAND_DIVS_TIMEOUT</code>: 展开大纲项的超时时间（毫秒）</li>
      <li><code>LEVEL_THRESHOLD</code>: 大纲层级阈值</li>
      <li><code>TOGGLE_BUTTON_WIDTH</code>: 切换按钮宽度</li>
    </ul>
  </div>

  <!-- 属性 -->
  <div id="properties">
    <h2>属性</h2>
    
    <h3>公共属性</h3>
    <ul>
      <li><code>outline</code>: 文档大纲数据</li>
      <li><code>container</code>: 大纲容器元素</li>
      <li><code>linkService</code>: PDF 链接服务实例</li>
      <li><code>eventBus</code>: 事件总线</li>
      <li><code>l10n</code>: 本地化对象</li>
      <li><code>_pdfDocument</code>: 当前 PDF 文档对象</li>
      <li><code>outlineItemClass</code>: 大纲项目 CSS 类名</li>
    </ul>

    <h3>私有属性</h3>
    <ul>
      <li><code>#items</code>: 存储所有大纲项的数组</li>
      <li><code>#wasToggled</code>: 是否已切换过的标志</li>
      <li><code>#bindOnEvent</code>: 绑定的事件处理函数</li>
      <li><code>#resetOutlineToStateOnClose</code>: 关闭时重置大纲状态的标志</li>
      <li><code>#currentOutlineItemButton</code>: 当前活动的大纲项按钮</li>
    </ul>
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法</h2>
    
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">构造函数</h3>
      </div>
      <div class="method-content">
        <p>创建一个新的 PDFOutlineViewer 实例。</p>
        <pre><code class="language-javascript">
constructor({ container, linkService, eventBus, l10n = undefined })
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>container</code>: 大纲容器元素</li>
          <li><code>linkService</code>: PDF 链接服务实例</li>
          <li><code>eventBus</code>: 事件总线</li>
          <li><code>l10n</code>: 本地化对象 (可选)</li>
        </ul>
      </div>
    </div>

    <h3>核心方法</h3>
    <ul>
      <li><code>reset()</code>: 重置大纲视图到初始状态</li>
      <li><code>render({ outline, pdfDocument })</code>: 渲染 PDF 文档的大纲内容</li>
      <li><code>_dispatchEvent(outlineCount)</code>: 分发大纲已加载事件</li>
      <li><code>_bindLink(element, { action, dest })</code>: 绑定大纲项链接</li>
      <li><code>_setStyles(element, { bold, italic })</code>: 设置大纲项的样式</li>
      <li><code>_addToggleButton(div, { count, items })</code>: 添加切换按钮到大纲项</li>
      <li><code>_toggleAllTreeItems()</code>: 切换所有大纲项的展开/折叠状态</li>
      <li><code>toggleOutlineTree()</code>: 切换整个大纲树的展开/折叠状态</li>
      <li><code>expandOutlineItems()</code>: 展开所有大纲项</li>
      <li><code>collapseOutlineItems()</code>: 折叠所有大纲项</li>
    </ul>
    
    <h3>私有方法</h3>
    <ul>
      <li><code>#handleEvent()</code>: 处理事件</li>
      <li><code>#ensureOutlineLoaded()</code>: 确保大纲已加载</li>
      <li><code>#toggle(target)</code>: 切换大纲项展开/折叠状态</li>
      <li><code>#expand(target)</code>: 展开大纲项</li>
      <li><code>#collapse(target)</code>: 折叠大纲项</li>
      <li><code>#toggleItems(visibleElementsMap)</code>: 切换多个大纲项状态</li>
      <li><code>#walk(node)</code>: 遍历大纲项节点树</li>
      <li><code>#addOutlineItem({ dest, items, title, color, count, bold, italic })</code>: 添加大纲项</li>
    </ul>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <h3>大纲初始化与渲染流程</h3>
    <p>1. 创建 PDFOutlineViewer 实例<br>
    2. 调用 <code>render()</code> 方法设置文档大纲数据<br>
    3. 解析大纲结构<br>
    4. 生成 DOM 元素并添加到容器<br>
    5. 绑定点击事件<br>
    6. 分发大纲加载完成事件</p>
    
    <div class="mermaid">
      graph TD
          A[创建 PDFOutlineViewer 实例] --> B[调用 render 方法]
          B --> C[解析大纲结构]
          C --> D[生成大纲项 DOM 元素]
          D --> E[为每个项目添加点击事件]
          D --> F[为可展开项添加切换按钮]
          E --> G[分发大纲加载完成事件]
          F --> G
    </div>
    
    <h3>大纲项点击导航流程</h3>
    <p>1. 用户点击大纲项<br>
    2. 触发 <code>_bindLink</code> 方法处理点击事件<br>
    3. 使用 linkService 处理导航<br>
    4. PDF 跳转到目标位置</p>
    
    <div class="mermaid">
      graph TD
          A[用户点击大纲项] --> B[调用 _bindLink 方法]
          B --> C[获取目标位置信息]
          C --> D[调用 linkService 的导航方法]
          D --> E[PDF 查看器跳转到目标位置]
    </div>
    
    <h3>展开/折叠功能流程</h3>
    <p>1. 用户点击展开/折叠按钮<br>
    2. 触发 <code>#toggle</code> 方法<br>
    3. 改变目标项的可见性<br>
    4. 更新切换按钮状态</p>
    
    <div class="mermaid">
      graph TD
          A[用户点击展开/折叠按钮] --> B[调用 #toggle 方法]
          B --> C{当前状态是否为折叠?}
          C -->|是| D[展开子项]
          C -->|否| E[折叠子项]
          D --> F[更新按钮图标]
          E --> F
    </div>
  </div>

  <!-- 示例 -->
  <div id="examples">
    <h2>使用示例</h2>
    
    <h3>基本用法</h3>
    <pre><code class="language-javascript">
// 创建 PDFOutlineViewer 实例
const outlineViewer = new PDFOutlineViewer({
  container: document.getElementById('outlineView'),
  linkService: pdfLinkService,
  eventBus: eventBus
});

// 加载文档时渲染大纲
const loadingTask = pdfjsLib.getDocument('document.pdf');
loadingTask.promise.then(function(pdfDocument) {
  pdfDocument.getOutline().then(function(outline) {
    outlineViewer.render({ outline, pdfDocument });
  });
});
    </code></pre>
    
    <h3>切换大纲展开/折叠</h3>
    <pre><code class="language-javascript">
// 展开所有大纲项
document.getElementById('expandAll').addEventListener('click', function() {
  outlineViewer.expandOutlineItems();
});

// 折叠所有大纲项
document.getElementById('collapseAll').addEventListener('click', function() {
  outlineViewer.collapseOutlineItems();
});

// 切换大纲树
document.getElementById('toggleOutline').addEventListener('click', function() {
  outlineViewer.toggleOutlineTree();
});
    </code></pre>
  </div>

  <!-- 注意事项 -->
  <div id="notes">
    <h2>注意事项</h2>
    
    <ul>
      <li>大纲数据依赖于 PDF 文档本身包含的大纲信息。如果 PDF 文档没有大纲信息，则渲染方法不会生成任何内容。</li>
      <li>大纲项的点击导航依赖于 linkService 的正确配置。确保 linkService 已正确初始化并与当前 PDF 文档关联。</li>
      <li>大量大纲项（如超过 1000 个）可能会影响性能。对于特别大的大纲，可能需要考虑懒加载或虚拟滚动技术。</li>
      <li>大纲项的样式（加粗、斜体、颜色）取决于 PDF 文档中的设置。如果 PDF 文档中未定义这些样式，则使用默认样式。</li>
      <li>大纲视图的容器元素应该有适当的 CSS 样式，包括滚动条设置，以便用户可以浏览长大纲。</li>
    </ul>
  </div>

  <script>
    // 在页面加载完成后初始化 Mermaid
    document.addEventListener('DOMContentLoaded', function() {
      mermaid.initialize({ startOnLoad: true });
      
      // 生成目录
      generateTOC();
    });
  </script>
</body>
</html> 