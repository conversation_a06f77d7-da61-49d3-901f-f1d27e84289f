<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PDFDocumentProperties - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#constants">常量</a>
    <a href="#properties">属性</a>
    <a href="#methods">方法</a>
    <a href="#flowcharts">流程图</a>
    <a href="#examples">示例</a>
    <a href="#notes">注意事项</a>
  </div>

  <h1>PDFDocumentProperties 模块文档</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>PDFDocumentProperties 是 PDF.js 库中提供文档元数据访问和显示的组件。它负责提取、处理和展示 PDF 文档的各种属性信息，如标题、作者、创建日期、页数、文件大小、安全设置等。该组件通常以对话框形式呈现，为用户提供文档的详细信息。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 常量 -->
  <div id="constants">
    <h2>常量</h2>
    
    <p>PDFDocumentProperties 使用以下常量：</p>
    
    <ul>
      <li>
        <code>DEFAULT_FIELD_CONTENT</code>: 默认字段内容，通常为破折号 "-"
      </li>
      <li>
        <code>NON_METRIC_LOCALES</code>: 非公制单位地区列表，如 ["en-us", "en-lr", "my"]
      </li>
      <li>
        <code>FileSize</code>: 文件大小单位
        <ul>
          <li><code>Kb</code>: 千字节 (1024 字节)</li>
          <li><code>Mb</code>: 兆字节 (1024 * 1024 字节)</li>
          <li><code>Gb</code>: 吉字节 (1024 * 1024 * 1024 字节)</li>
        </ul>
      </li>
      <li>
        <code>PageSize</code>: 页面尺寸单位
        <ul>
          <li><code>LETTER_SIZE_IN_INCHES</code>: 信纸尺寸（英寸）</li>
          <li><code>LETTER_SIZE_IN_POINTS</code>: 信纸尺寸（点）</li>
          <li><code>METRIC_FACTOR</code>: 公制换算因子</li>
        </ul>
      </li>
    </ul>
  </div>

  <!-- 属性 -->
  <div id="properties">
    <h2>属性</h2>
    
    <h3>公共属性</h3>
    <ul>
      <li><code>dialog</code>: 属性对话框元素</li>
      <li><code>overlayName</code>: 覆盖层名称</li>
    </ul>

    <h3>私有属性</h3>
    <ul>
      <li><code>#fieldData</code>: 字段数据对象</li>
      <li><code>#reset</code>: 重置函数</li>
      <li><code>#pdfDocument</code>: 当前 PDF 文档</li>
      <li><code>#maybeFileSize</code>: 可能的文件大小值</li>
      <li><code>#dialog</code>: 对话框元素内部引用</li>
      <li><code>#overlayManager</code>: 覆盖层管理器</li>
      <li><code>#eventBus</code>: 事件总线</li>
      <li><code>#l10n</code>: 本地化对象</li>
      <li><code>#documentInfo</code>: 文档信息对象</li>
      <li><code>#fileSize</code>: 文件大小</li>
      <li><code>#updateUI</code>: UI 更新函数</li>
      <li><code>#fields</code>: 属性字段元素集合</li>
    </ul>
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法</h2>
    
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">构造函数</h3>
      </div>
      <div class="method-content">
        <p>创建一个新的 PDFDocumentProperties 实例。</p>
        <pre><code class="language-javascript">
constructor({
  dialog,
  fields,
  overlayManager,
  eventBus,
  l10n,
})
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>dialog</code>: 属性对话框元素</li>
          <li><code>fields</code>: 属性字段元素映射</li>
          <li><code>overlayManager</code>: 覆盖层管理器</li>
          <li><code>eventBus</code>: 事件总线</li>
          <li><code>l10n</code>: 本地化对象</li>
        </ul>
      </div>
    </div>

    <h3>核心方法</h3>
    <ul>
      <li><code>open()</code>: 打开文档属性对话框</li>
      <li><code>close()</code>: 关闭文档属性对话框</li>
      <li><code>setDocument(pdfDocument, url = "")</code>: 设置要显示属性的 PDF 文档</li>
      <li><code>update()</code>: 更新文档属性显示</li>
    </ul>
    
    <h3>私有方法</h3>
    <ul>
      <li><code>#getPageSize(pagesCount, pageIndex, pdfDocument)</code>: 获取页面尺寸</li>
      <li><code>#setupField(id, content)</code>: 设置属性字段内容</li>
      <li><code>#parseFileSize(fileSize)</code>: 解析文件大小为可读格式</li>
      <li><code>#parsePageSize(pageSizeInches, pagesRotation)</code>: 解析页面尺寸为可读格式</li>
      <li><code>#parseDate(inputDate)</code>: 解析日期为可读格式</li>
      <li><code>#resetFields()</code>: 重置所有属性字段</li>
      <li><code>#updateUI(reset = false)</code>: 更新用户界面</li>
      <li><code>#fieldData</code>: 获取字段数据</li>
      <li><code>#reset</code>: 获取重置函数</li>
    </ul>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <h3>文档属性加载流程</h3>
    <p>1. 用户请求查看文档属性<br>
    2. 调用 <code>open()</code> 方法<br>
    3. 通过覆盖层管理器显示对话框<br>
    4. 获取文档元数据<br>
    5. 解析和格式化数据<br>
    6. 更新用户界面</p>
    
    <div class="mermaid">
      graph TD
          A[用户请求查看属性] --> B[调用 open 方法]
          B --> C[通过覆盖层管理器显示对话框]
          C --> D[获取文档元数据]
          D --> E[解析文档信息]
          E --> F[获取文件大小信息]
          F --> G[获取页面尺寸信息]
          G --> H[格式化所有数据]
          H --> I[更新用户界面显示]
    </div>
    
    <h3>页面尺寸计算流程</h3>
    <p>1. 调用 <code>#getPageSize()</code> 方法<br>
    2. 获取页面视口<br>
    3. 考虑页面旋转<br>
    4. 将点单位转换为英寸或公制单位<br>
    5. 根据用户语言区域选择合适的单位显示</p>
    
    <div class="mermaid">
      graph TD
          A[调用 getPageSize 方法] --> B[获取页面视口]
          B --> C[考虑页面旋转]
          C --> D[计算原始尺寸]
          D --> E{用户区域是否使用公制?}
          E -->|是| F[转换为厘米]
          E -->|否| G[保留为英寸]
          F --> H[格式化尺寸字符串]
          G --> H
    </div>
    
    <h3>文档属性更新流程</h3>
    <p>1. 文档加载或变更<br>
    2. 调用 <code>setDocument()</code> 方法<br>
    3. 重置旧数据<br>
    4. 存储新的文档引用<br>
    5. 如果对话框已打开，立即更新显示</p>
    
    <div class="mermaid">
      graph TD
          A[文档加载或变更] --> B[调用 setDocument 方法]
          B --> C[重置旧数据]
          C --> D[存储新文档引用]
          D --> E{对话框是否打开?}
          E -->|是| F[调用 update 方法更新显示]
          E -->|否| G[等待用户打开对话框]
    </div>
  </div>

  <!-- 示例 -->
  <div id="examples">
    <h2>使用示例</h2>
    
    <h3>基本用法</h3>
    <pre><code class="language-javascript">
// 创建 PDFDocumentProperties 实例
const documentProperties = new PDFDocumentProperties({
  dialog: document.getElementById('documentPropertiesDialog'),
  fields: {
    fileName: document.getElementById('fileNameField'),
    fileSize: document.getElementById('fileSizeField'),
    title: document.getElementById('titleField'),
    author: document.getElementById('authorField'),
    subject: document.getElementById('subjectField'),
    keywords: document.getElementById('keywordsField'),
    creationDate: document.getElementById('creationDateField'),
    modificationDate: document.getElementById('modificationDateField'),
    creator: document.getElementById('creatorField'),
    producer: document.getElementById('producerField'),
    version: document.getElementById('versionField'),
    pageCount: document.getElementById('pageCountField'),
    pageSize: document.getElementById('pageSizeField'),
    linearized: document.getElementById('linearizedField'),
  },
  overlayManager: overlayManager,
  eventBus: eventBus,
  l10n: l10n
});

// 加载文档时设置文档
pdfDocument.getDownloadInfo().then(({ length }) => {
  documentProperties.setDocument(pdfDocument, url);
  documentProperties.maybeFileSize = length;
});

// 添加按钮点击事件
document.getElementById('documentProperties').addEventListener('click', function() {
  documentProperties.open();
});
    </code></pre>
    
    <h3>自定义属性显示</h3>
    <pre><code class="language-javascript">
// 在文档加载完成后扩展属性信息
eventBus.on('documentloaded', function() {
  // 获取额外的自定义元数据
  pdfDocument.getMetadata().then(function({ info, metadata, contentDispositionFilename }) {
    // 添加自定义属性到页面
    const customPropertiesContainer = document.getElementById('customProperties');
    customPropertiesContainer.innerHTML = '';
    
    // 显示所有可用的元数据
    if (metadata) {
      const metadataItems = metadata.getAll();
      for (const key in metadataItems) {
        const value = metadataItems[key];
        const row = document.createElement('div');
        row.className = 'row';
        
        const label = document.createElement('span');
        label.textContent = key + ':';
        label.className = 'label';
        
        const content = document.createElement('span');
        content.textContent = value;
        content.className = 'content';
        
        row.appendChild(label);
        row.appendChild(content);
        customPropertiesContainer.appendChild(row);
      }
    }
  });
});
    </code></pre>
    
    <h3>将属性导出为 JSON</h3>
    <pre><code class="language-javascript">
// 添加导出属性功能
document.getElementById('exportProperties').addEventListener('click', function() {
  // 收集当前属性
  const properties = {};
  
  // 获取 fieldData 中的所有属性
  const fieldData = documentProperties.fieldData;
  for (const key in fieldData) {
    properties[key] = fieldData[key];
  }
  
  // 创建 JSON 字符串
  const jsonString = JSON.stringify(properties, null, 2);
  
  // 创建下载链接
  const blob = new Blob([jsonString], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  
  const a = document.createElement('a');
  a.href = url;
  a.download = 'document_properties.json';
  a.click();
  
  URL.revokeObjectURL(url);
});
    </code></pre>
  </div>

  <!-- 注意事项 -->
  <div id="notes">
    <h2>注意事项</h2>
    
    <ul>
      <li>PDF 文档可能不包含所有元数据字段。在显示属性时应处理缺失字段的情况，通常用破折号"-"表示。</li>
      <li>日期格式可能因用户的语言和区域设置而异。使用本地化（l10n）功能来确保日期格式符合用户的预期。</li>
      <li>页面尺寸单位（英寸或厘米）应根据用户的区域设置自动选择，美国等少数国家使用英制单位，大多数国家使用公制单位。</li>
      <li>文件大小应以人类可读的格式显示（KB、MB、GB 等），而不是原始字节数。</li>
      <li>处理非常大的 PDF 文件时，获取页面尺寸可能会很耗时，特别是对于包含多种页面尺寸的文档。考虑仅计算第一页的尺寸，或实现异步加载策略。</li>
      <li>对于受保护的 PDF 文档，某些元数据可能不可用或被加密。在这种情况下，相应地处理权限错误。</li>
      <li>属性对话框应设计为模态对话框，防止用户在查看属性时与底层文档交互，这可以通过覆盖层管理器（overlayManager）实现。</li>
      <li>考虑添加复制功能，允许用户复制所选属性或所有属性到剪贴板，以便于分享或记录文档信息。</li>
    </ul>
  </div>

  <script>
    // 在页面加载完成后初始化 Mermaid
    document.addEventListener('DOMContentLoaded', function() {
      mermaid.initialize({ startOnLoad: true });
      
      // 生成目录
      generateTOC();
    });
  </script>
</body>
</html> 