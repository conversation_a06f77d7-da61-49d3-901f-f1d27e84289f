<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SimpleLinkService - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#methods">方法列表</a>
    <a href="#flowcharts">流程图</a>
  </div>

  <h1>SimpleLinkService</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>SimpleLinkService是PDF.js中一个轻量级的链接服务实现，它继承自PDFLinkService类。这个简化版本的链接服务主要用于非交互式环境或需要最小功能集的场景，如仅渲染PDF而不需要完整导航功能的应用。它通过覆盖父类的方法并提供空实现，移除了与文档导航和交互相关的复杂功能。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法列表</h2>
    
    <!-- setDocument方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">setDocument(pdfDocument, baseUrl = null)</h3>
      </div>
      <div class="method-content">
        <p>覆盖父类PDFLinkService的setDocument方法，提供空实现。这个方法在标准的PDFLinkService中用于设置当前PDF文档和基础URL，但在SimpleLinkService中不执行任何操作。</p>
        <pre><code class="language-javascript">
/**
 * 覆盖父类方法，简化实现（不执行任何操作）
 * @param {PDFDocumentProxy} pdfDocument - PDF文档对象
 * @param {string} baseUrl - 基础URL
 */
setDocument(pdfDocument, baseUrl = null) {} // 空实现，不做任何事情
        </code></pre>
      </div>
    </div>
    
    <!-- 继承的方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">继承的方法</h3>
      </div>
      <div class="method-content">
        <p>SimpleLinkService继承自PDFLinkService，因此继承了以下重要方法（除了被覆盖的方法外）：</p>
        <ul>
          <li><strong>setViewer(pdfViewer)</strong> - 设置PDF查看器实例</li>
          <li><strong>setHistory(pdfHistory)</strong> - 设置PDF历史记录实例</li>
          <li><strong>goToDestination(dest)</strong> - 跳转到指定目标位置</li>
          <li><strong>goToPage(val)</strong> - 跳转到指定页码</li>
          <li><strong>addLinkAttributes(link, url, newWindow)</strong> - 为链接元素添加属性</li>
        </ul>
        <p>在SimpleLinkService中，由于没有设置文档和相关组件，这些继承的方法通常不会产生实际效果。</p>
      </div>
    </div>
    
    <!-- 继承的属性 -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">继承的属性</h3>
      </div>
      <div class="method-content">
        <p>SimpleLinkService继承了PDFLinkService的以下属性：</p>
        <ul>
          <li><strong>externalLinkEnabled</strong> - 控制是否启用外部链接</li>
          <li><strong>pagesCount</strong> - 获取PDF文档的页面总数</li>
          <li><strong>page</strong> - 获取或设置当前页码</li>
          <li><strong>rotation</strong> - 获取或设置页面旋转角度</li>
          <li><strong>isInPresentationMode</strong> - 检查是否处于演示模式</li>
        </ul>
      </div>
    </div>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <!-- SimpleLinkService类继承关系图 -->
    <div class="mermaid">
      classDiagram
        PDFLinkService <|-- SimpleLinkService
        class PDFLinkService {
          +externalLinkEnabled
          +constructor(options)
          +setDocument(pdfDocument, baseUrl)
          +setViewer(pdfViewer)
          +setHistory(pdfHistory)
          +goToDestination(dest)
          +goToPage(val)
          +addLinkAttributes(link, url, newWindow)
        }
        class SimpleLinkService {
          +setDocument(pdfDocument, baseUrl) // 覆盖方法
        }
    </div>
    
    <!-- 链接服务使用流程图 -->
    <div class="mermaid">
      graph TD
        A["创建SimpleLinkService实例"] --> B["传递给需要链接服务的组件"]
        B --> C{"是否需要处理用户交互?"}
        C -->|"否"| D["使用SimpleLinkService<br>提供基本链接功能"]
        C -->|"是"| E["使用完整的PDFLinkService<br>支持导航和历史"]
        D --> F["仅用于渲染和显示"]
        E --> G["支持完整的用户交互"]
    </div>
  </div>

  <!-- 使用示例 -->
  <div id="usage-example">
    <h2>使用示例</h2>
    <p>以下是SimpleLinkService在PDF.js中的典型使用方式：</p>
    <pre><code class="language-javascript">
// 创建SimpleLinkService实例
const linkService = new SimpleLinkService();

// 在不需要完整导航功能的组件中使用
const annotationLayer = new AnnotationLayerBuilder({
  pageDiv: pageDiv,
  pdfPage: pdfPage,
  linkService: linkService,
  downloadManager: null
});

// 或者在创建组件时作为默认选项
this.linkService = options.linkService || new SimpleLinkService();

// 即使调用setDocument方法也不会有实际操作
linkService.setDocument(pdfDocument);
    </code></pre>
  </div>

  <!-- 实现细节 -->
  <div id="implementation-details">
    <h2>实现细节</h2>
    <p>SimpleLinkService的实现遵循了以下设计原则：</p>
    <ul>
      <li><strong>最小化原则</strong>：通过继承PDFLinkService并覆盖关键方法为空实现，提供最小化的功能集。</li>
      <li><strong>接口兼容性</strong>：保持与PDFLinkService相同的接口签名，使其可以在期望PDFLinkService的地方使用。</li>
      <li><strong>非交互设计</strong>：专为不需要用户交互的场景设计，如PDF预览或静态渲染。</li>
    </ul>
    <p>这种简化的实现使得SimpleLinkService特别适合以下场景：</p>
    <ul>
      <li>仅需展示PDF而不需要跳转功能的嵌入式查看器</li>
      <li>服务器端渲染或预览生成</li>
      <li>需要最小化JavaScript足迹的轻量级实现</li>
      <li>作为组件的默认链接服务，当没有提供完整PDFLinkService时使用</li>
    </ul>
  </div>

  <!-- 注意事项 -->
  <div id="notes">
    <h2>注意事项</h2>
    <p>在使用SimpleLinkService时，需要注意以下几点：</p>
    <ul>
      <li>由于setDocument方法被覆盖为空实现，SimpleLinkService不会保存对PDF文档的引用，因此依赖文档的功能（如页面导航）将不可用。</li>
      <li>继承的方法如goToDestination和goToPage通常不会产生实际效果，因为没有设置文档和查看器组件。</li>
      <li>SimpleLinkService主要用于简化组件初始化，在需要完整功能的场景中应使用标准的PDFLinkService。</li>
      <li>虽然功能受限，但SimpleLinkService在API结构上与PDFLinkService保持兼容，可以在同一代码库中交替使用。</li>
    </ul>
  </div>

  <!-- 返回顶部按钮 -->
  <button class="back-to-top">↑</button>

  <script>
    // 创建目录
    createTableOfContents();
  </script>
</body>
</html> 