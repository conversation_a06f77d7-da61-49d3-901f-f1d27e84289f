# PDF.js 文档系统

本文档系统基于 PDF.js 源代码自动生成，包含了模块注释、方法代码以及流程图。

## 已完成的模块文档

- [AltTextManager](alt_text_manager_doc.html) - 替代文本管理器
- [PDFLinkService](pdf_link_service_doc.html) - PDF链接服务
- [PDFThumbnailViewer](pdf_thumbnail_viewer_doc.html) - PDF缩略图查看器
- [PDFViewer](pdf_viewer_doc.html) - PDF查看器核心组件
- [PDFPageView](pdf_page_view_doc.html) - PDF页面视图组件

## 目录结构

```
document/
  ├── index.html           # 文档系统入口页面
  ├── doc_styles.css       # 共用样式表
  ├── doc_script.js        # 共用JavaScript脚本
  ├── template.html        # 文档模板文件
  ├── xxx_doc.md           # 各模块的Markdown文档
  └── xxx_doc.html         # 各模块的HTML文档
```

## 文档特点

1. **原始注释保留**：所有的文档都基于源代码中的注释生成，保留了原始开发者的意图。
2. **方法代码展示**：包含了每个方法的完整代码，便于理解实现细节。
3. **Mermaid流程图**：通过流程图直观展示各个模块和方法的工作流程。
4. **导航系统**：提供了方便的导航和搜索功能，方便查找特定模块。

## 如何添加新的模块文档

1. 从源代码中提取模块注释和代码
2. 使用`template.html`作为模板创建新的HTML文档
3. 创建对应的Markdown文档
4. 在`index.html`中的`modules`数组中添加新模块的信息
5. 根据代码逻辑创建适当的Mermaid流程图

## Mermaid流程图语法

流程图使用Mermaid语法创建，基本语法如下：

```
graph TD
  A[开始] --> B{条件判断}
  B -->|条件为真| C[处理1]
  B -->|条件为假| D[处理2]
  C --> E[结束]
  D --> E
```

更多语法请参考 [Mermaid官方文档](https://mermaid-js.github.io/mermaid/#/)。

## 注意事项

- 所有文档均基于现有代码注释，不会修改原始代码
- 流程图需要根据实际代码逻辑手动调整
- 文档分页时会保留完整的模块边界，不会拆分单个模块

## 维护与更新

当源代码更新时，可以按照以下步骤更新文档：

1. 对比源代码变更
2. 更新对应的Markdown和HTML文档
3. 如果模块逻辑有变化，调整对应的流程图
4. 如果有新增模块，按照上述步骤添加新的文档页面 