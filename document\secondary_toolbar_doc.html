<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SecondaryToolbar - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#constants">常量</a>
    <a href="#properties">属性</a>
    <a href="#methods">方法</a>
    <a href="#flowcharts">流程图</a>
    <a href="#examples">示例</a>
    <a href="#notes">注意事项</a>
  </div>

  <h1>SecondaryToolbar 模块文档</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>SecondaryToolbar 是 PDF.js 库中提供额外功能按钮的次要工具栏组件。它通常作为主工具栏的补充，包含一些不常用但仍然重要的功能，如文档属性、页面旋转、光标工具切换、滚动模式和展开模式设置等。次要工具栏通常以浮动面板的形式显示，可以通过主工具栏中的按钮触发显示或隐藏。此组件扩展了 PDF 查看器的功能，提供了更多高级选项，同时保持主界面的简洁。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 常量 -->
  <div id="constants">
    <h2>常量</h2>
    
    <p>SecondaryToolbar 使用以下常量：</p>
    
    <ul>
      <li>
        <code>DELAY_BEFORE_HIDING_CONTROLS</code>: 隐藏控件前的延迟时间（毫秒）
      </li>
      <li>
        <code>BUTTONS_SCROLL_STEP</code>: 按钮滚动步长
      </li>
      <li>
        <code>SidebarView</code>: 侧边栏视图枚举
        <ul>
          <li><code>NONE</code>: 无侧边栏</li>
          <li><code>THUMBS</code>: 缩略图视图</li>
          <li><code>OUTLINE</code>: 大纲视图</li>
          <li><code>ATTACHMENTS</code>: 附件视图</li>
          <li><code>LAYERS</code>: 图层视图</li>
        </ul>
      </li>
      <li>
        <code>CursorTool</code>: 光标工具枚举
        <ul>
          <li><code>SELECT</code>: 选择工具</li>
          <li><code>HAND</code>: 手形工具</li>
          <li><code>ZOOM</code>: 缩放工具</li>
        </ul>
      </li>
      <li>
        <code>ScrollMode</code>: 滚动模式枚举
        <ul>
          <li><code>VERTICAL</code>: 垂直滚动</li>
          <li><code>HORIZONTAL</code>: 水平滚动</li>
          <li><code>WRAPPED</code>: 环绕滚动</li>
          <li><code>PAGE</code>: 页面滚动</li>
        </ul>
      </li>
      <li>
        <code>SpreadMode</code>: 展开模式枚举
        <ul>
          <li><code>NONE</code>: 无展开</li>
          <li><code>ODD</code>: 奇数页展开</li>
          <li><code>EVEN</code>: 偶数页展开</li>
        </ul>
      </li>
    </ul>
  </div>

  <!-- 属性 -->
  <div id="properties">
    <h2>属性</h2>
    
    <h3>公共属性</h3>
    <ul>
      <li><code>toolbar</code>: 次要工具栏 DOM 元素</li>
      <li><code>opened</code>: 次要工具栏是否已打开</li>
      <li><code>toggleButton</code>: 切换按钮</li>
      <li><code>presentationModeButton</code>: 演示模式按钮</li>
      <li><code>openFileButton</code>: 打开文件按钮</li>
      <li><code>printButton</code>: 打印按钮</li>
      <li><code>downloadButton</code>: 下载按钮</li>
      <li><code>viewBookmarkButton</code>: 查看书签按钮</li>
      <li><code>firstPageButton</code>: 第一页按钮</li>
      <li><code>lastPageButton</code>: 最后一页按钮</li>
      <li><code>pageRotateCwButton</code>: 顺时针旋转页面按钮</li>
      <li><code>pageRotateCcwButton</code>: 逆时针旋转页面按钮</li>
      <li><code>cursorSelectToolButton</code>: 光标选择工具按钮</li>
      <li><code>cursorHandToolButton</code>: 光标手形工具按钮</li>
      <li><code>scrollPageButton</code>: 页面滚动按钮</li>
      <li><code>scrollVerticalButton</code>: 垂直滚动按钮</li>
      <li><code>scrollHorizontalButton</code>: 水平滚动按钮</li>
      <li><code>scrollWrappedButton</code>: 环绕滚动按钮</li>
      <li><code>spreadNoneButton</code>: 无展开按钮</li>
      <li><code>spreadOddButton</code>: 奇数页展开按钮</li>
      <li><code>spreadEvenButton</code>: 偶数页展开按钮</li>
      <li><code>documentPropertiesButton</code>: 文档属性按钮</li>
      <li><code>findButton</code>: 查找按钮</li>
      <li><code>attachmentsButton</code>: 附件按钮</li>
      <li><code>layersButton</code>: 图层按钮</li>
      <li><code>thumbsButton</code>: 缩略图按钮</li>
      <li><code>outlineButton</code>: 大纲按钮</li>
    </ul>

    <h3>私有属性</h3>
    <ul>
      <li><code>#eventBus</code>: 事件总线</li>
      <li><code>#pdfScriptingManager</code>: PDF 脚本管理器</li>
      <li><code>#mainContainer</code>: 主容器元素</li>
      <li><code>#contextMenuInactiveTimer</code>: 上下文菜单非活动计时器</li>
      <li><code>#isLibOpened</code>: 库是否已打开</li>
      <li><code>#close</code>: 关闭按钮元素</li>
      <li><code>#toggleButton</code>: 切换按钮元素</li>
      <li><code>#buttons</code>: 按钮集合</li>
      <li><code>#items</code>: 项目集合</li>
      <li><code>#wasLocalized</code>: 是否已本地化</li>
    </ul>
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法</h2>
    
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">构造函数</h3>
      </div>
      <div class="method-content">
        <p>创建一个新的 SecondaryToolbar 实例。</p>
        <pre><code class="language-javascript">
constructor({
  pdfViewer,
  eventBus,
  externalServices = null,
  toggleElement = null,
  pdfScriptingManager = null,
})
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>pdfViewer</code>: PDF 查看器实例</li>
          <li><code>eventBus</code>: 事件总线，用于与其他组件通信</li>
          <li><code>externalServices</code>: 外部服务对象 (可选，默认为 null)</li>
          <li><code>toggleElement</code>: 切换次要工具栏显示/隐藏的元素 (可选，默认为 null)</li>
          <li><code>pdfScriptingManager</code>: PDF 脚本管理器 (可选，默认为 null)</li>
        </ul>
      </div>
    </div>

    <h3>核心方法</h3>
    <ul>
      <li><code>setPageNumber(pageNumber)</code>: 设置当前页码</li>
      <li><code>setPagesCount(pagesCount)</code>: 设置文档总页数</li>
      <li><code>reset()</code>: 重置次要工具栏状态</li>
      <li><code>updateLoadingIndicator(loading = false)</code>: 更新加载指示器状态</li>
      <li><code>open()</code>: 打开次要工具栏</li>
      <li><code>close()</code>: 关闭次要工具栏</li>
      <li><code>toggle()</code>: 切换次要工具栏的显示状态</li>
    </ul>
    
    <h3>事件处理方法</h3>
    <ul>
      <li><code>#bindListeners()</code>: 绑定事件监听器</li>
      <li><code>#bindExternalListeners()</code>: 绑定外部事件监听器</li>
      <li><code>#addEventListeners()</code>: 添加事件监听器</li>
      <li><code>#removeEventListeners()</code>: 移除事件监听器</li>
      <li><code>#bindPresentationModeListener(eventBus)</code>: 绑定演示模式监听器</li>
      <li><code>#handleEvent(evt)</code>: 处理事件</li>
    </ul>
    
    <h3>UI 相关方法</h3>
    <ul>
      <li><code>#createButton(id, eventBus, eventName, styleClass, prefLabel)</code>: 创建按钮</li>
      <li><code>#updateUIState()</code>: 更新 UI 状态</li>
      <li><code>#dispatchEvent(evt)</code>: 分发事件</li>
      <li><code>#isElementVisible(element)</code>: 检查元素是否可见</li>
      <li><code>#_updateButtonStyle(button, enabled)</code>: 更新按钮样式</li>
      <li><code>#_localize()</code>: 本地化次要工具栏</li>
    </ul>
    
    <h3>功能方法</h3>
    <ul>
      <li><code>#firstPage()</code>: 跳转到第一页</li>
      <li><code>#lastPage()</code>: 跳转到最后一页</li>
      <li><code>#pageRotateCw()</code>: 顺时针旋转页面</li>
      <li><code>#pageRotateCcw()</code>: 逆时针旋转页面</li>
      <li><code>#switchScrollMode(mode)</code>: 切换滚动模式</li>
      <li><code>#switchSpreadMode(mode)</code>: 切换展开模式</li>
      <li><code>#switchCursorTool(tool)</code>: 切换光标工具</li>
      <li><code>#switchSidebarView(view)</code>: 切换侧边栏视图</li>
      <li><code>#showDocumentProperties()</code>: 显示文档属性</li>
      <li><code>#showFindBar()</code>: 显示查找栏</li>
    </ul>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <h3>次要工具栏初始化流程</h3>
    <p>1. 创建次要工具栏实例<br>
    2. 初始化 DOM 元素<br>
    3. 绑定事件监听器<br>
    4. 本地化工具栏<br>
    5. 设置初始状态</p>
    
    <div class="mermaid">
      graph TD
          A[创建 SecondaryToolbar 实例] --> B[构造函数执行]
          B --> C[初始化属性]
          C --> D[获取容器内的 DOM 元素引用]
          D --> E[创建和初始化按钮]
          E --> F[绑定事件监听器]
          F --> G[绑定外部事件监听器]
          G --> H[绑定演示模式监听器]
          H --> I[本地化工具栏文本]
          I --> J[设置初始状态]
          J --> K[初始化完成]
    </div>
    
    <h3>事件处理流程</h3>
    <p>1. 用户与次要工具栏交互<br>
    2. 触发事件<br>
    3. 次要工具栏处理事件<br>
    4. 转发命令到相应模块<br>
    5. 更新 UI 状态</p>
    
    <div class="mermaid">
      graph TD
          A[用户与次要工具栏交互] --> B{交互类型}
          B -->|点击第一页/最后一页按钮| C[跳转到相应页面]
          B -->|点击旋转按钮| D[旋转页面]
          B -->|点击光标工具按钮| E[切换光标工具]
          B -->|点击滚动模式按钮| F[切换滚动模式]
          B -->|点击展开模式按钮| G[切换展开模式]
          B -->|点击侧边栏视图按钮| H[切换侧边栏视图]
          B -->|点击文档属性按钮| I[显示文档属性]
          B -->|点击查找按钮| J[显示查找栏]
          B -->|点击关闭按钮| K[关闭次要工具栏]
          C --> L[通过事件总线分发事件]
          D --> L
          E --> L
          F --> L
          G --> L
          H --> L
          I --> L
          J --> L
          K --> M[隐藏次要工具栏]
          L --> N[更新工具栏状态]
          M --> N
    </div>
    
    <h3>工具栏状态更新流程</h3>
    <p>1. 接收更新事件<br>
    2. 更新按钮状态<br>
    3. 处理模式切换<br>
    4. 反映当前状态到 UI</p>
    
    <div class="mermaid">
      graph TD
          A[外部事件触发状态更新] --> B{事件类型}
          B -->|rotationchanging| C[更新旋转按钮状态]
          B -->|scrollmodechanged| D[更新滚动模式按钮状态]
          B -->|spreadmodechanged| E[更新展开模式按钮状态]
          B -->|sidebarviewchanged| F[更新侧边栏视图按钮状态]
          B -->|cursortoolchanged| G[更新光标工具按钮状态]
          B -->|documentloaded| H[重置工具栏状态]
          B -->|updatefromsandbox| I[从沙盒更新状态]
          C --> J[调用 updateUIState]
          D --> J
          E --> J
          F --> J
          G --> J
          H --> K[调用 reset 方法]
          I --> L[根据沙盒状态更新 UI]
          K --> J
          L --> J
    </div>
  </div>

  <!-- 示例 -->
  <div id="examples">
    <h2>使用示例</h2>
    
    <h3>基本用法</h3>
    <pre><code class="language-javascript">
// 创建事件总线
const eventBus = new EventBus();

// 创建 PDF 查看器
const pdfViewer = new PDFViewer({
  container: document.getElementById('viewerContainer'),
  eventBus
});

// 创建 SecondaryToolbar 实例
const secondaryToolbar = new SecondaryToolbar({
  pdfViewer,
  eventBus,
  toggleElement: document.getElementById('secondaryToolbarToggle')
});

// 监听事件并与其他组件交互
eventBus.on('rotationchanging', function(evt) {
  console.log('页面旋转改变:', evt.pagesRotation);
});

// 设置页码和总页数
secondaryToolbar.setPageNumber(1);
secondaryToolbar.setPagesCount(10);

// 打开次要工具栏
secondaryToolbar.open();

// 关闭次要工具栏
// secondaryToolbar.close();

// 切换次要工具栏显示状态
// secondaryToolbar.toggle();
    </code></pre>
    
    <h3>自定义次要工具栏</h3>
    <pre><code class="language-javascript">
// 扩展次要工具栏类，添加自定义功能
class CustomSecondaryToolbar extends SecondaryToolbar {
  constructor(options) {
    super(options);
    
    // 添加自定义属性
    this.customFeatureEnabled = false;
    
    // 在初始化完成后添加自定义按钮
    this._addCustomButtons();
  }
  
  // 添加自定义按钮
  _addCustomButtons() {
    // 创建自定义分隔线
    const separator = document.createElement('div');
    separator.className = 'horizontalToolbarSeparator';
    this.toolbar.appendChild(separator);
    
    // 创建自定义功能按钮
    const customButton = document.createElement('button');
    customButton.className = 'secondaryToolbarButton customFeature';
    customButton.title = '自定义功能';
    customButton.setAttribute('tabindex', '68');
    
    const buttonLabel = document.createElement('span');
    buttonLabel.textContent = '自定义功能';
    customButton.appendChild(buttonLabel);
    
    // 添加点击事件
    customButton.addEventListener('click', () => {
      this._toggleCustomFeature();
    });
    
    // 将按钮添加到工具栏
    this.toolbar.appendChild(customButton);
    
    // 保存按钮引用
    this.customFeatureButton = customButton;
  }
  
  // 切换自定义功能
  _toggleCustomFeature() {
    this.customFeatureEnabled = !this.customFeatureEnabled;
    
    // 根据状态更新按钮外观
    if (this.customFeatureEnabled) {
      this.customFeatureButton.classList.add('toggled');
    } else {
      this.customFeatureButton.classList.remove('toggled');
    }
    
    // 通过事件总线通知其他组件
    this.#eventBus.dispatch('customfeaturechanged', {
      source: this,
      enabled: this.customFeatureEnabled
    });
  }
  
  // 重写重置方法以包含自定义功能
  reset() {
    super.reset();
    
    // 重置自定义功能状态
    this.customFeatureEnabled = false;
    if (this.customFeatureButton) {
      this.customFeatureButton.classList.remove('toggled');
    }
  }
  
  // 添加新的功能方法
  #executeCustomFeature() {
    console.log('执行自定义功能');
    
    // 实现自定义功能逻辑
    this.#eventBus.dispatch('customfeatureexecuted', {
      source: this
    });
  }
}

// 使用自定义次要工具栏
const customSecondaryToolbar = new CustomSecondaryToolbar({
  pdfViewer,
  eventBus,
  toggleElement: document.getElementById('secondaryToolbarToggle')
});

// 监听自定义事件
eventBus.on('customfeaturechanged', function(evt) {
  console.log('自定义功能状态变更:', evt.enabled);
  // 在这里实现状态变更的响应逻辑
});

eventBus.on('customfeatureexecuted', function(evt) {
  console.log('自定义功能执行');
  // 在这里实现功能执行的响应逻辑
});
    </code></pre>
    
    <h3>添加键盘快捷键支持</h3>
    <pre><code class="language-javascript">
// 扩展次要工具栏以支持键盘快捷键
function enhanceSecondaryToolbarWithKeyboardShortcuts(secondaryToolbar, eventBus) {
  // 定义快捷键映射
  const keyboardShortcuts = {
    'home': secondaryToolbar.firstPage.bind(secondaryToolbar),     // Home 键: 跳转到第一页
    'end': secondaryToolbar.lastPage.bind(secondaryToolbar),       // End 键: 跳转到最后一页
    'r': secondaryToolbar.pageRotateCw.bind(secondaryToolbar),     // R 键: 顺时针旋转
    'shift+r': secondaryToolbar.pageRotateCcw.bind(secondaryToolbar), // Shift+R: 逆时针旋转
    'h': () => secondaryToolbar.switchCursorTool(CursorTool.HAND), // H 键: 切换到手形工具
    's': () => secondaryToolbar.switchCursorTool(CursorTool.SELECT), // S 键: 切换到选择工具
    'v': () => secondaryToolbar.switchScrollMode(ScrollMode.VERTICAL), // V 键: 垂直滚动模式
    'h': () => secondaryToolbar.switchScrollMode(ScrollMode.HORIZONTAL), // H 键: 水平滚动模式
    'w': () => secondaryToolbar.switchScrollMode(ScrollMode.WRAPPED), // W 键: 环绕滚动模式
    'p': () => secondaryToolbar.switchScrollMode(ScrollMode.PAGE),  // P 键: 页面滚动模式
    '0': () => secondaryToolbar.switchSpreadMode(SpreadMode.NONE),  // 0 键: 无展开模式
    '1': () => secondaryToolbar.switchSpreadMode(SpreadMode.ODD),   // 1 键: 奇数页展开
    '2': () => secondaryToolbar.switchSpreadMode(SpreadMode.EVEN),  // 2 键: 偶数页展开
    'ctrl+d': secondaryToolbar.showDocumentProperties.bind(secondaryToolbar), // Ctrl+D: 显示文档属性
    'ctrl+alt+t': () => secondaryToolbar.switchSidebarView(SidebarView.THUMBS), // Ctrl+Alt+T: 缩略图视图
    'ctrl+alt+o': () => secondaryToolbar.switchSidebarView(SidebarView.OUTLINE), // Ctrl+Alt+O: 大纲视图
    'ctrl+alt+a': () => secondaryToolbar.switchSidebarView(SidebarView.ATTACHMENTS), // Ctrl+Alt+A: 附件视图
    'ctrl+alt+l': () => secondaryToolbar.switchSidebarView(SidebarView.LAYERS) // Ctrl+Alt+L: 图层视图
  };
  
  // 创建快捷键处理器
  function handleKeyDown(event) {
    // 如果用户正在输入文本，不处理快捷键
    if (event.target.tagName === 'INPUT' || 
        event.target.tagName === 'TEXTAREA' || 
        event.target.tagName === 'SELECT') {
      return;
    }
    
    // 构建快捷键字符串
    let shortcut = '';
    if (event.ctrlKey) shortcut += 'ctrl+';
    if (event.altKey) shortcut += 'alt+';
    if (event.shiftKey) shortcut += 'shift+';
    shortcut += event.key.toLowerCase();
    
    // 检查是否有对应的快捷键操作
    const action = keyboardShortcuts[shortcut];
    if (action) {
      event.preventDefault();
      action();
    }
  }
  
  // 添加键盘事件监听器
  document.addEventListener('keydown', handleKeyDown);
  
  // 在事件总线中分发键盘快捷键事件
  eventBus.dispatch('keyboardshortcutsadded', {
    source: secondaryToolbar
  });
  
  // 返回移除快捷键功能的函数
  return function removeKeyboardShortcuts() {
    document.removeEventListener('keydown', handleKeyDown);
    
    eventBus.dispatch('keyboardshortcutsremoved', {
      source: secondaryToolbar
    });
  };
}

// 使用键盘快捷键增强次要工具栏
const removeKeyboardShortcuts = enhanceSecondaryToolbarWithKeyboardShortcuts(secondaryToolbar, eventBus);

// 监听键盘快捷键事件
eventBus.on('keyboardshortcutsadded', function() {
  console.log('键盘快捷键已添加');
  
  // 显示快捷键提示
  showKeyboardShortcutsTooltip();
});

// 显示键盘快捷键提示
function showKeyboardShortcutsTooltip() {
  const tooltip = document.createElement('div');
  tooltip.className = 'keyboard-shortcuts-tooltip';
  tooltip.innerHTML = `
    <h3>键盘快捷键</h3>
    <ul>
      <li><strong>Home</strong>: 跳转到第一页</li>
      <li><strong>End</strong>: 跳转到最后一页</li>
      <li><strong>R</strong>: 顺时针旋转</li>
      <li><strong>Shift+R</strong>: 逆时针旋转</li>
      <li><strong>H</strong>: 切换到手形工具</li>
      <li><strong>S</strong>: 切换到选择工具</li>
      <li><strong>V</strong>: 垂直滚动模式</li>
      <li><strong>H</strong>: 水平滚动模式</li>
      <li><strong>W</strong>: 环绕滚动模式</li>
      <li><strong>P</strong>: 页面滚动模式</li>
      <li><strong>0</strong>: 无展开模式</li>
      <li><strong>1</strong>: 奇数页展开</li>
      <li><strong>2</strong>: 偶数页展开</li>
      <li><strong>Ctrl+D</strong>: 显示文档属性</li>
      <li><strong>Ctrl+Alt+T</strong>: 缩略图视图</li>
      <li><strong>Ctrl+Alt+O</strong>: 大纲视图</li>
      <li><strong>Ctrl+Alt+A</strong>: 附件视图</li>
      <li><strong>Ctrl+Alt+L</strong>: 图层视图</li>
    </ul>
    <button class="close-tooltip">关闭</button>
  `;
  
  document.body.appendChild(tooltip);
  
  // 添加样式
  const style = document.createElement('style');
  style.textContent = `
    .keyboard-shortcuts-tooltip {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: white;
      border: 1px solid #ccc;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
      padding: 20px;
      z-index: 1000;
      max-width: 400px;
      max-height: 80vh;
      overflow-y: auto;
      border-radius: 4px;
    }
    
    .keyboard-shortcuts-tooltip h3 {
      margin-top: 0;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }
    
    .keyboard-shortcuts-tooltip ul {
      padding-left: 20px;
    }
    
    .keyboard-shortcuts-tooltip li {
      margin-bottom: 8px;
    }
    
    .keyboard-shortcuts-tooltip .close-tooltip {
      display: block;
      margin: 0 auto;
      padding: 8px 16px;
      background: #f0f0f0;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    
    .keyboard-shortcuts-tooltip .close-tooltip:hover {
      background: #e0e0e0;
    }
  `;
  
  document.head.appendChild(style);
  
  // 添加关闭按钮事件
  tooltip.querySelector('.close-tooltip').addEventListener('click', function() {
    document.body.removeChild(tooltip);
  });
}
    </code></pre>
  </div>

  <!-- 注意事项 -->
  <div id="notes">
    <h2>注意事项</h2>
    
    <ul>
      <li>次要工具栏是对主工具栏的补充，应包含不常用但仍然重要的功能，避免在主工具栏中放置过多按钮。</li>
      <li>次要工具栏的设计应保持与主工具栏的视觉一致性，使用相同的样式和交互模式。</li>
      <li>次要工具栏应设计为可隐藏的面板，在不需要时不占用屏幕空间，提高界面的简洁性。</li>
      <li>在移动设备上，次要工具栏的布局可能需要调整，按钮应足够大，以适应触摸操作。</li>
      <li>次要工具栏应支持键盘导航和辅助功能，确保所有用户都能够轻松操作。</li>
      <li>当次要工具栏处于打开状态时，点击工具栏外部区域应自动关闭工具栏，以改善用户体验。</li>
      <li>考虑为次要工具栏中的功能添加键盘快捷键，提高高级用户的操作效率。</li>
      <li>次要工具栏应明确反映当前活动的状态，如当前的滚动模式、展开模式和光标工具等。</li>
      <li>次要工具栏应支持国际化和本地化，以适应不同语言环境的用户。</li>
      <li>在实现自定义次要工具栏功能时，应保持与 PDF.js 其他组件的兼容性，确保事件和参数传递正确。</li>
    </ul>
  </div>

  <script>
    // 在页面加载完成后初始化 Mermaid
    document.addEventListener('DOMContentLoaded', function() {
      mermaid.initialize({ startOnLoad: true });
      
      // 生成目录
      generateTOC();
    });
  </script>
</body>
</html>
