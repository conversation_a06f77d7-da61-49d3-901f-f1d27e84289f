<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>L10n - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#properties">属性</a>
    <a href="#methods">方法</a>
    <a href="#flowcharts">流程图</a>
    <a href="#examples">示例</a>
    <a href="#notes">注意事项</a>
  </div>

  <h1>L10n 模块文档</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>L10n（Localization 的缩写）是 PDF.js 库中的本地化模块，负责处理界面文本的翻译和文化适配。该模块提供了一套简洁的 API 来获取和显示不同语言的界面文本，支持动态切换语言、处理文本方向（从左到右或从右到左）以及参数化文本模板。L10n 模块是 PDF.js 实现国际化支持的核心组件。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 属性 -->
  <div id="properties">
    <h2>属性</h2>
    
    <h3>公共属性</h3>
    <ul>
      <li>无直接暴露的公共属性，所有交互通过方法完成</li>
    </ul>

    <h3>私有属性</h3>
    <ul>
      <li><code>#dir</code>: 文本方向，值为 "ltr"（从左到右）或 "rtl"（从右到左）</li>
      <li><code>#lang</code>: 当前语言代码，如 "en-us"、"zh-cn" 等</li>
      <li><code>#l10n</code>: 内部使用的 Fluent 本地化实例</li>
      <li><code>#elements</code>: 已连接的 DOM 元素集合，用于自动翻译</li>
    </ul>
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法</h2>
    
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">构造函数</h3>
      </div>
      <div class="method-content">
        <p>创建一个新的 L10n 实例。</p>
        <pre><code class="language-javascript">
constructor({
  lang,
  isRTL
}, l10n = null) {
  // 修正语言代码格式
  this.#lang = L10n.#fixupLangCode(lang);
  // 保存本地化实例
  this.#l10n = l10n;
  // 确定文本方向：如果明确指定isRTL则使用它，否则根据语言判断
  this.#dir = isRTL ?? L10n.#isRTL(this.#lang) ? "rtl" : "ltr";
}
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>options</code>: 本地化选项对象</li>
          <li><code>options.lang</code>: 语言代码，如 "en-us"、"zh-cn" 等</li>
          <li><code>options.isRTL</code>: （可选）是否为从右到左文本方向</li>
          <li><code>l10n</code>: （可选）预配置的 Fluent 本地化实例</li>
        </ul>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">_setL10n(l10n)</h3>
      </div>
      <div class="method-content">
        <p>设置内部使用的 Fluent 本地化实例，通常由继承类在初始化时调用。</p>
        <pre><code class="language-javascript">
_setL10n(l10n) {
  this.#l10n = l10n;
}
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>l10n</code>: Fluent 本地化实例</li>
        </ul>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">getLanguage()</h3>
      </div>
      <div class="method-content">
        <p>获取当前语言代码。</p>
        <pre><code class="language-javascript">
getLanguage() {
  return this.#lang;
}
        </code></pre>
        <p><strong>返回值:</strong></p>
        <ul>
          <li>当前语言代码字符串，如 "en-us"、"zh-cn" 等</li>
        </ul>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">getDirection()</h3>
      </div>
      <div class="method-content">
        <p>获取当前文本方向。</p>
        <pre><code class="language-javascript">
getDirection() {
  return this.#dir;
}
        </code></pre>
        <p><strong>返回值:</strong></p>
        <ul>
          <li>文本方向字符串，"ltr"（从左到右）或 "rtl"（从右到左）</li>
        </ul>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">async get(ids, args = null, fallback)</h3>
      </div>
      <div class="method-content">
        <p>获取翻译消息，支持单个消息 ID 或消息 ID 数组。</p>
        <pre><code class="language-javascript">
async get(ids, args = null, fallback) {
  // 处理ID数组的情况
  if (Array.isArray(ids)) {
    // 将ID数组转换为消息键数组
    ids = ids.map(id => ({
      id
    }));
    // 格式化所有消息
    const messages = await this.#l10n.formatMessages(ids);
    // 提取并返回所有消息值
    return messages.map(message => message.value);
  }
  
  // 处理单个ID的情况
  const messages = await this.#l10n.formatMessages([{
    id: ids,
    args
  }]);
  // 返回消息值，如果未找到则返回备用文本
  return messages[0]?.value || fallback;
}
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>ids</code>: 消息 ID 或 ID 数组</li>
          <li><code>args</code>: （可选）消息参数，用于替换消息模板中的变量</li>
          <li><code>fallback</code>: （可选）当消息未找到时的备用文本</li>
        </ul>
        <p><strong>返回值:</strong></p>
        <ul>
          <li>翻译后的消息字符串或消息字符串数组</li>
        </ul>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">async translate(element)</h3>
      </div>
      <div class="method-content">
        <p>翻译指定的 DOM 元素，处理其中的本地化属性。</p>
        <pre><code class="language-javascript">
async translate(element) {
  // 确保元素有效
  if (!element || !this.#l10n) {
    return;
  }
  
  // 添加元素到连接列表
  if (!this.#elements) {
    this.#elements = new Set();
  }
  if (this.#elements.has(element)) {
    return;
  }
  
  // 连接元素到Fluent，并启动观察
  this.#l10n.connectRoot(element);
  this.#elements.add(element);
  await this.#l10n.translateRoots();
  
  // 恢复观察
  this.#l10n.resumeObserving();
}
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>element</code>: 要翻译的 DOM 元素</li>
        </ul>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">pause()</h3>
      </div>
      <div class="method-content">
        <p>暂停本地化处理，临时禁用自动翻译。</p>
        <pre><code class="language-javascript">
pause() {
  this.#l10n.pauseObserving();
}
        </code></pre>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">resume()</h3>
      </div>
      <div class="method-content">
        <p>恢复本地化处理，重新启用自动翻译。</p>
        <pre><code class="language-javascript">
resume() {
  this.#l10n.resumeObserving();
}
        </code></pre>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">async destroy()</h3>
      </div>
      <div class="method-content">
        <p>销毁本地化实例，释放资源。</p>
        <pre><code class="language-javascript">
async destroy() {
  // 如果有已连接的元素
  if (this.#elements) {
    // 断开所有连接的根元素
    for (const element of this.#elements) {
      this.#l10n.disconnectRoot(element);
    }
    // 清除元素集合
    this.#elements.clear();
    this.#elements = null;
  }
  
  // 暂停DOM观察
  this.#l10n.pauseObserving();
}
        </code></pre>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">static #fixupLangCode(langCode)</h3>
      </div>
      <div class="method-content">
        <p>修正语言代码格式，将简写的语言代码转换为完整格式。</p>
        <pre><code class="language-javascript">
static #fixupLangCode(langCode) {
  // 默认为英语（美国）
  langCode = langCode?.toLowerCase() || "en-us";
  
  // 简写语言代码到完整语言代码的映射
  const PARTIAL_LANG_CODES = {
    en: "en-us", // 英语 -> 英语（美国）
    es: "es-es", // 西班牙语 -> 西班牙语（西班牙）
    fy: "fy-nl", // 弗里斯兰语 -> 弗里斯兰语（荷兰）
    ga: "ga-ie", // 爱尔兰语 -> 爱尔兰语（爱尔兰）
    gu: "gu-in", // 古吉拉特语 -> 古吉拉特语（印度）
    hi: "hi-in", // 印地语 -> 印地语（印度）
    hy: "hy-am", // 亚美尼亚语 -> 亚美尼亚语（亚美尼亚）
    nb: "nb-no", // 书面挪威语 -> 书面挪威语（挪威）
    ne: "ne-np", // 尼泊尔语 -> 尼泊尔语（尼泊尔）
    nn: "nn-no", // 新挪威语 -> 新挪威语（挪威）
    pa: "pa-in", // 旁遮普语 -> 旁遮普语（印度）
    pt: "pt-pt", // 葡萄牙语 -> 葡萄牙语（葡萄牙）
    sv: "sv-se", // 瑞典语 -> 瑞典语（瑞典）
    zh: "zh-cn"  // 中文 -> 中文（中国）
  };
  
  // 如果有对应的完整代码则使用它，否则保持原样
  return PARTIAL_LANG_CODES[langCode] || langCode;
}
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>langCode</code>: 输入的语言代码</li>
        </ul>
        <p><strong>返回值:</strong></p>
        <ul>
          <li>修正后的语言代码</li>
        </ul>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">static #isRTL(lang)</h3>
      </div>
      <div class="method-content">
        <p>判断指定语言是否为从右到左（RTL）书写方向。</p>
        <pre><code class="language-javascript">
static #isRTL(lang) {
  // 从右到左书写的语言代码
  const RTL_LANGS = [
    "ar",   // 阿拉伯语
    "he",   // 希伯来语
    "fa",   // 波斯语
    "ur",   // 乌尔都语
    "ps",   // 普什图语
    "sd"    // 信德语
  ];
  
  // 提取主要语言部分（如从 "ar-eg" 提取 "ar"）
  const primaryLang = lang.split("-", 1)[0];
  
  // 检查是否为RTL语言
  return RTL_LANGS.includes(primaryLang);
}
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>lang</code>: 语言代码</li>
        </ul>
        <p><strong>返回值:</strong></p>
        <ul>
          <li>如果是RTL语言则返回 true，否则返回 false</li>
        </ul>
      </div>
    </div>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <h3>本地化初始化流程</h3>
    <p>1. 创建L10n实例<br>
    2. 初始化语言和方向<br>
    3. 设置Fluent实例<br>
    4. 开始翻译界面</p>
    
    <div class="mermaid">
      graph TD
        A[创建L10n实例] --> B[修正语言代码格式]
        B --> C{是否提供isRTL参数?}
        C -->|是| D[使用提供的方向值]
        C -->|否| E[根据语言代码判断方向]
        D --> F[设置#dir属性]
        E --> F
        F --> G{是否提供l10n实例?}
        G -->|是| H[使用提供的l10n实例]
        G -->|否| I[创建继承类时需要设置l10n实例]
        H --> J[L10n实例初始化完成]
        I --> J
    </div>
    
    <h3>消息获取流程</h3>
    <p>1. 调用get方法<br>
    2. 处理不同参数情况<br>
    3. 获取翻译消息<br>
    4. 返回结果</p>
    
    <div class="mermaid">
      graph TD
        A[调用get方法] --> B{是否为ID数组?}
        B -->|是| C[将ID数组转换为消息键数组]
        B -->|否| D[创建单个消息键]
        C --> E[调用formatMessages获取所有消息]
        D --> F[调用formatMessages获取单个消息]
        E --> G[提取所有消息值]
        F --> H{消息是否找到?}
        H -->|是| I[返回消息值]
        H -->|否| J[返回备用文本]
        G --> K[返回消息值数组]
        I --> L[完成消息获取]
        J --> L
        K --> L
    </div>
    
    <h3>DOM元素翻译流程</h3>
    <p>1. 调用translate方法<br>
    2. 连接DOM元素<br>
    3. 翻译元素内容<br>
    4. 启用变更观察</p>
    
    <div class="mermaid">
      graph TD
        A[调用translate方法] --> B{元素和l10n实例是否有效?}
        B -->|否| C[直接返回]
        B -->|是| D{元素集合是否存在?}
        D -->|否| E[创建新的元素集合]
        D -->|是| F{元素是否已连接?}
        E --> F
        F -->|是| C
        F -->|否| G[将元素连接到Fluent]
        G --> H[添加元素到集合]
        H --> I[翻译所有根元素]
        I --> J[恢复观察DOM变化]
    </div>
  </div>

  <!-- 示例 -->
  <div id="examples">
    <h2>使用示例</h2>
    
    <h3>基本用法</h3>
    <pre><code class="language-javascript">
// 创建通用本地化实例
class GenericL10n extends L10n {
  constructor(lang) {
    // 调用父类构造函数
    super({ lang });
    
    // 创建DOMLocalization实例
    const domL10n = new DOMLocalization([], async function* generateBundles() {
      // 在实际应用中，这里会加载语言资源文件
      const bundle = new FluentBundle(lang);
      // 添加一些测试消息
      bundle.addResource(new FluentResource(`
        welcome = 欢迎使用 PDF.js!
        page-num = 第 { $page } 页，共 { $total } 页
        loading = 加载中...
      `));
      yield bundle;
    });
    
    // 设置本地化实例
    this._setL10n(domL10n);
  }
}

// 创建本地化实例
const l10n = new GenericL10n('zh-cn');

// 使用本地化获取消息
async function showMessages() {
  // 获取简单消息
  const welcome = await l10n.get('welcome');
  console.log(welcome); // 输出: 欢迎使用 PDF.js!
  
  // 获取带参数的消息
  const pageMsg = await l10n.get('page-num', { page: 5, total: 20 });
  console.log(pageMsg); // 输出: 第 5 页，共 20 页
  
  // 使用备用文本
  const notFound = await l10n.get('not-exist', null, '未找到消息');
  console.log(notFound); // 输出: 未找到消息
}

// 调用示例函数
showMessages();
    </code></pre>
    
    <h3>翻译DOM元素</h3>
    <pre><code class="language-javascript">
// HTML:
// <div id="pdf-container">
//   <div data-l10n-id="welcome"></div>
//   <div data-l10n-id="page-num" data-l10n-args='{"page": 1, "total": 10}'></div>
//   <button data-l10n-id="next-button">下一页</button>
// </div>

// 获取容器元素
const container = document.getElementById('pdf-container');

// 翻译容器内的所有元素
l10n.translate(container).then(() => {
  console.log('翻译完成');
});

// 更新元素的本地化参数
function updatePageNumber(page, total) {
  const pageElement = container.querySelector('[data-l10n-id="page-num"]');
  pageElement.setAttribute('data-l10n-args', JSON.stringify({ page, total }));
  // Fluent观察器会自动更新翻译
}

// 更新页码
setTimeout(() => {
  updatePageNumber(2, 10);
}, 2000);

// 暂停和恢复本地化
function toggleLocalization() {
  const isPaused = document.getElementById('pause-toggle').checked;
  if (isPaused) {
    l10n.pause();
    console.log('本地化已暂停');
  } else {
    l10n.resume();
    console.log('本地化已恢复');
  }
}
    </code></pre>
    
    <h3>与PDF查看器集成</h3>
    <pre><code class="language-javascript">
class PDFViewerApplication {
  constructor() {
    this.l10n = null;
  }
  
  // 初始化本地化
  async initializeL10n() {
    // 从URL参数或本地存储获取语言设置
    const lang = this.getLanguage();
    
    // 创建本地化实例
    this.l10n = new GenericL10n(lang);
    
    // 设置文档方向
    document.documentElement.dir = this.l10n.getDirection();
    
    // 翻译整个文档
    await this.l10n.translate(document.body);
    
    return this.l10n;
  }
  
  // 获取语言设置
  getLanguage() {
    // 尝试从URL获取语言参数
    const params = new URLSearchParams(window.location.search);
    const langParam = params.get('lang');
    
    if (langParam) {
      return langParam;
    }
    
    // 尝试从本地存储获取语言设置
    const storedLang = localStorage.getItem('pdfjs.language');
    if (storedLang) {
      return storedLang;
    }
    
    // 使用浏览器语言
    return navigator.language || 'en-us';
  }
  
  // 更改语言
  async changeLanguage(newLang) {
    // 保存语言设置
    localStorage.setItem('pdfjs.language', newLang);
    
    // 重新加载页面应用新语言
    // 在实际应用中，可能会有更复杂的不刷新切换语言的逻辑
    window.location.reload();
  }
  
  // 动态更新界面元素的文本
  async updateUIText() {
    const pageInput = document.getElementById('pageNumber');
    const pagesTotal = document.getElementById('numPages');
    
    // 获取当前页码和总页数
    const currentPage = parseInt(pageInput.value, 10) || 1;
    const totalPages = this.pdfDocument ? this.pdfDocument.numPages : 0;
    
    // 更新标题
    document.title = await this.l10n.get('document-title', {
      title: this.documentTitle || document.title
    });
    
    // 更新页码提示
    pagesTotal.setAttribute('data-l10n-args', JSON.stringify({
      pagesCount: totalPages
    }));
  }
}

// 使用示例
const app = new PDFViewerApplication();
app.initializeL10n().then(() => {
  console.log('本地化初始化完成');
  app.updateUIText();
});
    </code></pre>
  </div>

  <!-- 注意事项 -->
  <div id="notes">
    <h2>注意事项</h2>
    
    <ul>
      <li>L10n 模块是一个基类，通常需要通过继承来提供具体的语言资源加载机制，如 GenericL10n 类。</li>
      <li>使用 <code>data-l10n-id</code> 属性标记需要翻译的 DOM 元素，使用 <code>data-l10n-args</code> 属性提供消息参数。</li>
      <li>参数化消息使用花括号语法，例如 <code>"共 { $total } 页"</code>，可以在 JavaScript 和 HTML 中提供参数值。</li>
      <li>模块自动处理从右到左（RTL）语言的文本方向，包括阿拉伯语、希伯来语等，确保界面布局正确。</li>
      <li>使用 <code>translate</code> 方法可以自动翻译整个 DOM 子树，不需要手动处理每个元素。</li>
      <li>调用 <code>get</code> 方法获取翻译消息是异步操作，需要使用 <code>async/await</code> 或 Promise 处理。</li>
      <li>当切换语言时，最佳实践是保存用户语言偏好到本地存储，并在下次加载时应用。</li>
      <li>模块支持语言代码修正，将简写代码（如 "en"）自动转换为完整形式（如 "en-us"）。</li>
      <li>使用 <code>pause</code> 和 <code>resume</code> 方法可以临时禁用自动翻译，在批量更新界面时很有用。</li>
      <li>在组件销毁时应调用 <code>destroy</code> 方法以释放资源，避免内存泄漏。</li>
    </ul>
  </div>

  <script>
    // 在页面加载完成后初始化 Mermaid
    document.addEventListener('DOMContentLoaded', function() {
      mermaid.initialize({ startOnLoad: true });
      
      // 生成目录
      createTableOfContents();
    });
  </script>
</body>
</html> 