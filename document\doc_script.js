// 文档页面共用脚本

// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
  // 返回顶部按钮功能
  const backToTopButton = document.querySelector('.back-to-top');
  
  if (backToTopButton) {
    // 监听滚动事件
    window.addEventListener('scroll', function() {
      if (window.pageYOffset > 300) {
        backToTopButton.style.display = 'block';
      } else {
        backToTopButton.style.display = 'none';
      }
    });
    
    // 点击返回顶部
    backToTopButton.addEventListener('click', function() {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    });
  }
  
  // 代码语法高亮
  highlightCode();
  
  // 初始化Mermaid图表
  initMermaid();
});

// 代码高亮函数
function highlightCode() {
  // 如果页面中有代码块，可以在这里添加语法高亮逻辑
  // 这个例子中我们只使用简单的样式，但你可以集成其他语法高亮库
  const codeBlocks = document.querySelectorAll('pre code');
  
  codeBlocks.forEach(block => {
    // 添加语言类
    const language = block.getAttribute('class') || 'language-javascript';
    block.classList.add(language);
    
    // 这里可以添加你的高亮逻辑
  });
}

// 初始化Mermaid流程图
function initMermaid() {
  // 检查mermaid是否已加载
  if (typeof mermaid !== 'undefined') {
    mermaid.initialize({
      startOnLoad: true,
      theme: 'default',
      securityLevel: 'loose',
      flowchart: {
        useMaxWidth: true,
        htmlLabels: true,
        curve: 'basis'
      }
    });
  }
}

// 创建目录
function createTableOfContents() {
  const toc = document.getElementById('toc');
  if (!toc) return;
  
  const headings = document.querySelectorAll('h2, h3');
  const tocList = document.createElement('ul');
  
  headings.forEach((heading, index) => {
    // 给每个标题添加ID
    if (!heading.id) {
      heading.id = `heading-${index}`;
    }
    
    const listItem = document.createElement('li');
    listItem.className = heading.tagName.toLowerCase();
    
    const link = document.createElement('a');
    link.href = `#${heading.id}`;
    link.textContent = heading.textContent;
    
    listItem.appendChild(link);
    tocList.appendChild(listItem);
  });
  
  toc.appendChild(tocList);
} 