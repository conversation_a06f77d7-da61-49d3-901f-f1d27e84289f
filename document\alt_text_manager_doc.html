<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AltTextManager - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#properties">属性列表</a>
    <a href="#methods">方法列表</a>
    <a href="#flowcharts">流程图</a>
  </div>

  <h1>AltTextManager</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>AltTextManager 是一个替代文本管理器，用于管理PDF中图像的替代文本（无障碍描述）。这是旧版实现，主要处理装饰性图片选项的情况。</p>
    <p>替代文本（Alt Text）是为图像提供的文本描述，主要用于帮助视障用户了解图像内容，提高文档的可访问性。</p>
  </div>

  <!-- 属性列表 -->
  <div id="properties">
    <h2>属性列表</h2>
    <p>该类使用私有属性管理其内部状态：</p>
    <ul>
      <li><code>#clickAC</code>: 点击事件中止控制器</li>
      <li><code>#currentEditor</code>: 当前编辑器对象</li>
      <li><code>#cancelButton</code>: 取消按钮</li>
      <li><code>#dialog</code>: 对话框元素</li>
      <li><code>#eventBus</code>: 事件总线</li>
      <li><code>#hasUsedPointer</code>: 是否使用过指针设备</li>
      <li><code>#optionDescription</code>: 描述选项按钮</li>
      <li><code>#optionDecorative</code>: 装饰性图像选项按钮</li>
      <li><code>#overlayManager</code>: 覆盖层管理器</li>
      <li><code>#saveButton</code>: 保存按钮</li>
      <li><code>#textarea</code>: 文本编辑区域</li>
      <li><code>#uiManager</code>: UI管理器</li>
      <li><code>#previousAltText</code>: 之前的替代文本</li>
      <li><code>#resizeAC</code>: 调整大小事件中止控制器</li>
      <li><code>#svgElement</code>: SVG元素</li>
      <li><code>#rectElement</code>: 矩形元素</li>
      <li><code>#container</code>: 容器元素</li>
      <li><code>#telemetryData</code>: 遥测数据</li>
    </ul>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法列表</h2>
    
    <!-- 构造方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="constructor" class="method-name">constructor({dialog, optionDescription, optionDecorative, textarea, cancelButton, saveButton}, container, overlayManager, eventBus)</h3>
      </div>
      <div class="method-content">
        <p>创建AltTextManager实例。</p>
        <pre><code class="language-javascript">
/**
 * 创建AltTextManager实例
 * 
 * @param {Object} elements - 包含所需DOM元素的对象
 * @param {Element} elements.dialog - 对话框元素
 * @param {Element} elements.optionDescription - 描述选项按钮
 * @param {Element} elements.optionDecorative - 装饰性选项按钮
 * @param {Element} elements.textarea - 文本编辑区域
 * @param {Element} elements.cancelButton - 取消按钮
 * @param {Element} elements.saveButton - 保存按钮
 * @param {Element} container - 容器元素
 * @param {OverlayManager} overlayManager - 覆盖层管理器
 * @param {EventBus} eventBus - 事件总线
 */
constructor({
  dialog,
  optionDescription,
  optionDecorative,
  textarea,
  cancelButton,
  saveButton
}, container, overlayManager, eventBus) {
  // 保存DOM元素引用
  this.#dialog = dialog;
  this.#optionDescription = optionDescription;
  this.#optionDecorative = optionDecorative;
  this.#textarea = textarea;
  this.#cancelButton = cancelButton;
  this.#saveButton = saveButton;
  this.#overlayManager = overlayManager;
  this.#eventBus = eventBus;
  this.#container = container;
  // 绑定UI状态更新方法
  const onUpdateUIState = this.#updateUIState.bind(this);
  
  // 设置对话框关闭事件
  dialog.addEventListener("close", this.#close.bind(this));
  
  // 设置右键菜单处理 - 仅允许在文本区域内使用右键菜单
  dialog.addEventListener("contextmenu", event => {
    if (event.target !== this.#textarea) {
      event.preventDefault();
    }
  });
  
  // 设置取消按钮点击事件
  cancelButton.addEventListener("click", this.#finish.bind(this));
  
  // 设置保存按钮点击事件
  saveButton.addEventListener("click", this.#save.bind(this));
  
  // 设置描述选项变更事件
  optionDescription.addEventListener("change", onUpdateUIState);
  
  // 设置装饰性选项变更事件
  optionDecorative.addEventListener("change", onUpdateUIState);
  
  // 注册对话框到覆盖层管理器
  this.#overlayManager.register(dialog);
}
        </code></pre>
      </div>
    </div>
    
    <!-- createSVGElement方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="createSVGElement" class="method-name">#createSVGElement()</h3>
      </div>
      <div class="method-content">
        <p>创建SVG元素，用于显示高亮图像区域。</p>
        <pre><code class="language-javascript">
/**
 * 创建SVG元素
 * 用于显示高亮图像区域
 * 
 * @private
 */
#createSVGElement() {
  // 如果SVG元素已存在，则不重复创建
  if (this.#svgElement) {
    return;
  }
  
  // 创建SVG工厂实例
  const svgFactory = new DOMSVGFactory();
  
  // 创建SVG元素
  const svg = this.#svgElement = svgFactory.createElement("svg");
  svg.setAttribute("width", "0");
  svg.setAttribute("height", "0");
  
  // 创建定义区域
  const defs = svgFactory.createElement("defs");
  svg.append(defs);
  
  // 创建遮罩元素
  const mask = svgFactory.createElement("mask");
  defs.append(mask);
  mask.setAttribute("id", "alttext-manager-mask");
  mask.setAttribute("maskContentUnits", "objectBoundingBox");
  
  // 创建遮罩的白色背景矩形
  let rect = svgFactory.createElement("rect");
  mask.append(rect);
  rect.setAttribute("fill", "white");
  rect.setAttribute("width", "1");
  rect.setAttribute("height", "1");
  rect.setAttribute("x", "0");
  rect.setAttribute("y", "0");
  
  // 创建高亮矩形
  rect = this.#rectElement = svgFactory.createElement("rect");
  mask.append(rect);
  rect.setAttribute("fill", "black");
  
  // 将SVG元素添加到对话框
  this.#dialog.append(svg);
}
        </code></pre>
      </div>
    </div>
    
    <!-- editAltText方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="editAltText" class="method-name">editAltText(uiManager, editor)</h3>
      </div>
      <div class="method-content">
        <p>编辑图像的替代文本，打开替代文本编辑对话框。</p>
        <pre><code class="language-javascript">
/**
 * 编辑图像的替代文本
 * 打开替代文本编辑对话框
 * 
 * @param {PDFEditorUIManager} uiManager - UI管理器
 * @param {PDFImageEditor} editor - 图像编辑器实例
 * @returns {Promise<void>}
 */
async editAltText(uiManager, editor) {
  // 如果已有正在编辑的编辑器或未提供编辑器，则不执行操作
  if (this.#currentEditor || !editor) {
    return;
  }
  
  // 创建SVG元素（用于显示高亮区域）
  this.#createSVGElement();
  
  // 重置指针使用状态
  this.#hasUsedPointer = false;
  
  // 创建点击事件的中止控制器
  this.#clickAC = new AbortController();
  const clickOpts = {
      signal: this.#clickAC.signal
    },
    onClick = this.#onClick.bind(this);
  
  // 为所有交互元素添加点击监听
  for (const element of [this.#optionDescription, this.#optionDecorative, this.#textarea, this.#saveButton, this.#cancelButton]) {
    element.addEventListener("click", onClick, clickOpts);
  }
  
  // 获取当前图像的替代文本数据
  const {
    altText,
    decorative
  } = editor.altTextData;
  
  // 根据是否为装饰性图像设置选项状态
  if (decorative === true) {
    this.#optionDecorative.checked = true;
    this.#optionDescription.checked = false;
  } else {
    this.#optionDecorative.checked = false;
    this.#optionDescription.checked = true;
  }
  
  // 设置文本区域的值，并保存原始值用于比较
  this.#previousAltText = this.#textarea.value = altText?.trim() || "";
  
  // 更新UI状态
  this.#updateUIState();
  
  // 保存当前编辑器和UI管理器引用
  this.#currentEditor = editor;
  this.#uiManager = uiManager;
  
  // 移除UI管理器的编辑监听器，防止冲突
  this.#uiManager.removeEditListeners();
  
  // 创建调整大小事件的中止控制器
  this.#resizeAC = new AbortController();
  
  // 监听窗口调整大小事件，以更新对话框位置
  this.#eventBus._on("resize", this.#setPosition.bind(this), {
    signal: this.#resizeAC.signal
  });
  
  try {
    // 打开对话框
    await this.#overlayManager.open(this.#dialog);
    
    // 设置对话框位置
    this.#setPosition();
  } catch (ex) {
    // 发生错误时清理资源
    this.#close();
    throw ex;
  }
}
        </code></pre>
      </div>
    </div>
  </div>
    
    <!-- setPosition方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="setPosition" class="method-name">#setPosition()</h3>
      </div>
      <div class="method-content">
        <p>设置对话框位置，根据当前编辑器的位置和屏幕尺寸计算对话框的最佳位置。</p>
        <pre><code class="language-javascript">
/**
 * 设置对话框位置
 * 根据当前编辑器的位置和屏幕尺寸计算对话框的最佳位置
 * 
 * @private
 */
#setPosition() {
  // 如果没有当前编辑器，则不执行操作
  if (!this.#currentEditor) {
    return;
  }
  
  // 获取对话框元素
  const dialog = this.#dialog;
  const {
    style
  } = dialog;
  
  // 获取容器的位置和尺寸
  const {
    x: containerX,
    y: containerY,
    width: containerW,
    height: containerH
  } = this.#container.getBoundingClientRect();
  
  // 获取窗口尺寸
  const {
    innerWidth: windowW,
    innerHeight: windowH
  } = window;
  
  // 获取对话框尺寸
  const {
    width: dialogW,
    height: dialogH
  } = dialog.getBoundingClientRect();
  
  // 获取当前编辑器的位置和尺寸
  const {
    x,
    y,
    width,
    height
  } = this.#currentEditor.getClientDimensions();
  
  // 设置边距
  const MARGIN = 10;
  
  // 获取文本方向
  const isLTR = this.#uiManager.direction === "ltr";
  
  // 计算图像在容器中可见部分的坐标
  const xs = Math.max(x, containerX);
  const xe = Math.min(x + width, containerX + containerW);
  const ys = Math.max(y, containerY);
  const ye = Math.min(y + height, containerY + containerH);
  
  // 设置高亮矩形的位置和尺寸（相对于窗口）
  this.#rectElement.setAttribute("width", `${(xe - xs) / windowW}`);
  this.#rectElement.setAttribute("height", `${(ye - ys) / windowH}`);
  this.#rectElement.setAttribute("x", `${xs / windowW}`);
  this.#rectElement.setAttribute("y", `${ys / windowH}`);
  
  // 尝试计算对话框的最佳位置
  let left = null;
  let top = Math.max(y, 0);
  
  // 确保对话框不会超出底部
  top += Math.min(windowH - (top + dialogH), 0);
  
  // 根据文本方向和可用空间计算水平位置
  if (isLTR) {
    // 从左到右：尝试放在图像右侧，如果不行则放在左侧
    if (x + width + MARGIN + dialogW < windowW) {
      left = x + width + MARGIN;
    } else if (x > dialogW + MARGIN) {
      left = x - dialogW - MARGIN;
    }
  } else {
    // 从右到左：尝试放在图像左侧，如果不行则放在右侧
    if (x > dialogW + MARGIN) {
      left = x - dialogW - MARGIN;
    } else if (x + width + MARGIN + dialogW < windowW) {
      left = x + width + MARGIN;
    }
  }
  
  // 如果无法水平放置，尝试垂直放置
  if (left === null) {
    top = null;
    left = Math.max(x, 0);
    
    // 确保对话框不会超出右侧
    left += Math.min(windowW - (left + dialogW), 0);
    
    // 尝试放在图像上方或下方
    if (y > dialogH + MARGIN) {
      top = y - dialogH - MARGIN;
    } else if (y + height + MARGIN + dialogH < windowH) {
      top = y + height + MARGIN;
    }
  }
  
  // 应用计算出的位置
  if (top !== null) {
    // 使用绝对定位
    dialog.classList.add("positioned");
    
    // 根据文本方向设置水平位置
    if (isLTR) {
      style.left = `${left}px`;
    } else {
      style.right = `${windowW - left - dialogW}px`;
    }
    
    // 设置垂直位置
    style.top = `${top}px`;
  } else {
    // 无法找到合适位置，使用默认居中位置
    dialog.classList.remove("positioned");
    style.left = "";
    style.top = "";
  }
}
        </code></pre>
      </div>
    </div>
    
    <!-- finish方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="finish" class="method-name">#finish()</h3>
      </div>
      <div class="method-content">
        <p>完成编辑，关闭替代文本对话框。</p>
        <pre><code class="language-javascript">
/**
 * 完成编辑
 * 关闭替代文本对话框
 * 
 * @private
 */
#finish() {
  // 如果对话框处于活动状态，关闭它
  this.#overlayManager.closeIfActive(this.#dialog);
}
        </code></pre>
      </div>
    </div>
    
    <!-- close方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="close" class="method-name">#close()</h3>
      </div>
      <div class="method-content">
        <p>关闭对话框并清理资源，当对话框关闭时调用。</p>
        <pre><code class="language-javascript">
/**
 * 关闭对话框并清理资源
 * 当对话框关闭时调用
 * 
 * @private
 */
#close() {
  // 报告遥测数据（如果没有保存，则报告取消操作）
  this.#currentEditor._reportTelemetry(this.#telemetryData || {
    action: "alt_text_cancel",
    alt_text_keyboard: !this.#hasUsedPointer  // 记录是否使用键盘操作
  });
  
  // 清除遥测数据
  this.#telemetryData = null;
  
  // 移除点击事件监听器
  this.#removeOnClickListeners();
  
  // 恢复UI管理器的编辑监听器
  this.#uiManager?.addEditListeners();
  
  // 中止调整大小事件监听
  this.#resizeAC?.abort();
  this.#resizeAC = null;
  
  // 通知编辑器完成替代文本编辑
  this.#currentEditor.altTextFinish();
  
  // 清除引用
  this.#currentEditor = null;
  this.#uiManager = null;
}
                </code></pre>
      </div>
    </div>
    
    <!-- updateUIState方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="updateUIState" class="method-name">#updateUIState()</h3>
      </div>
      <div class="method-content">
        <p>更新UI状态，根据当前选项更新界面元素的可用状态。</p>
        <pre><code class="language-javascript">
/**
 * 更新UI状态
 * 根据当前选项更新界面元素的可用状态
 * 
 * @private
 */
#updateUIState() {
  // 如果选择了"装饰性图像"，则禁用文本输入框
  this.#textarea.disabled = this.#optionDecorative.checked;
}
        </code></pre>
      </div>
    </div>
    
    <!-- save方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="save" class="method-name">#save()</h3>
      </div>
      <div class="method-content">
        <p>保存替代文本，处理用户点击"保存"按钮的操作。</p>
        <pre><code class="language-javascript">
/**
 * 保存替代文本
 * 处理用户点击"保存"按钮的操作
 * 
 * @private
 */
#save() {
  // 获取并清理文本值
  const altText = this.#textarea.value.trim();
  
  // 获取是否为装饰性图像
  const decorative = this.#optionDecorative.checked;
  
  // 设置编辑器的替代文本数据
  this.#currentEditor.altTextData = {
    altText,
    decorative
  };
  
  // 准备遥测数据
  this.#telemetryData = {
    action: "alt_text_save",
    alt_text_description: !!altText,  // 是否包含描述文本
    alt_text_edit: !!this.#previousAltText && this.#previousAltText !== altText,  // 是否编辑了现有文本
    alt_text_decorative: decorative,
    alt_text_keyboard: !this.#hasUsedPointer
  };
  this.#finish();
}
        </code></pre>
      </div>
    </div>
    
    <!-- onClick方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="onClick" class="method-name">#onClick(evt)</h3>
      </div>
      <div class="method-content">
        <p>处理点击事件，记录用户是否使用了指针设备。</p>
        <pre><code class="language-javascript">
/**
 * 处理点击事件
 * 记录用户是否使用了指针设备
 * 
 * @param {MouseEvent} evt - 鼠标事件对象
 * @private
 */
#onClick(evt) {
  // 忽略键盘触发的点击事件（detail为0）
  if (evt.detail === 0) {
    return;
  }
  
  // 标记用户已使用指针设备
  this.#hasUsedPointer = true;
  
  // 移除点击监听器，因为我们已经确定了用户使用了指针
  this.#removeOnClickListeners();
}
        </code></pre>
      </div>
    </div>
    
    <!-- removeOnClickListeners方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="removeOnClickListeners" class="method-name">#removeOnClickListeners()</h3>
      </div>
      <div class="method-content">
        <p>移除点击事件监听器，清理不再需要的事件处理。</p>
        <pre><code class="language-javascript">
/**
 * 移除点击事件监听器
 * 清理不再需要的事件处理
 * 
 * @private
 */
#removeOnClickListeners() {
  // 中止点击事件的监听
  this.#clickAC?.abort();
  this.#clickAC = null;
}
        </code></pre>
      </div>
    </div>
    
    <!-- destroy方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 id="destroy" class="method-name">destroy()</h3>
      </div>
      <div class="method-content">
        <p>销毁替代文本管理器，清理所有资源。</p>
        <pre><code class="language-javascript">
/**
 * 销毁替代文本管理器
 * 清理所有资源
 */
destroy() {
  // 清除UI管理器引用
  this.#uiManager = null;
  
  // 关闭任何打开的对话框
  this.#finish();
  
  // 移除SVG元素
  this.#svgElement?.remove();
  
  // 清除SVG相关引用
  this.#svgElement = this.#rectElement = null;
}
        </code></pre>
      </div>
    </div>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <h3>初始化流程</h3>
    <div class="mermaid">
      graph TD
        A[开始初始化] --> B[构造函数接收DOM元素]
        B --> C[保存DOM元素引用]
        C --> D[绑定事件处理器]
        D --> E[注册对话框到覆盖层管理器]
        E --> F[初始化完成]
    </div>
    
    <h3>编辑替代文本流程</h3>
    <div class="mermaid">
      graph TD
        A[调用editAltText] --> B{检查是否有正在编辑的编辑器}
        B -->|有| C[返回，不执行操作]
        B -->|无| D[创建SVG元素]
        D --> E[重置指针使用状态]
        E --> F[创建点击事件监听器]
        F --> G[获取当前替代文本数据]
        G --> H[设置UI状态]
        H --> I[保存当前编辑器引用]
        I --> J[移除UI管理器编辑监听器]
        J --> K[创建调整大小事件监听器]
        K --> L[打开对话框]
        L --> M[设置对话框位置]
        M --> N[等待用户操作]
        N --> O{用户操作}
        O -->|保存| P[保存替代文本数据]
        O -->|取消| Q[不保存数据]
        P --> R[关闭对话框]
        Q --> R
        R --> S[清理资源]
        S --> T[结束]
    </div>
    
    <h3>保存替代文本流程</h3>
    <div class="mermaid">
      graph TD
        A[点击保存按钮] --> B[获取文本值]
        B --> C[获取装饰性选项状态]
        C --> D[设置编辑器的替代文本数据]
        D --> E[准备遥测数据]
        E --> F[关闭对话框]
        F --> G[清理资源]
    </div>
    
    <h3>对话框位置计算流程</h3>
    <div class="mermaid">
      graph TD
        A[调用setPosition] --> B{检查是否有当前编辑器}
        B -->|无| C[返回，不执行操作]
        B -->|有| D[获取各元素尺寸和位置]
        D --> E[计算图像可见部分坐标]
        E --> F[设置高亮矩形位置]
        F --> G{计算最佳位置}
        G -->|水平成功| H[设置水平位置]
        G -->|水平失败| I[尝试垂直位置]
        I -->|垂直成功| J[设置垂直位置]
        I -->|垂直失败| K[使用默认居中位置]
        H --> L[应用位置样式]
        J --> L
        K --> L
        L --> M[结束]
    </div>
  </div>

  <!-- 返回顶部按钮 -->
  <button class="back-to-top">↑</button>

  <script>
    // 创建目录
    createTableOfContents();
  </script>
  </body>
  </html>  