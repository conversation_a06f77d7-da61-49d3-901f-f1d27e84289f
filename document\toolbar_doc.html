<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Toolbar - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#constants">常量</a>
    <a href="#properties">属性</a>
    <a href="#methods">方法</a>
    <a href="#flowcharts">流程图</a>
    <a href="#examples">示例</a>
    <a href="#notes">注意事项</a>
  </div>

  <h1>Toolbar 模块文档</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>Toolbar 是 PDF.js 库中提供主要用户界面控件的工具栏组件。它包含了 PDF 查看器中常用功能的按钮和控件，如页面导航、缩放控制、文件操作、打印、下载等。Toolbar 负责组织这些控件的布局和样式，处理用户交互事件，并将用户操作转发给相应的功能处理模块。此组件作为 PDF 查看器的主要交互入口，直接影响用户体验。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 常量 -->
  <div id="constants">
    <h2>常量</h2>
    
    <p>Toolbar 使用以下常量：</p>
    
    <ul>
      <li>
        <code>PAGE_NUMBER_LOADING_INDICATOR</code>: 页码加载指示器，通常显示为 "..."
      </li>
      <li>
        <code>SCALE_SELECT_CONTAINER_WIDTH</code>: 缩放选择器容器宽度
      </li>
      <li>
        <code>SCALE_SELECT_WIDTH</code>: 缩放选择器宽度
      </li>
      <li>
        <code>CSS_UNITS</code>: CSS 单位转换常量
      </li>
      <li>
        <code>DEFAULT_SCALE_VALUE</code>: 默认缩放值
      </li>
      <li>
        <code>DEFAULT_SCALE</code>: 默认缩放比例
      </li>
    </ul>
  </div>

  <!-- 属性 -->
  <div id="properties">
    <h2>属性</h2>
    
    <h3>公共属性</h3>
    <ul>
      <li><code>toolbar</code>: 工具栏 DOM 元素</li>
      <li><code>mainContainer</code>: 主容器元素</li>
      <li><code>loadingBar</code>: 加载进度条</li>
      <li><code>pageNumber</code>: 页码输入元素</li>
      <li><code>pagesCount</code>: 总页数显示元素</li>
      <li><code>previousButton</code>: 上一页按钮</li>
      <li><code>nextButton</code>: 下一页按钮</li>
      <li><code>zoomInButton</code>: 放大按钮</li>
      <li><code>zoomOutButton</code>: 缩小按钮</li>
      <li><code>viewFind</code>: 查找按钮</li>
      <li><code>openFile</code>: 打开文件按钮</li>
      <li><code>print</code>: 打印按钮</li>
      <li><code>presentationModeButton</code>: 演示模式按钮</li>
      <li><code>download</code>: 下载按钮</li>
      <li><code>viewBookmark</code>: 书签按钮</li>
      <li><code>editorFreeTextButton</code>: 自由文本编辑器按钮</li>
      <li><code>editorFreeTextParamsToolbar</code>: 自由文本参数工具栏</li>
      <li><code>editorInkButton</code>: 墨迹编辑器按钮</li>
      <li><code>editorInkParamsToolbar</code>: 墨迹参数工具栏</li>
      <li><code>editorStampButton</code>: 图章编辑器按钮</li>
      <li><code>editorHighlightButton</code>: 高亮编辑器按钮</li>
      <li><code>editorHighlightParamsToolbar</code>: 高亮参数工具栏</li>
      <li><code>editorUnderlineButton</code>: 下划线编辑器按钮</li>
      <li><code>editorUnderlineParamsToolbar</code>: 下划线参数工具栏</li>
      <li><code>editorSquigglyButton</code>: 波浪线编辑器按钮</li>
      <li><code>editorSquigglyParamsToolbar</code>: 波浪线参数工具栏</li>
      <li><code>editorStrikeoutButton</code>: 删除线编辑器按钮</li>
      <li><code>editorStrikeoutParamsToolbar</code>: 删除线参数工具栏</li>
      <li><code>downloadOrSaveButton</code>: 下载或保存按钮</li>
    </ul>

    <h3>私有属性</h3>
    <ul>
      <li><code>#eventBus</code>: 事件总线</li>
      <li><code>#l10n</code>: 本地化对象</li>
      <li><code>#pdfScriptingManager</code>: PDF 脚本管理器</li>
      <li><code>#mainContainer</code>: 主容器元素</li>
      <li><code>#container</code>: 容器元素</li>
      <li><code>#items</code>: 工具栏项目集合</li>
      <li><code>#wasLocalized</code>: 是否已本地化</li>
      <li><code>#buttons</code>: 按钮集合</li>
      <li><code>#dropdown</code>: 下拉菜单集合</li>
      <li><code>#buttons.viewerPreferences</code>: 查看器首选项按钮</li>
      <li><code>#buttons.sidebarToggle</code>: 侧边栏切换按钮</li>
      <li><code>#buttons.secondaryToolbarToggle</code>: 次要工具栏切换按钮</li>
      <li><code>#buttons.pageRotateCw</code>: 顺时针旋转页面按钮</li>
      <li><code>#buttons.pageRotateCcw</code>: 逆时针旋转页面按钮</li>
      <li><code>#buttons.cursorSelectTool</code>: 光标选择工具按钮</li>
      <li><code>#buttons.cursorHandTool</code>: 光标手形工具按钮</li>
      <li><code>#buttons.scrollPage</code>: 滚动页面按钮</li>
      <li><code>#buttons.scrollVertical</code>: 垂直滚动按钮</li>
      <li><code>#buttons.scrollHorizontal</code>: 水平滚动按钮</li>
      <li><code>#buttons.scrollWrapped</code>: 环绕滚动按钮</li>
      <li><code>#buttons.spreadNone</code>: 无页面展开按钮</li>
      <li><code>#buttons.spreadOdd</code>: 奇数页展开按钮</li>
      <li><code>#buttons.spreadEven</code>: 偶数页展开按钮</li>
      <li><code>#buttons.documentProperties</code>: 文档属性按钮</li>
      <li><code>#scaleSelect</code>: 缩放选择器</li>
      <li><code>#numPages</code>: 总页数</li>
      <li><code>#pageScaleValue</code>: 页面缩放值</li>
      <li><code>#pageScale</code>: 页面缩放比例</li>
    </ul>
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法</h2>
    
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">构造函数</h3>
      </div>
      <div class="method-content">
        <p>创建一个新的 Toolbar 实例。</p>
        <pre><code class="language-javascript">
constructor({
  container,
  eventBus,
  l10n = null,
  pdfScriptingManager = null,
})
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>container</code>: 容器元素，工具栏将在其中创建</li>
          <li><code>eventBus</code>: 事件总线，用于与其他组件通信</li>
          <li><code>l10n</code>: 本地化对象，用于翻译工具栏文本 (可选，默认为 null)</li>
          <li><code>pdfScriptingManager</code>: PDF 脚本管理器，用于处理文档中的脚本 (可选，默认为 null)</li>
        </ul>
      </div>
    </div>

    <h3>核心方法</h3>
    <ul>
      <li><code>setPageNumber(pageNumber, pageLabel)</code>: 设置当前页码和页面标签</li>
      <li><code>setPagesCount(pagesCount, hasPageLabels)</code>: 设置文档总页数和是否有页面标签</li>
      <li><code>setPageScale(pageScaleValue, pageScale)</code>: 设置页面缩放值和缩放比例</li>
      <li><code>reset()</code>: 重置工具栏状态</li>
      <li><code>updateLoadingIndicatorState(loading = false)</code>: 更新加载指示器状态</li>
    </ul>
    
    <h3>事件处理方法</h3>
    <ul>
      <li><code>#bindEvents()</code>: 绑定事件监听器</li>
      <li><code>#unbindEvents()</code>: 解除事件监听器</li>
      <li><code>#bindEditorToolsListener(eventBus)</code>: 绑定编辑器工具监听器</li>
      <li><code>#bindPresentationModeListener(eventBus)</code>: 绑定演示模式监听器</li>
      <li><code>#handleEvent(evt)</code>: 处理事件</li>
    </ul>
    
    <h3>UI 相关方法</h3>
    <ul>
      <li><code>#createAppearanceButton(id, items)</code>: 创建外观按钮</li>
      <li><code>#createVisibilityButton(id, items)</code>: 创建可见性按钮</li>
      <li><code>#updateUIState(resetNumPages = false)</code>: 更新 UI 状态</li>
      <li><code>#updateLoadingIndicator(loading = false)</code>: 更新加载指示器</li>
      <li><code>#disableMoreButtons(disabled = false)</code>: 禁用更多按钮</li>
      <li><code>#disableEditorButtons(disabled = true)</code>: 禁用编辑器按钮</li>
      <li><code>#enableEditorUIManager({ editingMode, hasEverythingSelected, isBlankPage, isFormsEnabled, })</code>: 启用编辑器 UI 管理器</li>
      <li><code>#updateEditorParamsToolbar(evt)</code>: 更新编辑器参数工具栏</li>
      <li><code>#updateEditorToolbarDisabled()</code>: 更新编辑器工具栏禁用状态</li>
    </ul>
    
    <h3>内部实用方法</h3>
    <ul>
      <li><code>#disableElementAfterKeydown(button)</code>: 在按键后禁用元素</li>
      <li><code>#isElementVisible(element)</code>: 检查元素是否可见</li>
      <li><code>#setMaxHeight(element)</code>: 设置元素最大高度</li>
      <li><code>#selectScaleOption(value = "auto")</code>: 选择缩放选项</li>
      <li><code>#setScaleUpdateUI(newScale, newValue)</code>: 设置缩放更新 UI</li>
      <li><code>#_adjustScaleWidth()</code>: 调整缩放宽度</li>
      <li><code>#_localize()</code>: 本地化工具栏</li>
    </ul>
    
    <h3>页面操作方法</h3>
    <ul>
      <li><code>#goToPage(evt)</code>: 转到指定页面</li>
      <li><code>#pageRotateCw()</code>: 顺时针旋转页面</li>
      <li><code>#pageRotateCcw()</code>: 逆时针旋转页面</li>
    </ul>
    
    <h3>视图控制方法</h3>
    <ul>
      <li><code>#switchScrollMode(evt)</code>: 切换滚动模式</li>
      <li><code>#switchSpreadMode(evt)</code>: 切换页面展开模式</li>
      <li><code>#zoomIn()</code>: 放大</li>
      <li><code>#zoomOut()</code>: 缩小</li>
    </ul>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <h3>工具栏初始化流程</h3>
    <p>1. 创建工具栏实例<br>
    2. 初始化 DOM 元素<br>
    3. 绑定事件监听器<br>
    4. 本地化工具栏<br>
    5. 调整缩放选择器宽度</p>
    
    <div class="mermaid">
      graph TD
          A[创建 Toolbar 实例] --> B[构造函数执行]
          B --> C[初始化属性]
          C --> D[获取容器内的 DOM 元素引用]
          D --> E[初始化按钮和下拉菜单]
          E --> F[绑定事件监听器]
          F --> G[为页码输入添加验证]
          G --> H[为缩放选择器添加事件]
          H --> I[本地化工具栏文本]
          I --> J[调整缩放选择器宽度]
          J --> K[初始化完成]
    </div>
    
    <h3>事件处理流程</h3>
    <p>1. 用户与工具栏交互<br>
    2. 触发事件<br>
    3. 工具栏处理事件<br>
    4. 转发命令到相应模块<br>
    5. 更新 UI 状态</p>
    
    <div class="mermaid">
      graph TD
          A[用户与工具栏交互] --> B{交互类型}
          B -->|点击上一页/下一页按钮| C[触发页面导航事件]
          B -->|输入页码| D[处理页码输入]
          B -->|选择缩放选项| E[设置页面缩放]
          B -->|点击放大/缩小按钮| F[调整缩放级别]
          B -->|点击旋转按钮| G[旋转页面]
          B -->|点击滚动模式按钮| H[切换滚动模式]
          B -->|点击展开模式按钮| I[切换展开模式]
          B -->|点击工具按钮| J[激活相应工具]
          C --> K[通过事件总线分发事件]
          D --> L[验证输入并跳转到页面]
          E --> M[更新缩放 UI 并应用缩放]
          F --> M
          G --> N[更新页面旋转]
          H --> O[更新滚动模式]
          I --> P[更新展开模式]
          J --> Q[调用相应功能模块]
          K --> R[更新工具栏状态]
          L --> R
          M --> R
          N --> R
          O --> R
          P --> R
          Q --> R
    </div>
    
    <h3>工具栏状态更新流程</h3>
    <p>1. 接收更新事件<br>
    2. 更新页码显示<br>
    3. 更新缩放显示<br>
    4. 更新按钮状态<br>
    5. 更新编辑器工具栏</p>
    
    <div class="mermaid">
      graph TD
          A[外部事件触发状态更新] --> B{事件类型}
          B -->|pagechanging| C[更新当前页码]
          B -->|pagesloaded| D[设置总页数]
          B -->|scalechanging| E[更新缩放值]
          B -->|rotationchanging| F[更新旋转状态]
          B -->|scrollmodechanged| G[更新滚动模式]
          B -->|spreadmodechanged| H[更新展开模式]
          B -->|documentloaded| I[重置工具栏状态]
          B -->|updatefromsandbox| J[从沙盒更新状态]
          B -->|enablecursortool| K[更新光标工具状态]
          B -->|annotationeditormodechanged| L[更新注释编辑器模式]
          C --> M[调用 setPageNumber]
          D --> N[调用 setPagesCount]
          E --> O[调用 setPageScale]
          F --> P[更新旋转按钮状态]
          G --> Q[更新滚动模式按钮状态]
          H --> R[更新展开模式按钮状态]
          I --> S[调用 reset 方法]
          J --> T[根据沙盒状态更新 UI]
          K --> U[更新光标工具按钮状态]
          L --> V[更新编辑器工具栏状态]
          M --> W[更新 UI 状态]
          N --> W
          O --> W
          P --> W
          Q --> W
          R --> W
          S --> W
          T --> W
          U --> W
          V --> W
    </div>
  </div>

  <!-- 示例 -->
  <div id="examples">
    <h2>使用示例</h2>
    
    <h3>基本用法</h3>
    <pre><code class="language-javascript">
// 创建事件总线
const eventBus = new EventBus();

// 创建本地化对象
const l10n = new GenericL10n({ defaultLang: 'zh-CN' });

// 创建 Toolbar 实例
const toolbar = new Toolbar({
  container: document.getElementById('toolbarContainer'),
  eventBus,
  l10n
});

// 监听事件并与其他组件交互
eventBus.on('pagechanging', function(evt) {
  console.log('页面正在改变:', evt.pageNumber);
});

// 设置初始页码和总页数
toolbar.setPageNumber(1, '1');
toolbar.setPagesCount(10, false);

// 设置缩放
toolbar.setPageScale('auto', 1.0);

// 更新加载指示器状态
toolbar.updateLoadingIndicatorState(true); // 显示加载中
// ... 加载完成后
toolbar.updateLoadingIndicatorState(false); // 隐藏加载指示器
    </code></pre>
    
    <h3>自定义工具栏</h3>
    <pre><code class="language-javascript">
// 扩展工具栏类，添加自定义功能
class CustomToolbar extends Toolbar {
  constructor(options) {
    super(options);
    
    // 添加自定义属性
    this.customFeatureEnabled = false;
    
    // 在初始化完成后添加自定义按钮
    this._addCustomButtons();
  }
  
  // 添加自定义按钮
  _addCustomButtons() {
    // 创建自定义功能按钮
    const customButton = document.createElement('button');
    customButton.className = 'toolbarButton customFeature';
    customButton.title = '自定义功能';
    customButton.textContent = '自定义';
    
    // 添加点击事件
    customButton.addEventListener('click', () => {
      this._toggleCustomFeature();
    });
    
    // 将按钮添加到工具栏的适当位置
    const firstRowRight = this.toolbar.querySelector('.toolbarViewerRight');
    firstRowRight.insertBefore(customButton, firstRowRight.firstChild);
    
    // 保存按钮引用
    this.customFeatureButton = customButton;
    
    // 创建自定义分隔线
    const separator = document.createElement('div');
    separator.className = 'verticalToolbarSeparator';
    firstRowRight.insertBefore(separator, customButton.nextSibling);
  }
  
  // 切换自定义功能
  _toggleCustomFeature() {
    this.customFeatureEnabled = !this.customFeatureEnabled;
    
    // 根据状态更新按钮外观
    if (this.customFeatureEnabled) {
      this.customFeatureButton.classList.add('toggled');
    } else {
      this.customFeatureButton.classList.remove('toggled');
    }
    
    // 通过事件总线通知其他组件
    this.#eventBus.dispatch('customfeaturechanged', {
      source: this,
      enabled: this.customFeatureEnabled
    });
  }
  
  // 重写重置方法以包含自定义功能
  reset() {
    super.reset();
    
    // 重置自定义功能状态
    this.customFeatureEnabled = false;
    if (this.customFeatureButton) {
      this.customFeatureButton.classList.remove('toggled');
    }
  }
  
  // 公开自定义功能状态
  isCustomFeatureEnabled() {
    return this.customFeatureEnabled;
  }
}

// 使用自定义工具栏
const customToolbar = new CustomToolbar({
  container: document.getElementById('toolbarContainer'),
  eventBus,
  l10n
});

// 监听自定义事件
eventBus.on('customfeaturechanged', function(evt) {
  console.log('自定义功能状态变更:', evt.enabled);
  // 在这里实现自定义功能的实际逻辑
});
    </code></pre>
    
    <h3>响应式工具栏</h3>
    <pre><code class="language-javascript">
// 创建一个响应式工具栏管理器
class ResponsiveToolbarManager {
  constructor(toolbar, breakpoints = {
    small: 600,
    medium: 900,
    large: 1200
  }) {
    this.toolbar = toolbar;
    this.breakpoints = breakpoints;
    this.currentSize = null;
    
    // 初始化
    this._initResponsiveFeatures();
    
    // 监听窗口大小变化
    window.addEventListener('resize', this._onResize.bind(this));
    
    // 初始调用一次以设置初始状态
    this._onResize();
  }
  
  // 初始化响应式功能
  _initResponsiveFeatures() {
    // 获取工具栏中的所有按钮
    const allButtons = Array.from(this.toolbar.toolbar.querySelectorAll('.toolbarButton'));
    
    // 按优先级分类按钮
    this.highPriorityButtons = allButtons.filter(button => 
      button.classList.contains('pageUp') || 
      button.classList.contains('pageDown') ||
      button.classList.contains('zoomIn') ||
      button.classList.contains('zoomOut') ||
      button.classList.contains('presentationMode') ||
      button.classList.contains('openFile') ||
      button.classList.contains('print')
    );
    
    this.mediumPriorityButtons = allButtons.filter(button => 
      button.classList.contains('viewFind') ||
      button.classList.contains('viewBookmark') ||
      button.classList.contains('sidebarToggle')
    );
    
    this.lowPriorityButtons = allButtons.filter(button => 
      !this.highPriorityButtons.includes(button) &&
      !this.mediumPriorityButtons.includes(button) &&
      !button.classList.contains('more') // 排除更多按钮
    );
    
    // 创建更多按钮和下拉菜单
    this._createMoreMenu();
  }
  
  // 创建"更多"按钮和下拉菜单
  _createMoreMenu() {
    // 检查是否已存在更多按钮
    let moreButton = this.toolbar.toolbar.querySelector('.toolbarButton.more');
    
    if (!moreButton) {
      // 创建更多按钮
      moreButton = document.createElement('button');
      moreButton.className = 'toolbarButton more';
      moreButton.textContent = '更多';
      moreButton.title = '更多选项';
      
      // 添加到工具栏右侧
      const rightContainer = this.toolbar.toolbar.querySelector('.toolbarViewerRight');
      rightContainer.appendChild(moreButton);
      
      // 创建下拉菜单容器
      this.moreMenu = document.createElement('div');
      this.moreMenu.className = 'moreMenu';
      this.moreMenu.style.display = 'none';
      this.moreMenu.style.position = 'absolute';
      this.moreMenu.style.right = '0';
      this.moreMenu.style.top = '100%';
      this.moreMenu.style.background = 'white';
      this.moreMenu.style.border = '1px solid #ccc';
      this.moreMenu.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
      this.moreMenu.style.zIndex = '100';
      this.moreMenu.style.minWidth = '200px';
      
      // 添加到工具栏
      rightContainer.style.position = 'relative';
      rightContainer.appendChild(this.moreMenu);
      
      // 切换菜单显示
      moreButton.addEventListener('click', () => {
        if (this.moreMenu.style.display === 'none') {
          this.moreMenu.style.display = 'block';
        } else {
          this.moreMenu.style.display = 'none';
        }
      });
      
      // 点击外部关闭菜单
      document.addEventListener('click', (event) => {
        if (!moreButton.contains(event.target) && !this.moreMenu.contains(event.target)) {
          this.moreMenu.style.display = 'none';
        }
      });
      
      this.moreButton = moreButton;
    }
  }
  
  // 创建菜单项
  _createMenuItem(button) {
    const menuItem = document.createElement('div');
    menuItem.className = 'moreMenuItem';
    menuItem.textContent = button.title || button.textContent;
    menuItem.style.padding = '10px 15px';
    menuItem.style.cursor = 'pointer';
    menuItem.style.borderBottom = '1px solid #eee';
    
    // 鼠标悬停效果
    menuItem.addEventListener('mouseenter', () => {
      menuItem.style.backgroundColor = '#f5f5f5';
    });
    
    menuItem.addEventListener('mouseleave', () => {
      menuItem.style.backgroundColor = '';
    });
    
    // 点击时触发原按钮的点击事件
    menuItem.addEventListener('click', () => {
      button.click();
      this.moreMenu.style.display = 'none';
    });
    
    return menuItem;
  }
  
  // 窗口大小变化处理
  _onResize() {
    const width = window.innerWidth;
    let newSize;
    
    if (width < this.breakpoints.small) {
      newSize = 'small';
    } else if (width < this.breakpoints.medium) {
      newSize = 'medium';
    } else if (width < this.breakpoints.large) {
      newSize = 'large';
    } else {
      newSize = 'xlarge';
    }
    
    // 如果尺寸没变，不做处理
    if (newSize === this.currentSize) {
      return;
    }
    
    this.currentSize = newSize;
    this._updateToolbarLayout();
  }
  
  // 更新工具栏布局
  _updateToolbarLayout() {
    // 清空更多菜单
    this.moreMenu.innerHTML = '';
    
    // 重置所有按钮可见性
    [...this.highPriorityButtons, ...this.mediumPriorityButtons, ...this.lowPriorityButtons].forEach(button => {
      button.style.display = '';
    });
    
    // 根据当前尺寸隐藏不同优先级的按钮
    if (this.currentSize === 'small') {
      // 在小屏幕上，只显示高优先级按钮，其余放入更多菜单
      [...this.mediumPriorityButtons, ...this.lowPriorityButtons].forEach(button => {
        button.style.display = 'none';
        this.moreMenu.appendChild(this._createMenuItem(button));
      });
      this.moreButton.style.display = '';
    } else if (this.currentSize === 'medium') {
      // 在中等屏幕上，显示高优先级和中优先级按钮
      this.lowPriorityButtons.forEach(button => {
        button.style.display = 'none';
        this.moreMenu.appendChild(this._createMenuItem(button));
      });
      this.moreButton.style.display = '';
    } else if (this.currentSize === 'large') {
      // 在大屏幕上，显示除了低优先级按钮外的所有按钮
      this.lowPriorityButtons.slice(2).forEach(button => {
        button.style.display = 'none';
        this.moreMenu.appendChild(this._createMenuItem(button));
      });
      this.moreButton.style.display = this.moreMenu.children.length > 0 ? '' : 'none';
    } else {
      // 在超大屏幕上，显示所有按钮
      this.moreButton.style.display = 'none';
    }
  }
}

// 在工具栏初始化后创建响应式管理器
const responsiveManager = new ResponsiveToolbarManager(toolbar);
    </code></pre>
  </div>

  <!-- 注意事项 -->
  <div id="notes">
    <h2>注意事项</h2>
    
    <ul>
      <li>工具栏是用户与 PDF 查看器交互的主要界面，应确保其响应迅速且易于使用。</li>
      <li>为确保良好的用户体验，工具栏按钮应有清晰的图标和工具提示，帮助用户理解其功能。</li>
      <li>在移动设备上，工具栏应采用响应式设计，可以根据屏幕大小调整按钮的显示方式。</li>
      <li>对于经常使用的功能（如页面导航、缩放），应确保这些按钮始终可见并易于访问。</li>
      <li>工具栏应支持键盘导航和辅助功能，确保所有用户都能够轻松操作。</li>
      <li>考虑添加自定义工具栏配置选项，允许用户根据自己的需求调整工具栏布局和可用按钮。</li>
      <li>在处理页码输入时，应进行有效性验证，确保用户输入的页码在有效范围内。</li>
      <li>在加载大型文档时，工具栏应显示适当的加载指示器，提供视觉反馈。</li>
      <li>工具栏应支持国际化和本地化，以适应不同语言环境的用户。</li>
      <li>在实现自定义工具栏功能时，应保持与 PDF.js 其他组件的兼容性，确保事件和参数传递正确。</li>
    </ul>
  </div>

  <script>
    // 在页面加载完成后初始化 Mermaid
    document.addEventListener('DOMContentLoaded', function() {
      mermaid.initialize({ startOnLoad: true });
      
      // 生成目录
      generateTOC();
    });
  </script>
</body>
</html>
