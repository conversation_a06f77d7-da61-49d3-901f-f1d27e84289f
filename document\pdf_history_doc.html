<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PDFHistory - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#constants">常量</a>
    <a href="#properties">属性</a>
    <a href="#methods">方法</a>
    <a href="#flowcharts">流程图</a>
    <a href="#examples">示例</a>
    <a href="#notes">注意事项</a>
  </div>

  <h1>PDFHistory 模块文档</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>PDFHistory 是 PDF.js 库中负责管理浏览历史和状态的组件。它通过与浏览器的历史 API 集成，提供了在 PDF 文档内导航时的历史记录功能。该组件管理页面、缩放级别和滚动位置等状态信息，使用户可以使用浏览器的前进/后退按钮在 PDF 视图状态之间导航。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 常量 -->
  <div id="constants">
    <h2>常量</h2>
    
    <p>PDFHistory 使用以下常量：</p>
    
    <ul>
      <li>
        <code>HASH_CHANGE_TIMEOUT</code>: 哈希变化超时时间（毫秒）
      </li>
      <li>
        <code>POSITION_UPDATED_THRESHOLD</code>: 位置更新阈值（毫秒）
      </li>
      <li>
        <code>UPDATE_VIEWAREA_TIMEOUT</code>: 视图区域更新超时时间（毫秒）
      </li>
      <li>
        <code>HistoryUpdateType</code>: 历史更新类型
        <ul>
          <li><code>HASH</code>: 哈希更新</li>
          <li><code>BROWSER</code>: 浏览器更新</li>
          <li><code>PUSHSTATE</code>: 推送状态更新</li>
        </ul>
      </li>
    </ul>
  </div>

  <!-- 属性 -->
  <div id="properties">
    <h2>属性</h2>
    
    <h3>公共属性</h3>
    <ul>
      <li><code>initialized</code>: 是否已初始化</li>
      <li><code>initialBookmark</code>: 初始书签</li>
      <li><code>initialRotation</code>: 初始旋转角度</li>
    </ul>

    <h3>私有属性</h3>
    <ul>
      <li><code>#isInitializedPromiseResolved</code>: 初始化 Promise 是否已解决</li>
      <li><code>#initializedPromise</code>: 初始化 Promise</li>
      <li><code>#fingerprint</code>: PDF 文档指纹</li>
      <li><code>#pdfDocument</code>: PDF 文档实例</li>
      <li><code>#linkService</code>: 链接服务实例</li>
      <li><code>#eventBus</code>: 事件总线</li>
      <li><code>#updateUrl</code>: 是否更新 URL</li>
      <li><code>#initialized</code>: 内部初始化标志</li>
      <li><code>#ariaLabel</code>: ARIA 标签</li>
      <li><code>#baseUrl</code>: 基础 URL</li>
      <li><code>#currentUrl</code>: 当前 URL</li>
      <li><code>#file</code>: 文件路径</li>
      <li><code>#currentUid</code>: 当前唯一标识符</li>
      <li><code>#uid</code>: 唯一标识符计数器</li>
      <li><code>#lastHash</code>: 最后的哈希值</li>
      <li><code>#pushStateInProgress</code>: 推送状态是否进行中</li>
      <li><code>#popStateInProgress</code>: 弹出状态是否进行中</li>
      <li><code>#recentlyUpdated</code>: 最近是否更新</li>
      <li><code>#mouseScrollTimeStamp</code>: 鼠标滚动时间戳</li>
      <li><code>#mouseScrollDelta</code>: 鼠标滚动增量</li>
      <li><code>#touchSwipeState</code>: 触摸滑动状态</li>
      <li><code>#stateObj</code>: 状态对象</li>
      <li><code>#updateViewareaTimeout</code>: 更新视图区域超时计时器</li>
      <li><code>#previousHash</code>: 前一个哈希值</li>
      <li><code>#numPositionUpdates</code>: 位置更新次数</li>
      <li><code>#hashChangeTimeout</code>: 哈希变化超时计时器</li>
      <li><code>#initialBookmark</code>: 内部初始书签</li>
      <li><code>#initialRotation</code>: 内部初始旋转角度</li>
      <li><code>#onPopState</code>: 弹出状态事件处理函数</li>
    </ul>
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法</h2>
    
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">构造函数</h3>
      </div>
      <div class="method-content">
        <p>创建一个新的 PDFHistory 实例。</p>
        <pre><code class="language-javascript">
constructor({
  linkService,
  eventBus,
  updateUrl = false,
  browserInitialized = false,
})
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>linkService</code>: 链接服务实例</li>
          <li><code>eventBus</code>: 事件总线</li>
          <li><code>updateUrl</code>: 是否更新 URL (可选，默认为 false)</li>
          <li><code>browserInitialized</code>: 浏览器是否已初始化 (可选，默认为 false)</li>
        </ul>
      </div>
    </div>

    <h3>核心方法</h3>
    <ul>
      <li><code>initialize({ fingerprint, resetHistory = false, updateUrl = false })</code>: 初始化历史组件</li>
      <li><code>reset()</code>: 重置历史状态</li>
      <li><code>push({ namedDest = null, explicitDest, pageNumber, scale = null, })</code>: 推送新状态到历史</li>
      <li><code>pushPage(pageNumber)</code>: 推送页面到历史</li>
      <li><code>pushCurrentPosition()</code>: 推送当前位置到历史</li>
      <li><code>back()</code>: 返回上一个历史状态</li>
      <li><code>forward()</code>: 前进到下一个历史状态</li>
      <li><code>updateIfCurrent({ fingerprint, resetHistory = false })</code>: 更新当前历史状态</li>
    </ul>
    
    <h3>私有方法</h3>
    <ul>
      <li><code>#updateViewarea({ location })</code>: 更新视图区域</li>
      <li><code>#updateHash(hash)</code>: 更新哈希值</li>
      <li><code>#getBaseUrl()</code>: 获取基础 URL</li>
      <li><code>#getHash()</code>: 获取当前哈希值</li>
      <li><code>#parseCurrentHash(checkNameddest = false)</code>: 解析当前哈希值</li>
      <li><code>#updateHistoryWithCurrentHash()</code>: 使用当前哈希更新历史</li>
      <li><code>#isValidState(state)</code>: 检查状态是否有效</li>
      <li><code>#isStateObjectDefined()</code>: 检查状态对象是否已定义</li>
      <li><code>#parseDestHash(hash)</code>: 解析目标哈希值</li>
      <li><code>#updatePreviousHash()</code>: 更新前一个哈希值</li>
      <li><code>#isDestHashesEqual(destHash1, destHash2)</code>: 检查两个目标哈希是否相等</li>
      <li><code>#stateToParams(state)</code>: 将状态转换为参数</li>
      <li><code>#normalizeHash(hash)</code>: 规范化哈希值</li>
      <li><code>#getFileNameFromUrl(url)</code>: 从 URL 获取文件名</li>
      <li><code>#pushOrReplaceState(stateObj, updateType)</code>: 推送或替换状态</li>
      <li><code>#handleRenderingDone()</code>: 处理渲染完成事件</li>
      <li><code>#handleHash(evt)</code>: 处理哈希变化事件</li>
      <li><code>#handlePushState(evt)</code>: 处理推送状态事件</li>
      <li><code>#handlePopState(evt)</code>: 处理弹出状态事件</li>
      <li><code>#handleResetHistory(evt)</code>: 处理重置历史事件</li>
    </ul>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <h3>历史初始化流程</h3>
    <p>1. 创建 PDFHistory 实例<br>
    2. 设置 PDF 文档指纹<br>
    3. 获取基础 URL<br>
    4. 解析当前哈希值<br>
    5. 绑定事件监听器<br>
    6. 初始化状态</p>
    
    <div class="mermaid">
      graph TD
          A[创建 PDFHistory 实例] --> B[调用 initialize 方法]
          B --> C[设置 PDF 文档指纹]
          C --> D[获取基础 URL]
          D --> E[解析当前哈希值]
          E --> F[设置初始书签和旋转]
          F --> G[绑定事件监听器]
          G --> H[初始化状态]
          H --> I[触发初始化完成]
    </div>
    
    <h3>状态推送流程</h3>
    <p>1. 用户导航到新页面或位置<br>
    2. 调用 <code>push()</code> 方法<br>
    3. 创建状态对象<br>
    4. 生成新哈希值<br>
    5. 推送状态到浏览器历史<br>
    6. 更新当前状态</p>
    
    <div class="mermaid">
      graph TD
          A[用户导航到新页面/位置] --> B[调用 push 方法]
          B --> C[创建状态对象]
          C --> D[生成新哈希值]
          D --> E{是否更新 URL?}
          E -->|是| F[推送状态到浏览器历史]
          E -->|否| G[更新内部状态]
          F --> G
          G --> H[更新当前哈希值]
    </div>
    
    <h3>哈希变化处理流程</h3>
    <p>1. 浏览器哈希变化<br>
    2. 触发 <code>hashchange</code> 事件<br>
    3. 调用 <code>#handleHash()</code> 方法<br>
    4. 解析新哈希值<br>
    5. 验证哈希是否有效<br>
    6. 导航到新位置</p>
    
    <div class="mermaid">
      graph TD
          A[浏览器哈希变化] --> B[触发 hashchange 事件]
          B --> C[调用 #handleHash 方法]
          C --> D{是否推送状态或弹出状态进行中?}
          D -->|是| E[退出处理]
          D -->|否| F[解析新哈希值]
          F --> G{是否包含有效目标?}
          G -->|是| H[链接服务导航到指定位置]
          G -->|否| I[保持当前位置]
    </div>
  </div>

  <!-- 示例 -->
  <div id="examples">
    <h2>使用示例</h2>
    
    <h3>基本用法</h3>
    <pre><code class="language-javascript">
// 创建 PDFHistory 实例
const pdfHistory = new PDFHistory({
  linkService: pdfLinkService,
  eventBus: eventBus,
  updateUrl: true // 启用 URL 更新
});

// 初始化历史组件
pdfDocument.getFingerprint().then(function(fingerprint) {
  pdfHistory.initialize({
    fingerprint,
    resetHistory: false,
    updateUrl: true
  });
});

// 监听初始化完成
pdfHistory.initializedPromise.then(function() {
  console.log('PDF历史组件已初始化');
  
  // 如果有初始书签，导航到该位置
  if (pdfHistory.initialBookmark) {
    pdfLinkService.setHash(pdfHistory.initialBookmark);
  }
  
  // 如果有初始旋转，设置页面旋转
  if (pdfHistory.initialRotation !== null) {
    pdfViewer.pagesRotation = pdfHistory.initialRotation;
  }
});
    </code></pre>
    
    <h3>页面导航集成</h3>
    <pre><code class="language-javascript">
// 页面导航按钮事件处理
document.getElementById('previous').addEventListener('click', function() {
  pdfViewer.currentPageNumber--;
  // 页面变化会自动通过 eventBus 触发 updateviewarea 事件，从而更新历史
});

document.getElementById('next').addEventListener('click', function() {
  pdfViewer.currentPageNumber++;
  // 页面变化会自动通过 eventBus 触发 updateviewarea 事件，从而更新历史
});

// 页面输入导航
document.getElementById('pageNumber').addEventListener('change', function() {
  const pageNumber = parseInt(this.value);
  if (pageNumber > 0 && pageNumber <= pdfViewer.pagesCount) {
    // 手动推送页面到历史
    pdfHistory.pushPage(pageNumber);
    pdfViewer.currentPageNumber = pageNumber;
  }
});

// 浏览器前进/后退按钮集成
window.addEventListener('popstate', function(evt) {
  // PDFHistory 已内部处理 popstate 事件
  console.log('浏览器历史导航已触发');
});
    </code></pre>
    
    <h3>高级状态管理</h3>
    <pre><code class="language-javascript">
// 自定义导航点
function navigateToPoint(pageNumber, position) {
  // 创建显式目标
  const dest = [
    { num: pageNumber, gen: 0 }, // 页面引用
    { name: 'XYZ' },            // 定位类型
    position.left,              // 左边距
    position.top,               // 顶边距
    pdfViewer.currentScale      // 缩放比例
  ];
  
  // 推送到历史
  pdfHistory.push({
    explicitDest: dest,
    pageNumber: pageNumber
  });
  
  // 导航到目标
  pdfLinkService.navigateTo(dest);
}

// 保存阅读位置
function saveCurrentPosition() {
  pdfHistory.pushCurrentPosition();
  
  // 可以通过存储 pdfHistory.lastHash 来记住用户的位置
  localStorage.setItem('lastPdfPosition', pdfHistory.lastHash);
  console.log('保存当前阅读位置:', pdfHistory.lastHash);
}

// 加载上次阅读位置
function loadLastPosition() {
  const lastPosition = localStorage.getItem('lastPdfPosition');
  if (lastPosition) {
    console.log('恢复上次阅读位置:', lastPosition);
    pdfLinkService.setHash(lastPosition);
  }
}

// 导航到命名目标
function navigateToNamedDestination(name) {
  pdfDocument.getDestination(name).then(function(dest) {
    if (dest) {
      pdfHistory.push({
        namedDest: name,
        explicitDest: dest
      });
      
      pdfLinkService.navigateTo(dest);
    }
  });
}
    </code></pre>
  </div>

  <!-- 注意事项 -->
  <div id="notes">
    <h2>注意事项</h2>
    
    <ul>
      <li>PDFHistory 依赖于浏览器的历史 API（history.pushState 和 popstate 事件），在不支持这些 API 的环境中可能需要降级处理。</li>
      <li>在使用 iframe 或嵌入式环境中，需要特别注意历史状态的管理，以避免与父页面的历史冲突。</li>
      <li>如果在同一页面上有多个 PDF 查看器实例，每个实例应使用唯一的 fingerprint 来区分历史状态。</li>
      <li>启用 updateUrl 选项时，URL 哈希部分将包含 PDF 导航状态。这可能会影响页面的分析和书签功能，需要相应处理。</li>
      <li>从其他网站加载 PDF 时，可能会受到浏览器的同源策略限制，影响历史功能的使用。</li>
      <li>在移动设备上，后退按钮行为可能与桌面浏览器不同，测试时应考虑这些差异。</li>
      <li>处理大型 PDF 文档时，频繁的历史状态更新可能会导致性能问题。考虑使用节流或限制更新频率。</li>
      <li>用户可能会直接编辑 URL 哈希部分。PDFHistory 应能够处理无效或损坏的哈希值，避免应用程序崩溃。</li>
    </ul>
  </div>

  <script>
    // 在页面加载完成后初始化 Mermaid
    document.addEventListener('DOMContentLoaded', function() {
      mermaid.initialize({ startOnLoad: true });
      
      // 生成目录
      generateTOC();
    });
  </script>
</body>
</html> 