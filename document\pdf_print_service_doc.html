<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PDFPrintService 文档 - PDF.js</title>
  <link rel="stylesheet" href="doc_styles.css">
</head>
<body>
  <header>
    <h1>PDFPrintService</h1>
    <p class="module-description">PDF打印服务，负责处理文档的打印预览与输出</p>
    <a href="index.html" class="back-link">返回模块列表</a>
  </header>

  <div class="content">
    <section class="module-intro">
      <h2>模块介绍</h2>
      <p>PDFPrintService 是 PDF.js 中负责处理 PDF 文档打印的核心组件。它将 PDF 页面渲染为高质量图像，组织成可打印的 DOM 结构，然后调用浏览器的打印功能完成实际打印。该服务处理各种打印相关的任务，包括页面尺寸适配、打印进度显示、注释处理，以及支持各种打印选项，确保 PDF 内容能被准确地打印输出。</p>
      
      <div class="module-diagram">
        <h3>PDFPrintService 在系统中的位置</h3>
        <div class="mermaid">
          graph TD
            PDFViewerApplication -- 调用 --> PDFPrintServiceFactory
            PDFPrintServiceFactory -- 创建 --> PDFPrintService
            PDFPrintService -- 渲染 --> PDF["PDF页面"]
            PDFPrintService -- 输出到 --> PrintContainer["打印容器"]
            PDFPrintService -- 触发 --> BrowserPrint["浏览器打印对话框"]
            BrowserPrint -- 输出 --> Printer["打印机"]
        </div>
      </div>
    </section>

    <section class="architecture">
      <h2>架构设计</h2>
      <p>PDFPrintService 采用工厂模式设计，通过 PDFPrintServiceFactory 创建实例。整个打印流程设计为状态驱动的过程，包含以下主要组件：</p>
      <ul>
        <li><strong>PDFPrintServiceFactory</strong>: 工厂类，负责创建和管理打印服务实例</li>
        <li><strong>PDFPrintService</strong>: 核心服务类，管理整个打印过程</li>
        <li><strong>打印容器</strong>: 用于承载打印内容的DOM元素</li>
        <li><strong>打印对话框</strong>: 显示打印进度和提供取消选项的UI组件</li>
      </ul>
      
      <div class="mermaid">
        flowchart TD
          A[调用window.print] -->|触发| B["beforeprint事件"]
          B --> C["创建PDFPrintService实例"]
          C --> D["设置打印布局"]
          D --> E["渲染PDF页面为图像"]
          E --> F["添加到打印容器"]
          F --> G["调用浏览器打印功能"]
          G --> H["打印完成或取消"]
          H --> I["销毁服务实例"]
          I --> J["触发afterprint事件"]
      </div>
    </section>

    <section class="properties">
      <h2>属性</h2>
      
      <h3>PDFPrintService 类属性</h3>
      <div class="property">
        <h4>pdfDocument</h4>
        <p>要打印的PDF文档对象，包含了所有页面和文档结构信息。</p>
      </div>
      
      <div class="property">
        <h4>pagesOverview</h4>
        <p>页面概览信息数组，每个元素包含页面的宽度、高度和旋转角度等信息。</p>
        <pre><code>// pagesOverview 示例
[
  { width: 612, height: 792, rotation: 0 },  // 第一页
  { width: 612, height: 792, rotation: 0 },  // 第二页
  ...
]</code></pre>
      </div>
      
      <div class="property">
        <h4>printContainer</h4>
        <p>用于存放打印内容的DOM容器，通常是一个隐藏的div元素，专门用于打印输出。</p>
      </div>
      
      <div class="property">
        <h4>currentPage</h4>
        <p>当前正在处理的页面索引，初始值为-1，随着渲染进度递增。</p>
      </div>
      
      <div class="property">
        <h4>scratchCanvas</h4>
        <p>临时画布元素，用于渲染PDF页面为图像。每个页面会重复使用此画布进行渲染。</p>
      </div>
      
      <div class="property">
        <h4>pageStyleSheet</h4>
        <p>页面样式表元素，用于设置打印页面的尺寸和其他CSS属性。</p>
      </div>
      
      <h3>私有属性</h3>
      <div class="property">
        <h4>_printResolution</h4>
        <p>打印分辨率，单位为DPI（每英寸点数），默认值为150。</p>
      </div>
      
      <div class="property">
        <h4>_optionalContentConfigPromise</h4>
        <p>可选内容配置Promise，用于处理PDF中的可选内容（如图层）在打印时的显示状态。</p>
      </div>
      
      <div class="property">
        <h4>_printAnnotationStoragePromise</h4>
        <p>注释存储Promise，用于获取打印时的注释数据，包括表单数据和用户添加的注释。</p>
      </div>
    </section>

    <section class="methods">
      <h2>方法</h2>

      <h3>PDFPrintService 类方法</h3>

      <div class="method">
        <h4>constructor({ pdfDocument, pagesOverview, printContainer, printResolution, printAnnotationStoragePromise })</h4>
        <p>创建一个新的打印服务实例。</p>
        <h5>参数：</h5>
        <ul>
          <li><code>pdfDocument</code> - PDF文档对象</li>
          <li><code>pagesOverview</code> - 页面概览信息数组</li>
          <li><code>printContainer</code> - 打印容器元素</li>
          <li><code>printResolution</code> - 打印分辨率，默认为150 DPI</li>
          <li><code>printAnnotationStoragePromise</code> - 注释存储Promise，默认为已解析的空Promise</li>
        </ul>
        
        <pre><code>const printService = PDFPrintServiceFactory.createPrintService({
  pdfDocument: this.pdfDocument,
  pagesOverview: this.pdfViewer.getPagesOverview(),
  printContainer: document.getElementById('printContainer'),
  printResolution: 150,
  printAnnotationStoragePromise: this.pdfDocument.annotationStorage.print
});</code></pre>
      </div>

      <div class="method">
        <h4>layout()</h4>
        <p>设置打印布局，包括创建页面样式表和设置打印标记。</p>
        
        <pre><code>// 设置打印布局
printService.layout();

// 内部实现会添加样式表
// @page { size: 612pt 792pt; }</code></pre>
      </div>

      <div class="method">
        <h4>renderPages()</h4>
        <p>渲染所有页面为图像，并添加到打印容器中。这个过程是异步的，返回Promise。</p>
        <h5>返回：</h5>
        <p>Promise，在所有页面渲染完成后解析</p>
        
        <pre><code>// 渲染所有页面
printService.renderPages()
  .then(() => {
    console.log('所有页面渲染完成，准备打印');
  })
  .catch(error => {
    console.error('渲染页面失败:', error);
  });</code></pre>
      </div>

      <div class="method">
        <h4>useRenderedPage()</h4>
        <p>将渲染好的页面添加到打印容器中。由renderPages内部调用。</p>
        <h5>返回：</h5>
        <p>Promise，在页面添加到容器后解析</p>
        
        <pre><code>// 内部方法，通常不直接调用
printService.useRenderedPage().then(() => {
  console.log('页面已添加到打印容器');
});</code></pre>
      </div>

      <div class="method">
        <h4>performPrint()</h4>
        <p>执行实际的打印操作，调用浏览器的打印功能。</p>
        <h5>返回：</h5>
        <p>Promise，在打印对话框关闭后解析</p>
        
        <pre><code>// 执行打印
printService.performPrint().then(() => {
  console.log('打印对话框已关闭');
});</code></pre>
      </div>

      <div class="method">
        <h4>destroy()</h4>
        <p>销毁打印服务，清理资源。</p>
        
        <pre><code>// 销毁打印服务
printService.destroy();</code></pre>
      </div>

      <div class="method">
        <h4>throwIfInactive()</h4>
        <p>检查服务是否活跃，如果不活跃则抛出异常。内部用于防止在服务已取消时继续执行操作。</p>
        
        <pre><code>// 内部方法，用于验证服务状态
try {
  printService.throwIfInactive();
  // 继续执行打印操作
} catch (error) {
  console.error('打印服务已不活跃:', error);
}</code></pre>
      </div>

      <h3>PDFPrintServiceFactory 静态方法</h3>

      <div class="method">
        <h4>initGlobals(app)</h4>
        <p>初始化全局变量，为打印服务提供应用程序上下文。</p>
        <h5>参数：</h5>
        <ul>
          <li><code>app</code> - PDF查看器应用程序实例</li>
        </ul>
        
        <pre><code>// 初始化打印服务工厂
PDFPrintServiceFactory.initGlobals(PDFViewerApplication);</code></pre>
      </div>

      <div class="method">
        <h4>createPrintService(params)</h4>
        <p>创建新的打印服务实例。</p>
        <h5>参数：</h5>
        <ul>
          <li><code>params</code> - 与PDFPrintService构造函数参数相同</li>
        </ul>
        <h5>返回：</h5>
        <p>新创建的PDFPrintService实例</p>
        
        <pre><code>// 创建打印服务
const printService = PDFPrintServiceFactory.createPrintService({
  pdfDocument,
  pagesOverview,
  printContainer,
  printResolution: 300
});</code></pre>
      </div>

      <div class="method">
        <h4>get supportsPrinting()</h4>
        <p>检查是否支持打印功能的getter方法。</p>
        <h5>返回：</h5>
        <p>布尔值，表示是否支持打印</p>
        
        <pre><code>// 检查是否支持打印
if (PDFPrintServiceFactory.supportsPrinting) {
  // 执行打印操作
} else {
  alert('当前环境不支持打印功能');
}</code></pre>
      </div>
    </section>

    <section class="usage">
      <h2>使用示例</h2>

      <div class="example">
        <h3>基本打印流程</h3>
        <pre><code>// 在PDF查看器应用程序中的打印实现
class PDFViewerApplication {
  // ...
  
  beforePrint() {
    // 获取注释存储数据
    this._printAnnotationStoragePromise = this.pdfScriptingManager.dispatchWillPrint()
      .catch(() => {})
      .then(() => this.pdfDocument?.annotationStorage.print);
    
    // 检查是否已有打印服务
    if (this.printService) {
      return;
    }
    
    // 检查是否支持打印
    if (!this.supportsPrinting) {
      this._otherError("pdfjs-printing-not-supported");
      return;
    }
    
    // 确保所有页面已准备好
    if (!this.pdfViewer.pageViewsReady) {
      this.l10n.get("pdfjs-printing-not-ready").then(msg => {
        window.alert(msg);
      });
      return;
    }
    
    // 创建打印服务
    this.printService = PDFPrintServiceFactory.createPrintService({
      pdfDocument: this.pdfDocument,
      pagesOverview: this.pdfViewer.getPagesOverview(),
      printContainer: this.appConfig.printContainer,
      printResolution: AppOptions.get("printResolution"),
      printAnnotationStoragePromise: this._printAnnotationStoragePromise
    });
    
    // 设置布局
    this.printService.layout();
  }
  
  afterPrint() {
    if (this.printService) {
      this.printService.destroy();
      this.printService = null;
    }
  }
}</code></pre>
      </div>

      <div class="example">
        <h3>触发打印流程</h3>
        <pre><code>// 用户界面中的打印按钮事件处理
document.getElementById('print-button').addEventListener('click', () => {
  // 直接调用window.print()，它已被PDF.js重写
  window.print();
});

// window.print()的实现（已由PDF.js重写）
window.print = function() {
  if (activeService) {
    console.warn("Ignored window.print() because of a pending print job.");
    return;
  }
  
  // 显示打印对话框
  ensureOverlay().then(() => {
    if (activeService) {
      overlayManager.open(dialog);
    }
  });
  
  try {
    // 触发beforeprint事件，会调用PDFViewerApplication.beforePrint
    dispatchEvent("beforeprint");
  } finally {
    if (!activeService) {
      console.error("Expected print service to be initialized.");
      ensureOverlay().then(() => {
        overlayManager.closeIfActive(dialog);
      });
    } else {
      // 执行打印流程
      const activeServiceOnEntry = activeService;
      activeService.renderPages()
        .then(() => activeServiceOnEntry.performPrint())
        .catch(() => {})
        .then(() => {
          if (activeServiceOnEntry.active) {
            abort();
          }
        });
    }
  }
};</code></pre>
      </div>

      <div class="example">
        <h3>取消打印</h3>
        <pre><code>// 打印对话框中的取消按钮
document.getElementById('printCancel').onclick = abort;

// 取消打印的函数
function abort() {
  if (activeService) {
    // 销毁打印服务
    activeService.destroy();
    // 触发afterprint事件
    dispatchEvent("afterprint");
  }
}</code></pre>
      </div>
    </section>

    <section class="implementation-notes">
      <h2>实现说明</h2>
      <div class="note">
        <h3>打印流程</h3>
        <p>打印流程分为几个关键阶段：</p>
        <ol>
          <li><strong>准备阶段</strong>：创建打印服务，设置布局和样式</li>
          <li><strong>渲染阶段</strong>：将PDF页面渲染为高分辨率图像</li>
          <li><strong>组装阶段</strong>：将渲染好的页面添加到打印容器</li>
          <li><strong>打印阶段</strong>：触发浏览器打印机制</li>
          <li><strong>清理阶段</strong>：销毁打印服务，释放资源</li>
        </ol>
      </div>
      
      <div class="note">
        <h3>单例模式</h3>
        <p>PDFPrintService 使用单例模式，通过全局变量 <code>activeService</code> 确保同一时间只有一个打印服务实例。如果尝试在已有活动打印服务时创建新实例，会抛出错误。</p>
      </div>
      
      <div class="note">
        <h3>页面渲染</h3>
        <p>页面渲染采用递归方式，一次渲染一个页面，并更新进度显示。渲染完成后，将页面转换为图像，并添加到打印容器中。这种方式可以处理大型文档，避免一次性渲染所有页面导致的内存问题。</p>
      </div>
      
      <div class="note">
        <h3>浏览器打印机制</h3>
        <p>PDFPrintService 重写了 <code>window.print()</code> 方法，以便在打印前完成必要的渲染工作。当所有页面渲染完成后，它会调用原始的 <code>print</code> 方法触发浏览器的打印对话框。</p>
      </div>
      
      <div class="note">
        <h3>事件处理</h3>
        <p>打印过程中会触发两个关键事件：</p>
        <ul>
          <li><code>beforeprint</code>：在打印开始前触发，用于初始化打印服务</li>
          <li><code>afterprint</code>：在打印完成或取消后触发，用于清理资源</li>
        </ul>
        <p>PDF.js 确保这些事件只在其控制下触发，防止外部事件干扰打印流程。</p>
      </div>
    </section>

    <section class="best-practices">
      <h2>最佳实践</h2>
      <div class="practice">
        <h3>检查打印准备状态</h3>
        <p>在调用打印功能前，应确保PDF已完全加载并且所有页面视图已准备好：</p>
        <pre><code>// 在触发打印前检查
if (!pdfViewer.pageViewsReady) {
  alert('PDF尚未完全加载，请稍后再试');
  return;
}</code></pre>
      </div>
      
      <div class="practice">
        <h3>设置合适的打印分辨率</h3>
        <p>打印分辨率会影响输出质量和渲染性能，应根据需要设置合适的值：</p>
        <pre><code>// 设置打印分辨率
// 较低的值（如96）适合快速打印，较高的值（如300）适合高质量输出
AppOptions.set("printResolution", 150);</code></pre>
      </div>
      
      <div class="practice">
        <h3>处理打印事件</h3>
        <p>确保正确处理打印事件，以便在打印前后执行必要的操作：</p>
        <pre><code>// 监听打印事件
window.addEventListener('beforeprint', () => {
  // 准备打印前的操作
  console.log('准备打印...');
});

window.addEventListener('afterprint', () => {
  // 打印后的清理操作
  console.log('打印已完成或取消');
});</code></pre>
      </div>
      
      <div class="practice">
        <h3>注意页面尺寸一致性</h3>
        <p>当文档包含不同尺寸的页面时，打印服务会显示警告。为获得最佳效果，应确保页面尺寸一致：</p>
        <pre><code>// 检查页面尺寸一致性
const { width, height } = pagesOverview[0];
const hasEqualPageSizes = pagesOverview.every(
  size => size.width === width && size.height === height
);

if (!hasEqualPageSizes) {
  console.warn('文档包含不同尺寸的页面，打印效果可能不理想');
}</code></pre>
      </div>
      
      <div class="practice">
        <h3>提供打印进度反馈</h3>
        <p>对于大型文档，渲染可能需要一定时间，应提供进度反馈：</p>
        <pre><code>// 打印进度更新示例
function renderProgress(index, total) {
  const progress = Math.round(100 * index / total);
  progressBar.value = progress;
  progressText.textContent = `${progress}%`;
}</code></pre>
      </div>
    </section>
  </div>

  <script src="doc_script.js"></script>
  <script src="js/mermaid.js"></script>
  <script>
    mermaid.initialize({ startOnLoad: true, theme: 'neutral' });
  </script>
</body>
</html> 