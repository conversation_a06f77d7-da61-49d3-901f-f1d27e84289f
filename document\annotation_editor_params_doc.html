<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AnnotationEditorParams - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#constants">常量</a>
    <a href="#properties">属性</a>
    <a href="#methods">方法</a>
    <a href="#flowcharts">流程图</a>
    <a href="#examples">示例</a>
    <a href="#notes">注意事项</a>
  </div>

  <h1>AnnotationEditorParams 模块文档</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>AnnotationEditorParams 是 PDF.js 库中用于配置和管理 PDF 注释编辑器行为的组件。它提供了一组参数和设置，用于控制注释的创建、编辑和显示方式。通过这个组件，可以自定义注释编辑器的外观、行为和功能，使其适应不同的应用场景和用户需求。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 常量 -->
  <div id="constants">
    <h2>常量</h2>
    
    <p>AnnotationEditorParams 使用以下常量：</p>
    
    <ul>
      <li>
        <code>AnnotationEditorType</code>: 注释编辑器类型枚举
        <ul>
          <li><code>NONE</code>: 无编辑器</li>
          <li><code>FREETEXT</code>: 自由文本编辑器</li>
          <li><code>INK</code>: 墨迹/手写编辑器</li>
          <li><code>STAMP</code>: 图章编辑器</li>
          <li><code>HIGHLIGHT</code>: 高亮编辑器</li>
        </ul>
      </li>
      <li>
        <code>AnnotationEditorParamsType</code>: 注释编辑器参数类型枚举
        <ul>
          <li><code>FREETEXT_SIZE</code>: 自由文本大小</li>
          <li><code>FREETEXT_COLOR</code>: 自由文本颜色</li>
          <li><code>FREETEXT_FONT_TYPE</code>: 自由文本字体类型</li>
          <li><code>INK_COLOR</code>: 墨迹颜色</li>
          <li><code>INK_THICKNESS</code>: 墨迹粗细</li>
          <li><code>INK_OPACITY</code>: 墨迹不透明度</li>
          <li><code>HIGHLIGHT_COLOR</code>: 高亮颜色</li>
          <li><code>HIGHLIGHT_OPACITY</code>: 高亮不透明度</li>
        </ul>
      </li>
    </ul>
  </div>

  <!-- 属性 -->
  <div id="properties">
    <h2>属性</h2>
    
    <h3>公共属性</h3>
    <ul>
      <li><code>editorType</code>: 当前选择的编辑器类型</li>
      <li><code>freeTextSize</code>: 自由文本注释的字体大小</li>
      <li><code>freeTextColor</code>: 自由文本注释的颜色</li>
      <li><code>freeTextFontString</code>: 自由文本注释的字体字符串</li>
      <li><code>inkColor</code>: 墨迹注释的颜色</li>
      <li><code>inkThickness</code>: 墨迹注释的线条粗细</li>
      <li><code>inkOpacity</code>: 墨迹注释的不透明度</li>
      <li><code>highlightColor</code>: 高亮注释的颜色</li>
      <li><code>highlightOpacity</code>: 高亮注释的不透明度</li>
      <li><code>keybindings</code>: 注释编辑器的键盘绑定配置</li>
    </ul>

    <h3>私有属性</h3>
    <ul>
      <li><code>#eventBus</code>: 事件总线</li>
      <li><code>#annotationStorage</code>: 注释存储</li>
      <li><code>#freeTextFontType</code>: 自由文本字体类型</li>
      <li><code>#defaultFontSize</code>: 默认自由文本字体大小</li>
      <li><code>#lastUsedValues</code>: 上次使用的值的存储</li>
      <li><code>#defaultParamsMap</code>: 默认参数映射</li>
      <li><code>#keybindings</code>: 内部键盘绑定存储</li>
    </ul>
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法</h2>
    
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">构造函数</h3>
      </div>
      <div class="method-content">
        <p>创建一个新的 AnnotationEditorParams 实例。</p>
        <pre><code class="language-javascript">
constructor({
  eventBus,
  annotationStorage = null,
  defaultParamsMap = null,
})
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>eventBus</code>: 事件总线</li>
          <li><code>annotationStorage</code>: 注释存储 (可选，默认为 null)</li>
          <li><code>defaultParamsMap</code>: 默认参数映射 (可选，默认为 null)</li>
        </ul>
      </div>
    </div>

    <h3>核心方法</h3>
    <ul>
      <li><code>setParams(type, value)</code>: 设置特定类型的参数值</li>
      <li><code>getParams(type)</code>: 获取特定类型的参数值</li>
      <li><code>setDefaults(type)</code>: 将特定编辑器类型的参数重置为默认值</li>
      <li><code>getDefault(type)</code>: 获取特定参数类型的默认值</li>
      <li><code>setKeybinding(key, command)</code>: 设置键盘绑定</li>
      <li><code>getKeybinding(key)</code>: 获取键盘绑定</li>
      <li><code>removeKeybinding(key)</code>: 移除键盘绑定</li>
      <li><code>createNewDrawingLayer()</code>: 创建新的绘图图层</li>
      <li><code>destroy()</code>: 销毁注释编辑器参数实例</li>
    </ul>
    
    <h3>私有方法</h3>
    <ul>
      <li><code>#registerKeyboardActions()</code>: 注册键盘操作</li>
      <li><code>#dispatchUpdateStates(type)</code>: 分发更新状态事件</li>
      <li><code>#dispatchUpdateUI(detail)</code>: 分发更新界面事件</li>
      <li><code>#saveLastUsed(type, value)</code>: 保存上次使用的值</li>
      <li><code>#loadLastUsed(type)</code>: 加载上次使用的值</li>
      <li><code>#getDefaultFontSize()</code>: 获取默认字体大小</li>
      <li><code>#getDefaultFont()</code>: 获取默认字体</li>
      <li><code>#updateFontFamily()</code>: 更新字体族</li>
      <li><code>#buildDefaultParamsMap()</code>: 构建默认参数映射</li>
      <li><code>#serialize()</code>: 序列化参数</li>
      <li><code>#deserialize(data)</code>: 反序列化参数</li>
    </ul>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <h3>参数设置流程</h3>
    <p>1. 调用 setParams 设置参数<br>
    2. 根据参数类型处理值<br>
    3. 保存为最后使用的值<br>
    4. 分发更新状态事件<br>
    5. 通知界面更新</p>
    
    <div class="mermaid">
      graph TD
          A[调用 setParams 方法] --> B[根据参数类型验证值]
          B --> C[保存参数值]
          C --> D[调用 #saveLastUsed 方法]
          D --> E[调用 #dispatchUpdateStates 方法]
          E --> F[触发 annotationeditorparamschanged 事件]
          F --> G[编辑器组件收到事件并更新]
    </div>
    
    <h3>编辑器类型切换流程</h3>
    <p>1. 设置新的编辑器类型<br>
    2. 加载对应类型的上次使用值<br>
    3. 更新界面状态<br>
    4. 应用相应的工具栏设置</p>
    
    <div class="mermaid">
      graph TD
          A[设置新的编辑器类型] --> B[调用 setParams 方法]
          B --> C[更新 editorType 属性]
          C --> D[检查是否有上次使用的值]
          D -->|是| E[加载上次使用的参数]
          D -->|否| F[使用默认参数]
          E --> G[更新编辑器工具栏]
          F --> G
          G --> H[触发 annotationeditorparamsupdated 事件]
    </div>
    
    <h3>键盘绑定处理流程</h3>
    <p>1. 注册键盘绑定<br>
    2. 键盘事件触发<br>
    3. 查找匹配的绑定<br>
    4. 执行对应的命令</p>
    
    <div class="mermaid">
      graph TD
          A[调用 setKeybinding 方法] --> B[将键绑定到命令]
          B --> C[存储在 #keybindings 中]
          D[键盘事件触发] --> E[查找匹配的键绑定]
          E -->|找到| F[执行关联命令]
          E -->|未找到| G[继续正常事件处理]
          F --> H[阻止默认事件行为]
    </div>
  </div>

  <!-- 示例 -->
  <div id="examples">
    <h2>使用示例</h2>
    
    <h3>基本用法</h3>
    <pre><code class="language-javascript">
// 创建 AnnotationEditorParams 实例
const editorParams = new AnnotationEditorParams({
  eventBus: eventBus,
  annotationStorage: pdfDocument.annotationStorage
});

// 设置自由文本编辑器为当前编辑器类型
editorParams.setParams(AnnotationEditorParamsType.EDITOR_TYPE, AnnotationEditorType.FREETEXT);

// 设置自由文本颜色为红色
editorParams.setParams(AnnotationEditorParamsType.FREETEXT_COLOR, "#FF0000");

// 设置自由文本大小
editorParams.setParams(AnnotationEditorParamsType.FREETEXT_SIZE, 14);

// 获取当前自由文本颜色
const textColor = editorParams.getParams(AnnotationEditorParamsType.FREETEXT_COLOR);
console.log('当前文本颜色:', textColor);

// 重置自由文本编辑器参数为默认值
editorParams.setDefaults(AnnotationEditorType.FREETEXT);

// 监听参数变化事件
eventBus.on('annotationeditorparamschanged', function(event) {
  console.log('参数已更新:', event.detail);
  
  // 可以在这里更新 UI 显示
  updateEditorUI(event.detail);
});
    </code></pre>
    
    <h3>自定义工具栏与参数联动</h3>
    <pre><code class="language-javascript">
// 创建工具栏控制函数
function setupAnnotationToolbar() {
  // 编辑器类型选择器
  const editorTypeSelect = document.getElementById('editorTypeSelect');
  editorTypeSelect.addEventListener('change', function() {
    const editorType = parseInt(this.value, 10);
    editorParams.setParams(AnnotationEditorParamsType.EDITOR_TYPE, editorType);
    
    // 根据选择的编辑器类型显示/隐藏相应的控制选项
    updateToolbarControls(editorType);
  });
  
  // 自由文本颜色选择器
  const textColorPicker = document.getElementById('textColorPicker');
  textColorPicker.addEventListener('input', function() {
    editorParams.setParams(AnnotationEditorParamsType.FREETEXT_COLOR, this.value);
  });
  
  // 自由文本大小选择器
  const textSizeSelect = document.getElementById('textSizeSelect');
  textSizeSelect.addEventListener('change', function() {
    const fontSize = parseInt(this.value, 10);
    editorParams.setParams(AnnotationEditorParamsType.FREETEXT_SIZE, fontSize);
  });
  
  // 自由文本字体选择器
  const fontFamilySelect = document.getElementById('fontFamilySelect');
  fontFamilySelect.addEventListener('change', function() {
    editorParams.setParams(AnnotationEditorParamsType.FREETEXT_FONT_TYPE, this.value);
  });
  
  // 墨迹颜色选择器
  const inkColorPicker = document.getElementById('inkColorPicker');
  inkColorPicker.addEventListener('input', function() {
    editorParams.setParams(AnnotationEditorParamsType.INK_COLOR, this.value);
  });
  
  // 墨迹粗细滑块
  const inkThicknessSlider = document.getElementById('inkThicknessSlider');
  inkThicknessSlider.addEventListener('input', function() {
    const thickness = parseFloat(this.value);
    editorParams.setParams(AnnotationEditorParamsType.INK_THICKNESS, thickness);
  });
  
  // 墨迹不透明度滑块
  const inkOpacitySlider = document.getElementById('inkOpacitySlider');
  inkOpacitySlider.addEventListener('input', function() {
    const opacity = parseFloat(this.value);
    editorParams.setParams(AnnotationEditorParamsType.INK_OPACITY, opacity);
  });
  
  // 监听参数变化事件以更新 UI
  eventBus.on('annotationeditorparamschanged', function(event) {
    const { type, value } = event.detail;
    
    // 更新相应的 UI 控件
    switch (type) {
      case AnnotationEditorParamsType.EDITOR_TYPE:
        editorTypeSelect.value = value;
        updateToolbarControls(value);
        break;
      case AnnotationEditorParamsType.FREETEXT_COLOR:
        textColorPicker.value = value;
        break;
      case AnnotationEditorParamsType.FREETEXT_SIZE:
        textSizeSelect.value = value;
        break;
      case AnnotationEditorParamsType.FREETEXT_FONT_TYPE:
        fontFamilySelect.value = value;
        break;
      case AnnotationEditorParamsType.INK_COLOR:
        inkColorPicker.value = value;
        break;
      case AnnotationEditorParamsType.INK_THICKNESS:
        inkThicknessSlider.value = value;
        break;
      case AnnotationEditorParamsType.INK_OPACITY:
        inkOpacitySlider.value = value;
        break;
    }
  });
}

// 根据编辑器类型更新工具栏控件的显示状态
function updateToolbarControls(editorType) {
  const freetextControls = document.getElementById('freetextControls');
  const inkControls = document.getElementById('inkControls');
  const highlightControls = document.getElementById('highlightControls');
  
  // 隐藏所有控件
  freetextControls.style.display = 'none';
  inkControls.style.display = 'none';
  highlightControls.style.display = 'none';
  
  // 根据编辑器类型显示相应控件
  switch (editorType) {
    case AnnotationEditorType.FREETEXT:
      freetextControls.style.display = 'block';
      break;
    case AnnotationEditorType.INK:
      inkControls.style.display = 'block';
      break;
    case AnnotationEditorType.HIGHLIGHT:
      highlightControls.style.display = 'block';
      break;
  }
}

// 初始化工具栏
document.addEventListener('DOMContentLoaded', function() {
  setupAnnotationToolbar();
  
  // 默认选择自由文本编辑器
  editorParams.setParams(AnnotationEditorParamsType.EDITOR_TYPE, AnnotationEditorType.FREETEXT);
});
    </code></pre>
    
    <h3>自定义键盘绑定</h3>
    <pre><code class="language-javascript">
// 设置自定义键盘绑定
function setupCustomKeybindings() {
  // 设置 Ctrl+T 切换到文本编辑器
  editorParams.setKeybinding('ctrl+t', () => {
    editorParams.setParams(AnnotationEditorParamsType.EDITOR_TYPE, AnnotationEditorType.FREETEXT);
    return true; // 表示已处理该事件
  });
  
  // 设置 Ctrl+D 切换到绘图（墨迹）编辑器
  editorParams.setKeybinding('ctrl+d', () => {
    editorParams.setParams(AnnotationEditorParamsType.EDITOR_TYPE, AnnotationEditorType.INK);
    return true;
  });
  
  // 设置 Ctrl+H 切换到高亮编辑器
  editorParams.setKeybinding('ctrl+h', () => {
    editorParams.setParams(AnnotationEditorParamsType.EDITOR_TYPE, AnnotationEditorType.HIGHLIGHT);
    return true;
  });
  
  // 设置 Escape 键退出当前编辑
  editorParams.setKeybinding('escape', () => {
    editorParams.setParams(AnnotationEditorParamsType.EDITOR_TYPE, AnnotationEditorType.NONE);
    return true;
  });
  
  // 设置 Ctrl++ 增加字体大小或线条粗细
  editorParams.setKeybinding('ctrl++', () => {
    const editorType = editorParams.getParams(AnnotationEditorParamsType.EDITOR_TYPE);
    
    switch (editorType) {
      case AnnotationEditorType.FREETEXT:
        const currentSize = editorParams.getParams(AnnotationEditorParamsType.FREETEXT_SIZE);
        editorParams.setParams(AnnotationEditorParamsType.FREETEXT_SIZE, currentSize + 1);
        break;
      case AnnotationEditorType.INK:
        const currentThickness = editorParams.getParams(AnnotationEditorParamsType.INK_THICKNESS);
        editorParams.setParams(AnnotationEditorParamsType.INK_THICKNESS, currentThickness + 0.5);
        break;
    }
    
    return true;
  });
  
  // 设置 Ctrl+- 减小字体大小或线条粗细
  editorParams.setKeybinding('ctrl+-', () => {
    const editorType = editorParams.getParams(AnnotationEditorParamsType.EDITOR_TYPE);
    
    switch (editorType) {
      case AnnotationEditorType.FREETEXT:
        const currentSize = editorParams.getParams(AnnotationEditorParamsType.FREETEXT_SIZE);
        editorParams.setParams(AnnotationEditorParamsType.FREETEXT_SIZE, Math.max(currentSize - 1, 6));
        break;
      case AnnotationEditorType.INK:
        const currentThickness = editorParams.getParams(AnnotationEditorParamsType.INK_THICKNESS);
        editorParams.setParams(AnnotationEditorParamsType.INK_THICKNESS, Math.max(currentThickness - 0.5, 1));
        break;
    }
    
    return true;
  });
}

// 初始化键盘绑定
setupCustomKeybindings();

// 显示当前快捷键配置
function displayKeyboardShortcuts() {
  const shortcutsContainer = document.getElementById('keyboardShortcuts');
  shortcutsContainer.innerHTML = '';
  
  const shortcuts = [
    { key: 'Ctrl+T', description: '切换到文本编辑器' },
    { key: 'Ctrl+D', description: '切换到绘图编辑器' },
    { key: 'Ctrl+H', description: '切换到高亮编辑器' },
    { key: 'Escape', description: '退出当前编辑' },
    { key: 'Ctrl++', description: '增加字体大小/线条粗细' },
    { key: 'Ctrl+-', description: '减小字体大小/线条粗细' }
  ];
  
  const table = document.createElement('table');
  table.className = 'shortcuts-table';
  
  // 添加表头
  const thead = document.createElement('thead');
  thead.innerHTML = '<tr><th>快捷键</th><th>功能</th></tr>';
  table.appendChild(thead);
  
  // 添加表格内容
  const tbody = document.createElement('tbody');
  shortcuts.forEach(shortcut => {
    const row = document.createElement('tr');
    row.innerHTML = `<td>${shortcut.key}</td><td>${shortcut.description}</td>`;
    tbody.appendChild(row);
  });
  
  table.appendChild(tbody);
  shortcutsContainer.appendChild(table);
}

// 显示快捷键信息
document.getElementById('showShortcuts').addEventListener('click', displayKeyboardShortcuts);
    </code></pre>
  </div>

  <!-- 注意事项 -->
  <div id="notes">
    <h2>注意事项</h2>
    
    <ul>
      <li>编辑器参数的变更会通过事件总线（EventBus）广播到应用程序的其他部分。确保正确处理这些事件以保持界面的一致性。</li>
      <li>某些参数可能需要特定的格式或范围。例如，颜色值通常需要是有效的 CSS 颜色字符串，而大小和不透明度值需要在合理的范围内。</li>
      <li>注释编辑器的行为可能受到 PDF 文档权限的限制。在某些受保护的文档中，注释编辑可能被禁用或受限。</li>
      <li>当切换编辑器类型时，应确保相关的工具栏和控制元素也相应更新，以提供一致的用户体验。</li>
      <li>键盘绑定可能与浏览器或操作系统的默认快捷键冲突。在设置自定义键盘绑定时，应避免使用常见的系统级快捷键。</li>
      <li>注释编辑器参数的状态应该在用户会话中保持一致。考虑使用浏览器的本地存储（如 localStorage）来保存用户的偏好设置。</li>
      <li>在移动设备上，某些参数（如线条粗细）可能需要调整以适应触摸交互的特性。考虑为不同的设备类型提供不同的默认值。</li>
      <li>高分辨率显示器（如 Retina 显示屏）可能需要调整线条粗细和字体大小等参数，以确保注释在各种设备上具有一致的视觉效果。</li>
    </ul>
  </div>

  <script>
    // 在页面加载完成后初始化 Mermaid
    document.addEventListener('DOMContentLoaded', function() {
      mermaid.initialize({ startOnLoad: true });
      
      // 生成目录
      generateTOC();
    });
  </script>
</body>
</html> 