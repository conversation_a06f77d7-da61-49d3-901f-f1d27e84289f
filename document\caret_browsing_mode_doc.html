<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CaretBrowsingMode - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#methods">方法列表</a>
    <a href="#flowcharts">流程图</a>
  </div>

  <h1>CaretBrowsingMode</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>CaretBrowsingMode是PDF.js中提供插入符（光标）导航功能的模块，允许用户在PDF文档的文本内容中使用键盘导航，类似于网页文本编辑器中的光标操作。该模块实现了在文档文本层中精确控制插入符位置的能力，支持上下移动、跨页面导航以及文本选择功能，为无障碍访问和键盘操作提供了重要支持。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法列表</h2>
    
    <!-- 构造函数 -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">constructor(abortSignal, mainContainer, viewerContainer, toolbarContainer)</h3>
      </div>
      <div class="method-content">
        <p>创建一个新的CaretBrowsingMode实例，初始化插入符浏览模式所需的参数和容器引用。</p>
        <pre><code class="language-javascript">
/**
 * 创建插入符浏览模式实例
 * 
 * @param {AbortSignal} abortSignal - 中止信号，用于清理资源
 * @param {HTMLElement} mainContainer - 主容器元素
 * @param {HTMLElement} viewerContainer - 查看器容器元素
 * @param {HTMLElement} toolbarContainer - 工具栏容器元素
 */
constructor(abortSignal, mainContainer, viewerContainer, toolbarContainer) {
  // 保存容器引用
  this.#mainContainer = mainContainer;
  this.#viewerContainer = viewerContainer;
  
  // 如果没有工具栏容器，则不进行工具栏高度计算
  if (!toolbarContainer) {
    return;
  }
  
  // 获取初始工具栏高度
  this.#toolBarHeight = toolbarContainer.getBoundingClientRect().height;
  
  // 创建工具栏调整大小观察器，以便在工具栏大小变化时更新高度
  const toolbarObserver = new ResizeObserver(entries => {
    for (const entry of entries) {
      if (entry.target === toolbarContainer) {
        // 更新工具栏高度（向下取整以避免小数误差）
        this.#toolBarHeight = Math.floor(entry.borderBoxSize[0].blockSize);
        break;
      }
    }
  });
  
  // 开始观察工具栏尺寸变化
  toolbarObserver.observe(toolbarContainer);
  
  // 在中止信号触发时断开观察器连接
  abortSignal.addEventListener("abort", () => toolbarObserver.disconnect(), {
    once: true
  });
}
        </code></pre>
      </div>
    </div>
    
    <!-- moveCaret方法 -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">moveCaret(isUp, select)</h3>
      </div>
      <div class="method-content">
        <p>在PDF文档文本中移动插入符（光标），支持向上和向下移动，并可选择是否扩展当前选择区域。</p>
        <pre><code class="language-javascript">
/**
 * 移动插入符
 * 在文本中向上或向下导航，可选择是否扩展当前选择区域
 * 
 * @param {boolean} isUp - 是向上导航(true)还是向下导航(false)
 * @param {boolean} select - 是否扩展当前选择(true)或重新设置位置(false)
 */
moveCaret(isUp, select) {
  // 获取当前选择对象
  const selection = document.getSelection();
  
  // 如果没有选择范围，则不执行操作
  if (selection.rangeCount === 0) {
    return;
  }
  
  // 获取当前焦点节点
  const { focusNode } = selection;
  
  // 获取焦点元素（如果焦点节点是文本节点，则获取其父元素）
  const focusElement = focusNode.nodeType !== Node.ELEMENT_NODE ? 
    focusNode.parentElement : focusNode;
  
  // 获取包含焦点元素的文本层
  const root = focusElement.closest(".textLayer");
  
  // 如果没有在文本层内，则不执行操作
  if (!root) {
    return;
  }
  
  // 查找并移动到下一行合适的位置
  // ...查找逻辑...
  
  // 处理页面间导航
  // ...页面间导航逻辑...
  
  // 设置新的插入符位置
  // ...设置位置逻辑...
}
        </code></pre>
      </div>
    </div>
    
    <!-- 私有方法: #isOnSameLine -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">#isOnSameLine(rect1, rect2)</h3>
      </div>
      <div class="method-content">
        <p>判断两个矩形（DOM元素）是否在同一行，通过检查矩形的垂直重叠来确定。</p>
        <pre><code class="language-javascript">
/**
 * 判断两个矩形是否在同一行
 * 通过检查矩形的垂直重叠来确定
 * 
 * @param {DOMRect} rect1 - 第一个矩形
 * @param {DOMRect} rect2 - 第二个矩形
 * @returns {boolean} 是否在同一行
 * @private
 */
#isOnSameLine(rect1, rect2) {
  // 获取第一个矩形的上边、下边和中点
  const top1 = rect1.y;
  const bot1 = rect1.bottom;
  const mid1 = rect1.y + rect1.height / 2;
  
  // 获取第二个矩形的上边、下边和中点
  const top2 = rect2.y;
  const bot2 = rect2.bottom;
  const mid2 = rect2.y + rect2.height / 2;
  
  // 判断是否有垂直重叠：
  // 1. 第二个矩形的中点在第一个矩形的垂直范围内，或
  // 2. 第一个矩形的中点在第二个矩形的垂直范围内
  return top1 <= mid2 && mid2 <= bot1 || top2 <= mid1 && mid1 <= bot2;
}
        </code></pre>
      </div>
    </div>
    
    <!-- 私有方法: #setCaretPosition -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">#setCaretPosition(select, selection, newLineElement, newLineElementRect, caretX)</h3>
      </div>
      <div class="method-content">
        <p>设置插入符位置，处理元素在视图中的可见性，必要时滚动到目标元素。</p>
        <pre><code class="language-javascript">
/**
 * 设置插入符位置
 * 处理元素在视图中可见性，必要时滚动到目标元素
 * 
 * @param {boolean} select - 是否扩展当前选择(true)或重新设置位置(false)
 * @param {Selection} selection - 当前选择对象
 * @param {Element} newLineElement - 要设置插入符的目标元素
 * @param {DOMRect} newLineElementRect - 目标元素的边界矩形
 * @param {number} caretX - 插入符的水平坐标
 * @private
 */
#setCaretPosition(select, selection, newLineElement, newLineElementRect, caretX) {
  // 如果目标元素在视图中可见，直接设置插入符位置
  if (this.#isVisible(newLineElementRect)) {
    this.#setCaretPositionHelper(selection, caretX, select, newLineElement, newLineElementRect);
    return;
  }
  
  // 如果目标元素不可见，先滚动到该元素，等待滚动完成后再设置插入符位置
  this.#mainContainer.addEventListener("scrollend", 
    this.#setCaretPositionHelper.bind(this, selection, caretX, select, newLineElement, null), 
    { once: true } // 只执行一次
  );
  
  // 滚动到目标元素
  newLineElement.scrollIntoView();
}
        </code></pre>
      </div>
    </div>
    
    <!-- 私有方法: #getNodeOnNextPage -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">#getNodeOnNextPage(textLayer, isUp)</h3>
      </div>
      <div class="method-content">
        <p>获取下一页或上一页的文本节点，当需要垂直导航到不同页面时使用。</p>
        <pre><code class="language-javascript">
/**
 * 获取下一页或上一页的文本节点
 * 当需要垂直导航到不同页面时使用
 * 
 * @param {Element} textLayer - 当前文本层元素
 * @param {boolean} isUp - 是向上导航(true)还是向下导航(false)
 * @returns {Node|null} 找到的文本节点，如果没有找到则返回null
 * @private
 */
#getNodeOnNextPage(textLayer, isUp) {
  while (true) {
    // 获取当前页面元素和页码
    const page = textLayer.closest(".page");
    const pageNumber = parseInt(page.getAttribute("data-page-number"));
    
    // 计算目标页码（向上导航时页码减1，向下导航时页码加1）
    const nextPage = isUp ? pageNumber - 1 : pageNumber + 1;
    
    // 查找目标页面的文本层
    textLayer = this.#viewerContainer.querySelector(
      `.page[data-page-number="${nextPage}"] .textLayer`
    );
    
    // 如果没有找到目标页面的文本层，返回null
    if (!textLayer) {
      return null;
    }
    
    // 创建文本节点遍历器
    const walker = document.createTreeWalker(textLayer, NodeFilter.SHOW_TEXT);
    
    // 根据导航方向获取第一个或最后一个文本节点
    const node = isUp ? walker.lastChild() : walker.firstChild();
    
    // 如果找到文本节点，返回它
    if (node) {
      return node;
    }
    
    // 如果当前页面没有文本节点，继续循环查找下一个/上一个页面
  }
}
        </code></pre>
      </div>
    </div>
    
    <!-- 静态私有方法: #caretPositionFromPoint -->
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">static #caretPositionFromPoint(x, y)</h3>
      </div>
      <div class="method-content">
        <p>从指定坐标获取插入符位置，处理浏览器兼容性问题。</p>
        <pre><code class="language-javascript">
/**
 * 从点获取插入符位置
 * 处理浏览器兼容性问题
 * 
 * @param {number} x - 水平坐标
 * @param {number} y - 垂直坐标
 * @returns {Object} 包含偏移节点和偏移量的对象
 * @private
 * @static
 */
static #caretPositionFromPoint(x, y) {
  // 处理不支持caretPositionFromPoint的浏览器(如WebKit)
  if (!document.caretPositionFromPoint) {
    const {
      startContainer: offsetNode,
      startOffset: offset
    } = document.caretRangeFromPoint(x, y);
    return {
      offsetNode,
      offset
    };
  }
  
  // 标准方法
  return document.caretPositionFromPoint(x, y);
}
        </code></pre>
      </div>
    </div>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <!-- 插入符移动流程图 -->
    <div class="mermaid">
      graph TD
        A["调用moveCaret(isUp, select)"] --> B{"获取当前选择对象<br>并检查是否有选择范围"}
        B -->|"没有选择范围"| C["结束操作"]
        B -->|"有选择范围"| D["获取焦点节点和其父元素"]
        D --> E{"检查是否在文本层内"}
        E -->|"否"| C
        E -->|"是"| F["创建文本节点遍历器"]
        F --> G["在当前行找到下一行元素"]
        G --> H{"是否找到下一行元素?"}
        H -->|"否"| I["尝试获取下一页文本节点"]
        H -->|"是"| J["获取当前插入符位置坐标"]
        I --> K{"是否找到下一页节点?"}
        K -->|"否"| C
        K -->|"是"| L["处理跨页导航"]
        L --> C
        J --> M["计算新行中最合适的插入点"]
        M --> N["设置插入符到新位置"]
        N --> O{"新位置是否可见?"}
        O -->|"是"| P["直接设置插入符位置"]
        O -->|"否"| Q["滚动到目标元素<br>然后设置插入符位置"]
        P --> C
        Q --> C
    </div>
    
    <!-- 页面间导航流程图 -->
    <div class="mermaid">
      graph TD
        A["调用#getNodeOnNextPage(textLayer, isUp)"] --> B["获取当前页码"]
        B --> C["计算目标页码<br>(向上-1或向下+1)"]
        C --> D["查找目标页面的文本层"]
        D --> E{"是否找到文本层?"}
        E -->|"否"| F["返回null"]
        E -->|"是"| G["创建文本节点遍历器"]
        G --> H["获取第一个或最后一个<br>文本节点(基于导航方向)"]
        H --> I{"是否找到文本节点?"}
        I -->|"是"| J["返回找到的节点"]
        I -->|"否"| K["继续循环查找下一页"]
        K --> B
    </div>
  </div>

  <!-- 使用示例 -->
  <div id="usage-example">
    <h2>使用示例</h2>
    <p>以下是CaretBrowsingMode在PDF.js中的典型使用方式：</p>
    <pre><code class="language-javascript">
// 在PDFViewerApplication中初始化
this._caretBrowsing = new CaretBrowsingMode(
  this._globalAbortController.signal,
  this.appConfig.mainContainer,
  this.appConfig.viewerContainer,
  this.appConfig.toolbar?.container
);

// 处理键盘向上导航
document.addEventListener("keydown", function(event) {
  // 检查是否为向上箭头键
  if (event.key === "ArrowUp" && PDFViewerApplication.supportsCaretBrowsingMode) {
    // 是否按住Shift键决定是否扩展选择
    const select = event.shiftKey;
    // 移动插入符向上
    PDFViewerApplication.moveCaret(true, select);
    event.preventDefault();
  }
});

// 处理键盘向下导航
document.addEventListener("keydown", function(event) {
  // 检查是否为向下箭头键
  if (event.key === "ArrowDown" && PDFViewerApplication.supportsCaretBrowsingMode) {
    // 是否按住Shift键决定是否扩展选择
    const select = event.shiftKey;
    // 移动插入符向下
    PDFViewerApplication.moveCaret(false, select);
    event.preventDefault();
  }
});
    </code></pre>
  </div>

  <!-- 实现细节 -->
  <div id="implementation-details">
    <h2>实现细节</h2>
    <p>CaretBrowsingMode的实现采用了以下关键设计策略：</p>
    <ul>
      <li><strong>DOM遍历</strong>：使用TreeWalker API高效地遍历和搜索文档中的文本节点，避免了递归遍历DOM树的复杂性。</li>
      <li><strong>几何计算</strong>：通过边界矩形（BoundingClientRect）计算来精确确定文本元素的位置关系，准确判断元素是否在同一行。</li>
      <li><strong>浏览器兼容性处理</strong>：考虑了不同浏览器对文本定位API的支持差异，提供了兼容性解决方案。</li>
      <li><strong>跨页面导航</strong>：实现了跨PDF页面的插入符导航逻辑，使用页码属性在不同页面间平滑过渡。</li>
      <li><strong>可视区域监测</strong>：在导航到可视区域外的元素时，会自动滚动视图并在适当位置放置插入符。</li>
      <li><strong>元素堆叠处理</strong>：考虑了页面中元素堆叠可能导致的点击位置不准确问题，提供了临时隐藏遮挡元素的解决方案。</li>
      <li><strong>响应式布局支持</strong>：使用ResizeObserver监听工具栏大小变化，确保在界面布局调整时能正确计算可视区域。</li>
    </ul>
  </div>

  <!-- 注意事项 -->
  <div id="notes">
    <h2>注意事项</h2>
    <p>使用CaretBrowsingMode时，需要注意以下几点：</p>
    <ul>
      <li>插入符浏览模式要求PDF文档已正确加载文本层，对于没有文本层的PDF（如纯图像PDF或未经OCR处理的扫描文档），此功能不可用。</li>
      <li>插入符位置计算基于DOM元素的几何位置，在PDF缩放或旋转后可能需要重新计算位置。</li>
      <li>跨页面导航时，如果目标页面尚未渲染，可能会遇到导航失败的情况。</li>
      <li>CaretBrowsingMode应与键盘导航事件配合使用，通常通过应用程序层面的键盘事件处理器调用其moveCaret方法。</li>
      <li>在使用插入符浏览模式进行文本选择时，应确保Selection API可用，特别是在移动设备或某些嵌入式环境中。</li>
      <li>工具栏高度的变化会影响插入符的可视性判断，需要确保ResizeObserver正常工作。</li>
    </ul>
  </div>

  <!-- 返回顶部按钮 -->
  <button class="back-to-top">↑</button>

  <script>
    // 创建目录
    createTableOfContents();
  </script>
</body>
</html> 