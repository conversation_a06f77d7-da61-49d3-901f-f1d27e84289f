<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SignatureManager - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#constants">常量</a>
    <a href="#properties">属性</a>
    <a href="#methods">方法</a>
    <a href="#flowcharts">流程图</a>
    <a href="#examples">示例</a>
    <a href="#notes">注意事项</a>
  </div>

  <h1>SignatureManager 模块文档</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>SignatureManager 是 PDF.js 库中负责处理 PDF 文档中的数字签名的组件。它提供了验证签名、展示签名信息、管理签名状态以及创建新签名的功能。通过这个组件，用户可以检查文档的完整性和真实性，并在支持的情况下添加自己的数字签名。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 常量 -->
  <div id="constants">
    <h2>常量</h2>
    
    <p>SignatureManager 使用以下常量：</p>
    
    <ul>
      <li>
        <code>SignatureType</code>: 签名类型枚举
        <ul>
          <li><code>DIGITAL</code>: 数字签名</li>
          <li><code>ELECTRONIC</code>: 电子签名</li>
        </ul>
      </li>
      <li>
        <code>SignatureState</code>: 签名状态枚举
        <ul>
          <li><code>UNSIGNED</code>: 未签名</li>
          <li><code>SIGNED</code>: 已签名</li>
          <li><code>VALID</code>: 签名有效</li>
          <li><code>INVALID</code>: 签名无效</li>
          <li><code>VERIFICATION_FAILED</code>: 验证失败</li>
          <li><code>VERIFICATION_DISABLED</code>: 验证已禁用</li>
        </ul>
      </li>
      <li>
        <code>VerificationStatus</code>: 验证状态枚举
        <ul>
          <li><code>NOT_VERIFIED</code>: 未验证</li>
          <li><code>VERIFICATION_PENDING</code>: 验证进行中</li>
          <li><code>VERIFICATION_SUCCEEDED</code>: 验证成功</li>
          <li><code>VERIFICATION_FAILED</code>: 验证失败</li>
          <li><code>VERIFICATION_IMPOSSIBLE</code>: 无法验证</li>
        </ul>
      </li>
    </ul>
  </div>

  <!-- 属性 -->
  <div id="properties">
    <h2>属性</h2>
    
    <h3>公共属性</h3>
    <ul>
      <li><code>signatureCount</code>: 文档中的签名数量</li>
      <li><code>allSignatures</code>: 所有签名的数组</li>
      <li><code>verificationResults</code>: 签名验证结果</li>
      <li><code>isVerificationReady</code>: 验证是否就绪</li>
      <li><code>canCreateSignature</code>: 是否可以创建新签名</li>
    </ul>

    <h3>私有属性</h3>
    <ul>
      <li><code>#pdfDocument</code>: PDF 文档实例</li>
      <li><code>#eventBus</code>: 事件总线</li>
      <li><code>#l10n</code>: 本地化对象</li>
      <li><code>#verifier</code>: 签名验证器</li>
      <li><code>#signatureStorage</code>: 签名存储</li>
      <li><code>#signatureFields</code>: 签名字段集合</li>
      <li><code>#pendingVerifications</code>: 待处理的验证</li>
      <li><code>#verificationPromises</code>: 验证 Promise 集合</li>
      <li><code>#signatureCreator</code>: 签名创建器</li>
      <li><code>#overlay</code>: 覆盖层管理器</li>
      <li><code>#isVerificationReady</code>: 内部验证就绪标志</li>
      <li><code>#fieldToSignatureMap</code>: 字段到签名的映射</li>
      <li><code>#timeoutId</code>: 超时计时器 ID</li>
    </ul>
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法</h2>
    
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">构造函数</h3>
      </div>
      <div class="method-content">
        <p>创建一个新的 SignatureManager 实例。</p>
        <pre><code class="language-javascript">
constructor({
  pdfDocument,
  eventBus,
  l10n,
  verifier = null,
  signatureStorage = null,
  overlay = null,
  signatureCreator = null,
})
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>pdfDocument</code>: PDF 文档实例</li>
          <li><code>eventBus</code>: 事件总线</li>
          <li><code>l10n</code>: 本地化对象</li>
          <li><code>verifier</code>: 签名验证器 (可选，默认为 null)</li>
          <li><code>signatureStorage</code>: 签名存储 (可选，默认为 null)</li>
          <li><code>overlay</code>: 覆盖层管理器 (可选，默认为 null)</li>
          <li><code>signatureCreator</code>: 签名创建器 (可选，默认为 null)</li>
        </ul>
      </div>
    </div>

    <h3>核心方法</h3>
    <ul>
      <li><code>init()</code>: 初始化签名管理器</li>
      <li><code>async verifyAllSignatures()</code>: 验证所有签名</li>
      <li><code>async validateSignature(signatureField)</code>: 验证单个签名</li>
      <li><code>async getSignatureInfo(signatureField)</code>: 获取签名信息</li>
      <li><code>async showSignaturePanel(annotationId)</code>: 显示签名面板</li>
      <li><code>async createSignature(signatureField, annotationId)</code>: 创建签名</li>
      <li><code>async clearSignature(signatureField)</code>: 清除签名</li>
      <li><code>getSignatureById(annotationId)</code>: 通过 ID 获取签名</li>
      <li><code>getSignatureWidgetAnnotations()</code>: 获取签名小部件注释</li>
      <li><code>destroy()</code>: 销毁签名管理器</li>
    </ul>
    
    <h3>私有方法</h3>
    <ul>
      <li><code>#loadSignatureFields()</code>: 加载签名字段</li>
      <li><code>#getSignatureObject(signatureField)</code>: 获取签名对象</li>
      <li><code>#processSignatureField(field)</code>: 处理签名字段</li>
      <li><code>#verifySignature(signatureField)</code>: 验证签名</li>
      <li><code>#parseVerificationResult(result)</code>: 解析验证结果</li>
      <li><code>#updateVerificationStatus(signatureField, status)</code>: 更新验证状态</li>
      <li><code>#renderSignatureInfo(signatureInfo)</code>: 渲染签名信息</li>
      <li><code>#getFieldDetails(signatureField)</code>: 获取字段详情</li>
      <li><code>#handleSigningProcess(field, data)</code>: 处理签名过程</li>
      <li><code>#applySignature(field, signatureData)</code>: 应用签名</li>
      <li><code>#buildSignaturePanel(signatureField)</code>: 构建签名面板</li>
      <li><code>#updateSignaturePanel()</code>: 更新签名面板</li>
      <li><code>#closeSignaturePanel()</code>: 关闭签名面板</li>
      <li><code>#registerEvents()</code>: 注册事件</li>
      <li><code>#unregisterEvents()</code>: 取消注册事件</li>
    </ul>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <h3>签名验证流程</h3>
    <p>1. 初始化签名管理器<br>
    2. 加载文档中的签名字段<br>
    3. 对每个签名字段进行验证<br>
    4. 解析验证结果<br>
    5. 更新签名状态<br>
    6. 通知用户验证结果</p>
    
    <div class="mermaid">
      graph TD
          A[初始化签名管理器] --> B[调用 init 方法]
          B --> C[加载签名字段]
          C --> D[调用 verifyAllSignatures 方法]
          D --> E[遍历所有签名字段]
          E --> F[对每个字段调用 validateSignature]
          F --> G[验证器执行签名验证]
          G --> H[解析验证结果]
          H --> I{验证结果?}
          I -->|有效| J[更新状态为 VALID]
          I -->|无效| K[更新状态为 INVALID]
          I -->|失败| L[更新状态为 VERIFICATION_FAILED]
          J --> M[触发 signaturevalidated 事件]
          K --> M
          L --> M
    </div>
    
    <h3>签名创建流程</h3>
    <p>1. 用户选择签名字段<br>
    2. 显示签名面板<br>
    3. 用户输入签名信息<br>
    4. 生成签名数据<br>
    5. 应用签名到文档<br>
    6. 更新签名状态</p>
    
    <div class="mermaid">
      graph TD
          A[用户选择签名字段] --> B[调用 showSignaturePanel 方法]
          B --> C[构建签名面板]
          C --> D[显示签名面板]
          D --> E[用户输入签名信息]
          E --> F[用户确认签名]
          F --> G[调用 createSignature 方法]
          G --> H[签名创建器生成签名数据]
          H --> I[调用 #applySignature 方法]
          I --> J[更新文档中的签名]
          J --> K[更新签名状态]
          K --> L[关闭签名面板]
          L --> M[触发 signaturecreated 事件]
    </div>
    
    <h3>签名信息展示流程</h3>
    <p>1. 用户点击已有签名<br>
    2. 获取签名信息<br>
    3. 构建信息面板<br>
    4. 显示签名详细信息<br>
    5. 根据验证状态显示相应指示器</p>
    
    <div class="mermaid">
      graph TD
          A[用户点击已有签名] --> B[调用 getSignatureInfo 方法]
          B --> C[获取签名对象]
          C --> D[解析签名元数据]
          D --> E[检索签名人信息]
          E --> F[获取签名时间]
          F --> G[检查验证状态]
          G --> H[调用 #renderSignatureInfo 方法]
          H --> I[构建信息面板]
          I --> J[显示签名详细信息]
          J --> K{验证状态?}
          K -->|有效| L[显示有效指示器]
          K -->|无效| M[显示无效指示器]
          K -->|未验证| N[显示未验证指示器]
    </div>
  </div>

  <!-- 示例 -->
  <div id="examples">
    <h2>使用示例</h2>
    
    <h3>基本用法</h3>
    <pre><code class="language-javascript">
// 创建 SignatureManager 实例
const signatureManager = new SignatureManager({
  pdfDocument: pdfDocument,
  eventBus: eventBus,
  l10n: l10n,
  // 使用默认验证器
  verifier: new DefaultSignatureVerifier(),
  // 使用覆盖层管理器显示签名面板
  overlay: overlayManager
});

// 初始化签名管理器
signatureManager.init().then(() => {
  console.log(`文档中包含 ${signatureManager.signatureCount} 个签名`);
  
  // 验证所有签名
  signatureManager.verifyAllSignatures().then(() => {
    console.log('所有签名验证完成');
    
    // 查看验证结果
    const results = signatureManager.verificationResults;
    for (const [signatureId, result] of Object.entries(results)) {
      console.log(`签名 ${signatureId} 验证结果:`, result);
    }
  });
});

// 监听签名验证事件
eventBus.on('signaturevalidated', function(event) {
  const { signatureField, status, result } = event.detail;
  console.log(`签名 ${signatureField.id} 验证状态: ${status}`);
  
  // 更新 UI 显示验证结果
  updateSignatureUI(signatureField.id, status, result);
});
    </code></pre>
    
    <h3>显示签名信息</h3>
    <pre><code class="language-javascript">
// 添加签名点击事件处理
function setupSignatureClickHandlers() {
  // 获取所有签名小部件注释
  const signatureAnnotations = signatureManager.getSignatureWidgetAnnotations();
  
  // 为每个签名注释添加点击处理
  signatureAnnotations.forEach(annotation => {
    // 使用注释 ID 作为选择器
    const element = document.querySelector(`[data-annotation-id="${annotation.id}"]`);
    if (element) {
      element.addEventListener('click', () => {
        // 显示签名面板
        signatureManager.showSignaturePanel(annotation.id);
      });
    }
  });
}

// 显示签名信息
async function showSignatureDetails(signatureId) {
  const signature = signatureManager.getSignatureById(signatureId);
  if (!signature) {
    console.error('未找到签名:', signatureId);
    return;
  }
  
  // 获取签名信息
  const signatureInfo = await signatureManager.getSignatureInfo(signature);
  
  // 创建信息面板
  const infoPanel = document.createElement('div');
  infoPanel.className = 'signature-info-panel';
  
  // 添加签名信息
  infoPanel.innerHTML = `
    <h3>签名信息</h3>
    <p><strong>签名人:</strong> ${signatureInfo.signer || '未知'}</p>
    <p><strong>签名日期:</strong> ${formatDate(signatureInfo.signDate)}</p>
    <p><strong>原因:</strong> ${signatureInfo.reason || '无'}</p>
    <p><strong>位置:</strong> ${signatureInfo.location || '无'}</p>
    <p><strong>状态:</strong> <span class="status-${signatureInfo.status.toLowerCase()}">${getStatusText(signatureInfo.status)}</span></p>
  `;
  
  // 显示面板
  const container = document.getElementById('signatureInfoContainer');
  container.innerHTML = '';
  container.appendChild(infoPanel);
  container.style.display = 'block';
}

// 格式化日期
function formatDate(dateString) {
  if (!dateString) return '未知';
  
  try {
    const date = new Date(dateString);
    return date.toLocaleString();
  } catch (e) {
    return dateString;
  }
}

// 获取状态文本
function getStatusText(status) {
  const statusMap = {
    [SignatureState.VALID]: '有效',
    [SignatureState.INVALID]: '无效',
    [SignatureState.VERIFICATION_FAILED]: '验证失败',
    [SignatureState.VERIFICATION_DISABLED]: '验证已禁用',
    [SignatureState.SIGNED]: '已签名',
    [SignatureState.UNSIGNED]: '未签名'
  };
  
  return statusMap[status] || '未知';
}
    </code></pre>
    
    <h3>创建新签名</h3>
    <pre><code class="language-javascript">
// 自定义签名创建器
class CustomSignatureCreator {
  constructor() {
    this.signatureData = null;
  }
  
  // 创建签名面板
  createSignaturePanel(container, signatureField, onSign) {
    // 创建签名输入界面
    const panel = document.createElement('div');
    panel.className = 'signature-creation-panel';
    
    panel.innerHTML = `
      <h3>创建数字签名</h3>
      <div class="form-group">
        <label for="signerName">签名人:</label>
        <input type="text" id="signerName" required>
      </div>
      <div class="form-group">
        <label for="signReason">原因:</label>
        <input type="text" id="signReason">
      </div>
      <div class="form-group">
        <label for="signLocation">位置:</label>
        <input type="text" id="signLocation">
      </div>
      <div class="signature-area">
        <canvas id="signatureCanvas" width="400" height="200"></canvas>
        <button id="clearCanvas">清除</button>
      </div>
      <div class="button-group">
        <button id="cancelSign">取消</button>
        <button id="confirmSign">确认签名</button>
      </div>
    `;
    
    container.appendChild(panel);
    
    // 设置签名画布
    const canvas = document.getElementById('signatureCanvas');
    const ctx = canvas.getContext('2d');
    let isDrawing = false;
    
    // 画布事件处理
    canvas.addEventListener('mousedown', startDrawing);
    canvas.addEventListener('mousemove', draw);
    canvas.addEventListener('mouseup', stopDrawing);
    canvas.addEventListener('mouseout', stopDrawing);
    
    function startDrawing(e) {
      isDrawing = true;
      draw(e);
    }
    
    function draw(e) {
      if (!isDrawing) return;
      
      ctx.lineWidth = 2;
      ctx.lineCap = 'round';
      ctx.strokeStyle = '#000';
      
      ctx.lineTo(e.offsetX, e.offsetY);
      ctx.stroke();
      ctx.beginPath();
      ctx.moveTo(e.offsetX, e.offsetY);
    }
    
    function stopDrawing() {
      isDrawing = false;
      ctx.beginPath();
    }
    
    // 清除画布
    document.getElementById('clearCanvas').addEventListener('click', () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
    });
    
    // 取消签名
    document.getElementById('cancelSign').addEventListener('click', () => {
      container.innerHTML = '';
    });
    
    // 确认签名
    document.getElementById('confirmSign').addEventListener('click', () => {
      const signerName = document.getElementById('signerName').value;
      if (!signerName) {
        alert('请输入签名人');
        return;
      }
      
      // 获取签名图像
      const signatureImage = canvas.toDataURL('image/png');
      
      // 创建签名数据
      const signatureData = {
        signer: signerName,
        reason: document.getElementById('signReason').value,
        location: document.getElementById('signLocation').value,
        signDate: new Date().toISOString(),
        image: signatureImage,
        type: SignatureType.ELECTRONIC  // 电子签名
      };
      
      // 回调签名数据
      onSign(signatureData);
    });
  }
  
  // 创建签名数据
  async createSignature(signatureField, options = {}) {
    return new Promise((resolve, reject) => {
      const container = document.getElementById('signatureCreationContainer');
      container.innerHTML = '';
      
      this.createSignaturePanel(container, signatureField, (signatureData) => {
        container.innerHTML = '';
        resolve(signatureData);
      });
    });
  }
}

// 创建带自定义签名创建器的签名管理器
const customSignatureCreator = new CustomSignatureCreator();
const signatureManager = new SignatureManager({
  pdfDocument: pdfDocument,
  eventBus: eventBus,
  l10n: l10n,
  verifier: new DefaultSignatureVerifier(),
  overlay: overlayManager,
  signatureCreator: customSignatureCreator
});

// 初始化签名管理器
await signatureManager.init();

// 为空白签名字段添加签名功能
function setupSigningUI() {
  const unsignedFields = signatureManager.allSignatures.filter(
    field => field.state === SignatureState.UNSIGNED
  );
  
  unsignedFields.forEach(field => {
    const annotation = field.widgetAnnotation;
    if (annotation) {
      const element = document.querySelector(`[data-annotation-id="${annotation.id}"]`);
      if (element) {
        element.addEventListener('click', () => {
          // 创建签名
          signatureManager.createSignature(field, annotation.id)
            .then(() => {
              console.log('签名已创建');
            })
            .catch(error => {
              console.error('签名创建失败:', error);
            });
        });
        
        // 添加视觉提示
        element.classList.add('unsigned-signature-field');
        element.title = '点击以添加签名';
      }
    }
  });
}

// 监听签名创建事件
eventBus.on('signaturecreated', function(event) {
  const { signatureField } = event.detail;
  console.log(`签名已创建在字段: ${signatureField.id}`);
  
  // 刷新 UI
  setupSigningUI();
});
    </code></pre>
  </div>

  <!-- 注意事项 -->
  <div id="notes">
    <h2>注意事项</h2>
    
    <ul>
      <li>数字签名验证通常依赖于证书和密钥基础设施。要正确验证签名，需要确保验证器能够访问必要的证书链和吊销列表。</li>
      <li>PDF.js 的签名功能主要支持签名验证，而对签名创建的支持可能有限。在实际应用中，可能需要实现自定义的签名创建器以满足特定的签名要求。</li>
      <li>电子签名（如图像签名）和数字签名（基于密码学）之间有明显区别。电子签名提供的安全性和法律效力通常不如数字签名。</li>
      <li>签名验证可能涉及网络请求（如证书验证和时间戳验证），这可能会影响性能并引入依赖外部服务的风险。</li>
      <li>不同的 PDF 签名格式（如 PKCS#7、PAdES 等）可能需要不同的验证方法。确保验证器支持文档中使用的签名格式。</li>
      <li>在处理签名时，应考虑用户的隐私和数据安全。避免将敏感信息（如私钥）不安全地存储或传输。</li>
      <li>PDF 规范允许多种类型的签名字段和布局。签名管理器应能够适应不同的签名字段配置。</li>
      <li>签名验证状态的展示应清晰明了，以帮助用户理解文档的真实性和完整性状态。考虑使用颜色代码和图标来表示不同的验证状态。</li>
    </ul>
  </div>

  <script>
    // 在页面加载完成后初始化 Mermaid
    document.addEventListener('DOMContentLoaded', function() {
      mermaid.initialize({ startOnLoad: true });
      
      // 生成目录
      generateTOC();
    });
  </script>
</body>
</html>
 