<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ProgressBar - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#properties">属性</a>
    <a href="#methods">方法</a>
    <a href="#flowcharts">流程图</a>
    <a href="#examples">示例</a>
    <a href="#notes">注意事项</a>
  </div>

  <h1>ProgressBar 模块文档</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>ProgressBar 是 PDF.js 库中的进度条组件，用于显示文档加载、渲染和其他操作的进度。它提供了一种直观的方式向用户展示当前任务的完成情况，支持确定性进度（百分比）和不确定性进度（加载中状态）两种模式。该组件在 PDF 查看器中主要用于文档加载过程中的进度指示。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 属性 -->
  <div id="properties">
    <h2>属性</h2>
    
    <h3>公共属性</h3>
    <ul>
      <li><code>percent</code>: 获取或设置进度条的百分比值（0-100）</li>
    </ul>

    <h3>私有属性</h3>
    <ul>
      <li><code>#classList</code>: 进度条元素的类名列表</li>
      <li><code>#style</code>: 进度条元素的样式对象</li>
      <li><code>#percent</code>: 内部存储的进度百分比值</li>
      <li><code>#visible</code>: 进度条是否可见的标志</li>
      <li><code>#disableAutoFetchTimeout</code>: 禁用自动获取的超时定时器 ID</li>
    </ul>
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法</h2>
    
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">构造函数</h3>
      </div>
      <div class="method-content">
        <p>创建一个新的 ProgressBar 实例。</p>
        <pre><code class="language-javascript">
constructor(bar) {
  this.#classList = bar.classList;
  this.#style = bar.style;
}
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>bar</code>: 进度条的 DOM 元素</li>
        </ul>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">get percent()</h3>
      </div>
      <div class="method-content">
        <p>获取当前进度百分比。</p>
        <pre><code class="language-javascript">
get percent() {
  return this.#percent;
}
        </code></pre>
        <p><strong>返回值:</strong></p>
        <ul>
          <li>当前进度百分比值（0-100）</li>
        </ul>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">set percent(val)</h3>
      </div>
      <div class="method-content">
        <p>设置进度百分比并更新显示。</p>
        <pre><code class="language-javascript">
set percent(val) {
  this.#percent = MathClamp(val, 0, 100);
  if (isNaN(val)) {
    this.#classList.add("indeterminate");
    return;
  }
  this.#classList.remove("indeterminate");
  this.#style.setProperty("--progressBar-percent", `${this.#percent}%`);
}
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>val</code>: 要设置的进度值（0-100）。如果传入 NaN，将显示不确定状态的进度条</li>
        </ul>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">setWidth(viewer)</h3>
      </div>
      <div class="method-content">
        <p>根据查看器元素设置进度条的宽度，考虑滚动条宽度。</p>
        <pre><code class="language-javascript">
setWidth(viewer) {
  if (!viewer) {
    return;
  }
  const container = viewer.parentNode;
  const scrollbarWidth = container.offsetWidth - viewer.offsetWidth;
  if (scrollbarWidth > 0) {
    this.#style.setProperty("--progressBar-end-offset", `${scrollbarWidth}px`);
  }
}
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>viewer</code>: PDF 查看器元素</li>
        </ul>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">setDisableAutoFetch(delay = 5000)</h3>
      </div>
      <div class="method-content">
        <p>显示进度条一段时间后自动隐藏，通常用于禁用自动获取功能时提供视觉反馈。</p>
        <pre><code class="language-javascript">
setDisableAutoFetch(delay = 5000) {
  if (this.#percent === 100 || isNaN(this.#percent)) {
    return;
  }
  if (this.#disableAutoFetchTimeout) {
    clearTimeout(this.#disableAutoFetchTimeout);
  }
  this.show();
  this.#disableAutoFetchTimeout = setTimeout(() => {
    this.#disableAutoFetchTimeout = null;
    this.hide();
  }, delay);
}
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>delay</code>: 自动隐藏的延迟时间（毫秒），默认为 5000 毫秒（5 秒）</li>
        </ul>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">hide()</h3>
      </div>
      <div class="method-content">
        <p>隐藏进度条。</p>
        <pre><code class="language-javascript">
hide() {
  if (!this.#visible) {
    return;
  }
  this.#visible = false;
  this.#classList.add("hidden");
}
        </code></pre>
      </div>
    </div>

    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">show()</h3>
      </div>
      <div class="method-content">
        <p>显示进度条。</p>
        <pre><code class="language-javascript">
show() {
  if (this.#visible) {
    return;
  }
  this.#visible = true;
  this.#classList.remove("hidden");
}
        </code></pre>
      </div>
    </div>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <h3>进度条更新流程</h3>
    <p>1. 设置进度值<br>
    2. 处理确定和不确定状态<br>
    3. 更新视觉显示</p>
    
    <div class="mermaid">
      graph TD
        A[设置进度值] --> B{是否为 NaN?}
        B -->|是| C[添加不确定状态类]
        B -->|否| D[移除不确定状态类]
        D --> E[计算限制在0-100范围内]
        E --> F[设置 CSS 变量更新显示]
        C --> G[完成更新]
        F --> G
    </div>
    
    <h3>自动隐藏流程</h3>
    <p>1. 调用 setDisableAutoFetch<br>
    2. 检查条件<br>
    3. 设置计时器<br>
    4. 自动隐藏</p>
    
    <div class="mermaid">
      graph TD
        A[调用 setDisableAutoFetch] --> B{进度是否为100%<br>或不是数字?}
        B -->|是| C[直接返回]
        B -->|否| D{是否已有超时?}
        D -->|是| E[清除现有超时]
        D -->|否| F[显示进度条]
        E --> F
        F --> G[设置新超时]
        G --> H[等待指定延迟]
        H --> I[超时触发]
        I --> J[清空超时ID]
        J --> K[隐藏进度条]
    </div>
    
    <h3>宽度调整流程</h3>
    <p>1. 调用 setWidth<br>
    2. 计算滚动条宽度<br>
    3. 设置偏移量</p>
    
    <div class="mermaid">
      graph TD
        A[调用 setWidth] --> B{是否提供查看器元素?}
        B -->|否| C[直接返回]
        B -->|是| D[获取容器元素]
        D --> E[计算滚动条宽度]
        E --> F{滚动条宽度 > 0?}
        F -->|是| G[设置进度条结束偏移 CSS 变量]
        F -->|否| H[使用默认值]
    </div>
  </div>

  <!-- 示例 -->
  <div id="examples">
    <h2>使用示例</h2>
    
    <h3>基本用法</h3>
    <pre><code class="language-javascript">
// HTML 结构:
// <div id="loadingBar" class="hidden">
//   <div class="progress"></div>
// </div>

// 创建进度条实例
const progressBarElement = document.getElementById("loadingBar");
const progressBar = new ProgressBar(progressBarElement);

// 显示进度条
progressBar.show();

// 设置确定性进度
progressBar.percent = 30; // 设置为 30%

// 稍后更新进度
setTimeout(() => {
  progressBar.percent = 60; // 更新为 60%
}, 1000);

// 完成任务后隐藏进度条
setTimeout(() => {
  progressBar.percent = 100; // 设置为 100%
  
  // 短暂显示完成状态后隐藏
  setTimeout(() => {
    progressBar.hide();
  }, 500);
}, 2000);
    </code></pre>
    
    <h3>不确定状态进度</h3>
    <pre><code class="language-javascript">
// 获取进度条元素并创建实例
const progressBarElement = document.getElementById("loadingBar");
const progressBar = new ProgressBar(progressBarElement);

// 显示不确定状态的进度条（例如在开始加载但不知道总大小时）
progressBar.show();
progressBar.percent = NaN; // 设置为不确定状态

// 当知道确切进度时，更新为确定状态
function updateWithProgress() {
  progressBar.percent = 25; // 切换到确定性进度
  
  // 模拟后续进度更新
  const interval = setInterval(() => {
    // 获取当前进度
    const currentPercent = progressBar.percent;
    
    // 检查是否完成
    if (currentPercent >= 100) {
      clearInterval(interval);
      setTimeout(() => progressBar.hide(), 500);
      return;
    }
    
    // 增加进度
    progressBar.percent = currentPercent + 5;
  }, 200);
}

// 模拟延迟后获得进度信息
setTimeout(updateWithProgress, 2000);
    </code></pre>
    
    <h3>与 PDF 查看器集成</h3>
    <pre><code class="language-javascript">
class PDFViewerApplication {
  constructor() {
    // 其他初始化...
    
    // 懒加载创建进度条
    this._loadingBar = null;
  }
  
  // 使用 getter 延迟创建进度条
  get loadingBar() {
    if (!this._loadingBar) {
      const barElement = document.getElementById("loadingBar");
      this._loadingBar = barElement ? new ProgressBar(barElement) : null;
    }
    return this._loadingBar;
  }
  
  // 加载文档方法
  async loadDocument(source) {
    // 显示进度条
    this.loadingBar.show();
    
    try {
      // 设置初始进度为不确定状态
      this.loadingBar.percent = NaN;
      
      // 开始加载文档
      const loadingTask = pdfjsLib.getDocument(source);
      
      // 注册进度回调
      loadingTask.onProgress = (progressData) => {
        if (progressData.total > 0) {
          // 如果知道总大小，设置确定性进度
          const percent = (progressData.loaded / progressData.total) * 100;
          this.loadingBar.percent = percent;
        }
      };
      
      // 等待文档加载完成
      const pdfDocument = await loadingTask.promise;
      
      // 设置进度为 100%
      this.loadingBar.percent = 100;
      
      // 处理加载的文档...
      
      // 短暂延迟后隐藏进度条
      setTimeout(() => {
        this.loadingBar.hide();
      }, 500);
      
      return pdfDocument;
    } catch (error) {
      // 出错时隐藏进度条
      this.loadingBar.hide();
      throw error;
    }
  }
  
  // 调整界面大小时设置进度条宽度
  resizeViewer() {
    const viewer = document.getElementById("viewer");
    this.loadingBar.setWidth(viewer);
    
    // 其他调整大小逻辑...
  }
  
  // 禁用自动获取时的视觉反馈
  disableAutoFetch() {
    // 显示进度条并设置自动隐藏
    this.loadingBar.setDisableAutoFetch();
    
    // 其他禁用逻辑...
  }
}

// 使用示例
const app = new PDFViewerApplication();
app.loadDocument("document.pdf");
    </code></pre>
  </div>

  <!-- 注意事项 -->
  <div id="notes">
    <h2>注意事项</h2>
    
    <ul>
      <li>ProgressBar 组件依赖于特定的 HTML/CSS 结构，要求有带有 "progress" 类的子元素来显示进度。</li>
      <li>组件使用 CSS 变量 <code>--progressBar-percent</code> 和 <code>--progressBar-end-offset</code> 来控制进度显示和宽度。</li>
      <li>设置 <code>percent = NaN</code> 会触发不确定状态的进度条，通常用于不知道总量的加载过程。</li>
      <li>当设置 <code>percent = 100</code> 时，进度条完全填充，表示任务完成。</li>
      <li>通常在任务完成后应调用 <code>hide()</code> 方法隐藏进度条，而不是仅将其进度设置为 100%。</li>
      <li><code>setDisableAutoFetch()</code> 方法适用于需要临时显示视觉反馈后自动隐藏的场景。</li>
      <li><code>setWidth()</code> 方法用于适应不同视图大小，特别是考虑滚动条宽度对进度条的影响。</li>
      <li>组件使用私有字段（以 # 开头），需要现代浏览器支持。</li>
      <li>进度条状态变化（显示/隐藏、进度更新）会触发重排和重绘，频繁更新可能影响性能。</li>
    </ul>
  </div>

  <script>
    // 在页面加载完成后初始化 Mermaid
    document.addEventListener('DOMContentLoaded', function() {
      mermaid.initialize({ startOnLoad: true });
      
      // 生成目录
      createTableOfContents();
    });
  </script>
</body>
</html> 