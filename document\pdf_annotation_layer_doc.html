<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PDFAnnotationLayer - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#constants">常量</a>
    <a href="#properties">属性</a>
    <a href="#methods">方法</a>
    <a href="#flowcharts">流程图</a>
    <a href="#examples">示例</a>
    <a href="#notes">注意事项</a>
  </div>

  <h1>PDFAnnotationLayer 模块文档</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>PDFAnnotationLayer 是 PDF.js 库中负责处理文档注释显示和交互的组件。它将 PDF 文档中的各种注释（如链接、文本注释、高亮、下划线等）渲染为 HTML 元素，并处理相关的用户交互。注释层是 PDF 交互功能的重要组成部分，使文档能够包含丰富的互动元素。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 常量 -->
  <div id="constants">
    <h2>常量</h2>
    
    <p>PDFAnnotationLayer 使用以下常量：</p>
    
    <ul>
      <li>
        <code>AnnotationCategorySet</code>: 注释类别集合，例如：
        <ul>
          <li><code>LINK</code>: 链接注释</li>
          <li><code>TEXT</code>: 文本注释</li>
          <li><code>WIDGET</code>: 表单部件注释</li>
          <li><code>HIGHLIGHT</code>: 高亮注释</li>
          <li><code>POPUP</code>: 弹出注释</li>
        </ul>
      </li>
      <li>
        <code>AnnotationEditorType</code>: 注释编辑器类型枚举
        <ul>
          <li><code>DISABLE</code>: 禁用编辑器</li>
          <li><code>NONE</code>: 无编辑器</li>
          <li><code>FREETEXT</code>: 自由文本编辑器</li>
          <li><code>INK</code>: 墨迹编辑器</li>
          <li><code>STAMP</code>: 图章编辑器</li>
        </ul>
      </li>
      <li>
        <code>AnnotationMode</code>: 注释模式枚举
        <ul>
          <li><code>DISABLE</code>: 禁用注释</li>
          <li><code>ENABLE</code>: 启用注释</li>
          <li><code>ENABLE_FORMS</code>: 启用表单注释</li>
          <li><code>ENABLE_STORAGE</code>: 启用注释存储</li>
        </ul>
      </li>
      <li><code>DEFAULT_ICON_SIZE</code>: 默认注释图标尺寸</li>
      <li><code>DEFAULT_FONT_SIZE</code>: 默认注释文本字体大小</li>
    </ul>
  </div>

  <!-- 属性 -->
  <div id="properties">
    <h2>属性</h2>
    
    <h3>公共属性</h3>
    <ul>
      <li><code>div</code>: 注释层容器元素</li>
      <li><code>annotationCanvasMap</code>: 注释画布映射</li>
      <li><code>page</code>: 所属 PDF 页面</li>
      <li><code>viewport</code>: 视口对象</li>
      <li><code>linkService</code>: 链接服务</li>
      <li><code>downloadManager</code>: 下载管理器</li>
      <li><code>annotationStorage</code>: 注释存储</li>
      <li><code>imageResourcesPath</code>: 图像资源路径</li>
      <li><code>renderForms</code>: 是否渲染表单</li>
      <li><code>l10n</code>: 本地化对象</li>
      <li><code>enableScripting</code>: 是否启用脚本</li>
      <li><code>hasJSActions</code>: 是否有 JavaScript 动作</li>
    </ul>

    <h3>私有属性</h3>
    <ul>
      <li><code>#annotationLayer</code>: 注释层对象</li>
      <li><code>#accessibilityManager</code>: 辅助功能管理器</li>
      <li><code>#annotationCanvasMap</code>: 注释画布映射内部表示</li>
      <li><code>#annotationEditorLayerPromise</code>: 注释编辑器层Promise</li>
      <li><code>#annotationEditorLayer</code>: 注释编辑器层</li>
      <li><code>#textHighlighter</code>: 文本高亮器</li>
    </ul>
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法</h2>
    
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">构造函数</h3>
      </div>
      <div class="method-content">
        <p>创建一个新的 PDFAnnotationLayer 实例。</p>
        <pre><code class="language-javascript">
constructor({
  div,
  page,
  viewport,
  linkService,
  downloadManager,
  annotationStorage = null,
  imageResourcesPath = "",
  renderForms = true,
  l10n = null,
  enableScripting = false,
  hasJSActions = false,
  accessibilityManager = null,
  annotationCanvasMap = null,
  filterFactory = null,
})
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>div</code>: 注释层容器元素</li>
          <li><code>page</code>: PDF 页面对象</li>
          <li><code>viewport</code>: 视口对象</li>
          <li><code>linkService</code>: 链接服务</li>
          <li><code>downloadManager</code>: 下载管理器</li>
          <li><code>annotationStorage</code>: 注释存储 (可选)</li>
          <li><code>imageResourcesPath</code>: 图像资源路径 (可选)</li>
          <li><code>renderForms</code>: 是否渲染表单 (可选，默认为 true)</li>
          <li><code>l10n</code>: 本地化对象 (可选)</li>
          <li><code>enableScripting</code>: 是否启用脚本 (可选，默认为 false)</li>
          <li><code>hasJSActions</code>: 是否有 JavaScript 动作 (可选，默认为 false)</li>
          <li><code>accessibilityManager</code>: 辅助功能管理器 (可选)</li>
          <li><code>annotationCanvasMap</code>: 注释画布映射 (可选)</li>
          <li><code>filterFactory</code>: 过滤器工厂 (可选)</li>
        </ul>
      </div>
    </div>

    <h3>核心方法</h3>
    <ul>
      <li><code>render(parameters)</code>: 渲染注释层</li>
      <li><code>update(parameters)</code>: 更新注释层</li>
      <li><code>cancel()</code>: 取消正在进行的渲染操作</li>
      <li><code>hide()</code>: 隐藏注释层</li>
      <li><code>show()</code>: 显示注释层</li>
      <li><code>destroy()</code>: 销毁注释层并释放资源</li>
      <li><code>setTextHighlighter(highlighter)</code>: 设置文本高亮器</li>
      <li><code>setAnnotationEditorLayer(layer)</code>: 设置注释编辑器层</li>
      <li><code>setAccessibilityManager(manager)</code>: 设置辅助功能管理器</li>
    </ul>
    
    <h3>私有方法</h3>
    <ul>
      <li><code>#createAnnotationElement(data)</code>: 创建注释元素</li>
      <li><code>#setupAnnotations(annotations)</code>: 设置注释</li>
      <li><code>#getAnnotationElement(data)</code>: 获取注释元素</li>
      <li><code>#appendElement(element, id)</code>: 添加元素到注释层</li>
      <li><code>#bindMouse()</code>: 绑定鼠标事件处理</li>
      <li><code>#renderAnnotation(parameters)</code>: 渲染单个注释</li>
      <li><code>#renderFreeText(element, annotation)</code>: 渲染自由文本注释</li>
      <li><code>#renderPopup(element, annotation)</code>: 渲染弹出注释</li>
      <li><code>#renderHighlight(element, annotation)</code>: 渲染高亮注释</li>
      <li><code>#renderUnderline(element, annotation)</code>: 渲染下划线注释</li>
      <li><code>#renderStrikeOut(element, annotation)</code>: 渲染删除线注释</li>
      <li><code>#renderFileAttachment(element, annotation)</code>: 渲染文件附件注释</li>
    </ul>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <h3>注释层初始化与渲染流程</h3>
    <p>1. 创建 PDFAnnotationLayer 实例<br>
    2. 调用 <code>render()</code> 方法开始渲染<br>
    3. 从 PDF 页面获取注释数据<br>
    4. 处理和转换注释数据<br>
    5. 为每种类型的注释创建 HTML 元素<br>
    6. 将注释元素添加到容器中<br>
    7. 绑定注释交互事件</p>
    
    <div class="mermaid">
      graph TD
          A[创建 PDFAnnotationLayer 实例] --> B[调用 render 方法]
          B --> C[从 PDF 页面获取注释数据]
          C --> D[分析注释类型和属性]
          D --> E[创建注释 HTML 元素]
          E --> F[应用样式和定位]
          F --> G[添加到注释层容器]
          G --> H[绑定交互事件]
    </div>
    
    <h3>注释交互处理流程</h3>
    <p>1. 用户与注释元素交互（点击、悬停等）<br>
    2. 触发相应的事件处理器<br>
    3. 根据注释类型执行相应操作<br>
    4. 链接注释导航到目标位置<br>
    5. 弹出注释显示注释内容<br>
    6. 表单注释触发表单交互</p>
    
    <div class="mermaid">
      graph TD
          A[用户与注释交互] --> B{注释类型?}
          B -->|链接| C[使用 linkService 导航]
          B -->|弹出注释| D[显示/隐藏注释内容]
          B -->|表单部件| E[激活表单交互]
          B -->|文件附件| F[下载或显示附件]
          B -->|文本注释| G[显示注释文本]
    </div>
    
    <h3>注释更新流程</h3>
    <p>1. 页面视图变化（如缩放、旋转）<br>
    2. 调用 <code>update()</code> 方法<br>
    3. 更新注释层视口<br>
    4. 重新定位和缩放注释元素<br>
    5. 应用更新后的样式</p>
    
    <div class="mermaid">
      graph TD
          A[页面视图变化] --> B[调用 update 方法]
          B --> C[更新注释层视口]
          C --> D[重新计算注释位置和尺寸]
          D --> E[应用更新后的样式]
    </div>
  </div>

  <!-- 示例 -->
  <div id="examples">
    <h2>使用示例</h2>
    
    <h3>基本用法</h3>
    <pre><code class="language-javascript">
// 创建 PDFAnnotationLayer 实例
const annotationLayer = new PDFAnnotationLayer({
  div: document.getElementById('annotationLayerDiv'),
  page: pdfPage,
  viewport: viewport,
  linkService: pdfLinkService,
  downloadManager: downloadManager,
  renderForms: true
});

// 渲染注释层
annotationLayer.render({
  intent: 'display'
}).then(() => {
  console.log('注释层渲染完成');
});
    </code></pre>
    
    <h3>更新注释层</h3>
    <pre><code class="language-javascript">
// 当页面缩放或旋转时更新注释层
function onViewportChange(newViewport) {
  // 更新注释层
  annotationLayer.update({
    viewport: newViewport
  });
}

// 绑定到缩放事件
zoomButton.addEventListener('click', function() {
  const newScale = currentScale * 1.5;
  const newViewport = page.getViewport({ scale: newScale });
  onViewportChange(newViewport);
});
    </code></pre>
    
    <h3>与表单集成</h3>
    <pre><code class="language-javascript">
// 创建带有表单支持的注释层
const annotationLayer = new PDFAnnotationLayer({
  div: document.getElementById('annotationLayerDiv'),
  page: pdfPage,
  viewport: viewport,
  linkService: pdfLinkService,
  downloadManager: downloadManager,
  renderForms: true,
  annotationStorage: pdfDocument.annotationStorage,
  enableScripting: true,
  hasJSActions: pdfDocument.hasJSActions
});

// 监听表单字段变化
eventBus.on("dispatcheventinsandbox", function(event) {
  console.log("表单字段变化:", event.detail);
});
    </code></pre>
  </div>

  <!-- 注意事项 -->
  <div id="notes">
    <h2>注意事项</h2>
    
    <ul>
      <li>注释层的正常工作依赖于正确配置的 linkService、downloadManager 和其他服务组件。</li>
      <li>注释层的渲染会根据注释的类型和数量影响性能，特别是在包含大量复杂注释的文档中。</li>
      <li>表单注释（如文本字段、复选框、单选按钮等）需要启用 renderForms 选项才能正常显示和交互。</li>
      <li>脚本化注释（包含 JavaScript 动作的注释）需要启用 enableScripting 选项，并且可能存在安全风险。</li>
      <li>注释的外观可能受到 PDF 文档本身设置的影响，有些特殊样式可能无法完全精确再现。</li>
      <li>注释层需要与其他层（如文本层、XFA 层等）协调工作，以确保正确的叠加顺序和交互行为。</li>
      <li>某些注释类型（如 3D 模型、音频/视频等）可能需要额外的插件或功能支持才能正确显示。</li>
      <li>在高 DPI 显示器上，注释的缩放和定位可能需要特殊处理以保持清晰度和准确性。</li>
    </ul>
  </div>

  <script>
    // 在页面加载完成后初始化 Mermaid
    document.addEventListener('DOMContentLoaded', function() {
      mermaid.initialize({ startOnLoad: true });
      
      // 生成目录
      generateTOC();
    });
  </script>
</body>
</html> 