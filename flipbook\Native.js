/**
 * Native.js - 原生JavaScript翻页效果库
 * 基于原jQuery版本重构，不依赖任何第三方库
 * 
 * 功能：
 * - 翻页效果 (Turn)
 * - 页面折叠效果 (Flip)
 * - 支持触摸和鼠标事件
 * - 支持单页和双页显示模式
 * - 支持渐变效果和硬件加速
 */

(function() {
    'use strict';
    
    // 全局变量定义
    var has3d,
        vendor = '',
        PI = Math.PI,
        A90 = PI/2,
        isTouch = 'ontouchstart' in window,
        
        // 事件类型映射
        events = isTouch ? 
            {start: 'touchstart', move: 'touchmove', end: 'touchend'} :
            {start: 'mousedown', move: 'mousemove', end: 'mouseup'},
        
        // 角落常量定义
        // tl * tr
        // *     *
        // bl * br
        corners = {
            backward: ['bl', 'tl'],
            forward: ['br', 'tr'],
            all: ['tl', 'bl', 'tr', 'br']
        },
        
        // 显示模式
        displays = ['single', 'double'],
        
        // Turn 默认选项
        turnOptions = {
            page: 1,                // 首页
            gradients: true,        // 启用渐变
            duration: 600,          // 动画持续时间（毫秒）
            acceleration: true,     // 启用硬件加速
            display: 'double',      // 显示模式
            when: null              // 事件回调
        },
        
        // Flip 默认选项
        flipOptions = {
            folding: null,          // 背面页面
            corners: 'forward',     // 激活的角落
            cornerSize: 100,        // 角落活动区域大小
            gradients: true,        // 启用渐变
            duration: 600,          // 动画持续时间
            acceleration: true      // 启用硬件加速
        },
        
        // DOM中的页面数量，最小值：6
        pagesInDOM = 6,
        
        // 页面位置配置
        pagePosition = {
            0: {top: 0, left: 0, right: 'auto', bottom: 'auto'},
            1: {top: 0, right: 0, left: 'auto', bottom: 'auto'}
        };

    // 工具函数
    
    /**
     * 获取图层的基本属性
     * @param {number} top - 顶部位置
     * @param {number} left - 左侧位置
     * @param {number} zIndex - z-index值
     * @param {string} overf - overflow属性
     * @returns {object} CSS属性对象
     */
    function divAtt(top, left, zIndex, overf) {
        return {
            'css': {
                position: 'absolute',
                top: top,
                left: left,
                'overflow': overf || 'hidden',
                'z-index': zIndex || 'auto'
            }
        };
    }

    /**
     * 从四个点的贝塞尔曲线获取2D点
     * @param {object} p1 - 第一个点
     * @param {object} p2 - 第二个点
     * @param {object} p3 - 第三个点
     * @param {object} p4 - 第四个点
     * @param {number} t - 时间参数 (0-1)
     * @returns {object} 2D点对象
     */
    function bezier(p1, p2, p3, p4, t) {
        var mum1 = 1 - t,
            mum13 = mum1 * mum1 * mum1,
            mu3 = t * t * t;

        return point2D(
            Math.round(mum13*p1.x + 3*t*mum1*mum1*p2.x + 3*t*t*mum1*p3.x + mu3*p4.x),
            Math.round(mum13*p1.y + 3*t*mum1*mum1*p2.y + 3*t*t*mum1*p3.y + mu3*p4.y)
        );
    }
    
    /**
     * 将角度从度转换为弧度
     * @param {number} degrees - 角度值
     * @returns {number} 弧度值
     */
    function rad(degrees) {
        return degrees/180*PI;
    }

    /**
     * 将角度从弧度转换为度
     * @param {number} radians - 弧度值
     * @returns {number} 角度值
     */
    function deg(radians) {
        return radians/PI*180;
    }

    /**
     * 获取2D点对象
     * @param {number} x - x坐标
     * @param {number} y - y坐标
     * @returns {object} 点对象
     */
    function point2D(x, y) {
        return {x: x, y: y};
    }

    /**
     * 返回平移变换值
     * @param {number} x - x轴平移
     * @param {number} y - y轴平移
     * @param {boolean} use3d - 是否使用3D变换
     * @returns {string} CSS变换字符串
     */
    function translate(x, y, use3d) {
        return (has3d && use3d) ? 
            ' translate3d(' + x + 'px,' + y + 'px, 0px) ' : 
            ' translate(' + x + 'px, ' + y + 'px) ';
    }

    /**
     * 返回旋转变换值
     * @param {number} degrees - 旋转角度
     * @returns {string} CSS变换字符串
     */
    function rotate(degrees) {
        return ' rotate(' + degrees + 'deg) ';
    }

    /**
     * 检查属性是否属于对象
     * @param {string} property - 属性名
     * @param {object} object - 对象
     * @returns {boolean} 是否拥有该属性
     */
    function has(property, object) {
        return Object.prototype.hasOwnProperty.call(object, property);
    }

    /**
     * 获取CSS3供应商前缀
     * @returns {string} 供应商前缀
     */
    function getPrefix() {
        var vendorPrefixes = ['Moz','Webkit','Khtml','O','ms'],
            len = vendorPrefixes.length,
            vendor = '';

        while (len--) {
            if ((vendorPrefixes[len] + 'Transform') in document.body.style) {
                vendor = '-' + vendorPrefixes[len].toLowerCase() + '-';
                break;
            }
        }
        return vendor;
    }

    // DOM 操作工具函数
    
    /**
     * 扩展对象属性
     * @param {object} target - 目标对象
     * @param {...object} sources - 源对象
     * @returns {object} 扩展后的对象
     */
    function extend(target) {
        var sources = Array.prototype.slice.call(arguments, 1);
        sources.forEach(function(source) {
            if (source) {
                for (var key in source) {
                    if (source.hasOwnProperty(key)) {
                        target[key] = source[key];
                    }
                }
            }
        });
        return target;
    }

    /**
     * 创建DOM元素
     * @param {string} tagName - 标签名
     * @param {object} attributes - 属性对象
     * @returns {Element} DOM元素
     */
    function createElement(tagName, attributes) {
        var element = document.createElement(tagName);
        if (attributes) {
            for (var key in attributes) {
                if (key === 'css' && typeof attributes[key] === 'object') {
                    // 设置CSS样式
                    for (var cssKey in attributes[key]) {
                        element.style[cssKey] = attributes[key][cssKey];
                    }
                } else if (key === 'class') {
                    element.className = attributes[key];
                } else {
                    element.setAttribute(key, attributes[key]);
                }
            }
        }
        return element;
    }

    /**
     * 设置元素样式
     * @param {Element} element - DOM元素
     * @param {object|string} styles - 样式对象或样式名
     * @param {string} value - 样式值（当styles为字符串时）
     */
    function setStyles(element, styles, value) {
        if (typeof styles === 'string') {
            element.style[styles] = value;
        } else if (typeof styles === 'object') {
            for (var key in styles) {
                if (styles.hasOwnProperty(key)) {
                    element.style[key] = styles[key];
                }
            }
        }
    }

    /**
     * 获取元素的计算样式值
     * @param {Element} element - DOM元素
     * @param {string} property - CSS属性名
     * @returns {string} 样式值
     */
    function getComputedStyleValue(element, property) {
        return window.getComputedStyle(element)[property];
    }

    /**
     * 获取元素相对于文档的偏移量
     * @param {Element} element - DOM元素
     * @returns {object} 偏移量对象 {top, left}
     */
    function getOffset(element) {
        var rect = element.getBoundingClientRect();
        return {
            top: rect.top + window.pageYOffset,
            left: rect.left + window.pageXOffset
        };
    }

    /**
     * 获取元素的宽度
     * @param {Element} element - DOM元素
     * @returns {number} 宽度值
     */
    function getWidth(element) {
        return element.offsetWidth;
    }

    /**
     * 获取元素的高度
     * @param {Element} element - DOM元素
     * @returns {number} 高度值
     */
    function getHeight(element) {
        return element.offsetHeight;
    }

    /**
     * 检查元素是否可见
     * @param {Element} element - DOM元素
     * @returns {boolean} 是否可见
     */
    function isVisible(element) {
        return element.offsetWidth > 0 && element.offsetHeight > 0;
    }

    /**
     * 数据存储管理器
     * 为元素提供数据存储功能，类似jQuery的data()方法
     */
    var DataManager = {
        // 存储所有元素数据的WeakMap
        storage: new WeakMap(),

        /**
         * 设置元素数据
         * @param {Element} element - DOM元素
         * @param {string|object} key - 数据键名或数据对象
         * @param {*} value - 数据值
         */
        set: function(element, key, value) {
            var data = this.storage.get(element) || {};

            if (typeof key === 'object') {
                extend(data, key);
            } else {
                data[key] = value;
            }

            this.storage.set(element, data);
        },

        /**
         * 获取元素数据
         * @param {Element} element - DOM元素
         * @param {string} key - 数据键名（可选）
         * @returns {*} 数据值或数据对象
         */
        get: function(element, key) {
            var data = this.storage.get(element) || {};
            return key ? data[key] : data;
        },

        /**
         * 删除元素数据
         * @param {Element} element - DOM元素
         * @param {string} key - 数据键名（可选）
         */
        remove: function(element, key) {
            var data = this.storage.get(element);
            if (data) {
                if (key) {
                    delete data[key];
                } else {
                    this.storage.delete(element);
                }
            }
        }
    };

    /**
     * 添加渐变效果
     * @param {Element} element - DOM元素
     * @param {object} p0 - 起始点
     * @param {object} p1 - 结束点
     * @param {Array} colors - 颜色数组
     * @param {number} numColors - 颜色数量
     */
    function gradient(element, p0, p1, colors, numColors) {
        var j, cols = [];

        if (vendor === '-webkit-') {
            // WebKit浏览器的渐变处理
            for (j = 0; j < numColors; j++) {
                cols.push('color-stop(' + colors[j][0] + ', ' + colors[j][1] + ')');
            }

            setStyles(element, {
                'background-image': '-webkit-gradient(linear, ' + p0.x + '% ' + p0.y + '%,  ' +
                                   p1.x + '% ' + p1.y + '%, ' + cols.join(',') + ' )'
            });

        } else {
            // 非WebKit浏览器的渐变处理
            var width = getWidth(element),
                height = getHeight(element);

            p0 = {x: p0.x/100 * width, y: p0.y/100 * height};
            p1 = {x: p1.x/100 * width, y: p1.y/100 * height};

            var dx = p1.x - p0.x,
                dy = p1.y - p0.y,
                angle = Math.atan2(dy, dx),
                angle2 = angle - Math.PI/2,
                diagonal = Math.abs(width * Math.sin(angle2)) + Math.abs(height * Math.cos(angle2)),
                gradientDiagonal = Math.sqrt(dy*dy + dx*dx),
                corner = point2D((p1.x < p0.x) ? width : 0, (p1.y < p0.y) ? height : 0),
                slope = Math.tan(angle),
                inverse = -1/slope,
                x = (inverse*corner.x - corner.y - slope*p0.x + p0.y) / (inverse - slope),
                c = {x: x, y: inverse*x - inverse*corner.x + corner.y},
                segA = Math.sqrt(Math.pow(c.x - p0.x, 2) + Math.pow(c.y - p0.y, 2));

            for (j = 0; j < numColors; j++) {
                cols.push(' ' + colors[j][1] + ' ' +
                         ((segA + gradientDiagonal * colors[j][0]) * 100 / diagonal) + '%');
            }

            setStyles(element, {
                'background-image': vendor + 'linear-gradient(' + (-angle) + 'rad,' + cols.join(',') + ')'
            });
        }
    }

    /**
     * 事件管理器
     * 提供事件绑定、解绑和触发功能
     */
    var EventManager = {
        /**
         * 绑定事件
         * @param {Element} element - DOM元素
         * @param {string} eventType - 事件类型
         * @param {Function} handler - 事件处理函数
         */
        on: function(element, eventType, handler) {
            element.addEventListener(eventType, handler, false);
        },

        /**
         * 解绑事件
         * @param {Element} element - DOM元素
         * @param {string} eventType - 事件类型
         * @param {Function} handler - 事件处理函数
         */
        off: function(element, eventType, handler) {
            element.removeEventListener(eventType, handler, false);
        },

        /**
         * 触发事件
         * @param {Element} element - DOM元素
         * @param {string} eventType - 事件类型
         * @param {*} detail - 事件详情数据
         */
        trigger: function(element, eventType, detail) {
            var event;
            if (typeof CustomEvent === 'function') {
                event = new CustomEvent(eventType, {
                    detail: detail,
                    bubbles: true,
                    cancelable: true
                });
            } else {
                // IE兼容性处理
                event = document.createEvent('CustomEvent');
                event.initCustomEvent(eventType, true, true, detail);
            }
            element.dispatchEvent(event);
        }
    };

    /**
     * 动画管理器
     * 提供自定义动画功能
     */
    var AnimationManager = {
        /**
         * 执行动画
         * @param {Element} element - DOM元素
         * @param {object} options - 动画选项
         */
        animate: function(element, options) {
            var data = DataManager.get(element);

            // 清除之前的动画
            if (data.effect) {
                clearInterval(data.effect.handle);
            }

            if (options) {
                // 确保to和from是数组
                if (!Array.isArray(options.to)) options.to = [options.to];
                if (!Array.isArray(options.from)) options.from = [options.from];

                // 默认缓动函数
                if (!options.easing) {
                    options.easing = function(x, t, b, c, d) {
                        return c * Math.sqrt(1 - (t = t/d - 1) * t) + b;
                    };
                }

                var j, diff = [],
                    len = options.to.length,
                    fps = options.fps || 30,
                    time = -fps,

                    frame = function() {
                        var j, v = [];
                        time = Math.min(options.duration, time + fps);

                        for (j = 0; j < len; j++) {
                            v.push(options.easing(1, time, options.from[j], diff[j], options.duration));
                        }

                        options.frame(len === 1 ? v[0] : v);

                        if (time === options.duration) {
                            clearInterval(data.effect.handle);
                            DataManager.remove(element, 'effect');
                            if (options.complete) {
                                options.complete();
                            }
                        }
                    };

                // 计算差值
                for (j = 0; j < len; j++) {
                    diff.push(options.to[j] - options.from[j]);
                }

                data.effect = options;
                data.effect.handle = setInterval(frame, fps);
                DataManager.set(element, 'effect', data.effect);
                frame();
            } else {
                DataManager.remove(element, 'effect');
            }
        }
    };

    /**
     * Turn方法集合
     * 实现翻页效果的核心功能
     */
    var turnMethods = {
        /**
         * 初始化Turn效果
         * @param {object} opts - 选项配置
         */
        init: function(element, opts) {
            // 定义常量
            if (has3d === undefined) {
                has3d = 'WebKitCSSMatrix' in window || 'MozPerspective' in document.body.style;
                vendor = getPrefix();
            }

            var i, data = DataManager.get(element),
                children = Array.prototype.slice.call(element.children);

            opts = extend({
                width: getWidth(element),
                height: getHeight(element)
            }, turnOptions, opts);

            data.opts = opts;
            data.pageObjs = {};
            data.pages = {};
            data.pageWrap = {};
            data.pagePlace = {};
            data.pageMv = [];
            data.totalPages = opts.pages || 0;

            // 绑定事件回调
            if (opts.when) {
                for (i in opts.when) {
                    if (has(i, opts.when)) {
                        EventManager.on(element, i, opts.when[i]);
                    }
                }
            }

            // 设置容器样式
            setStyles(element, {
                position: 'relative',
                width: opts.width + 'px',
                height: opts.height + 'px'
            });

            turnMethods.display(element, opts.display);

            // 启用硬件加速
            if (has3d && !isTouch && opts.acceleration) {
                setTransform(element, translate(0, 0, true));
            }

            // 添加现有页面
            for (i = 0; i < children.length; i++) {
                turnMethods.addPage(element, children[i], i + 1);
            }

            turnMethods.page(element, opts.page);

            // 允许设置活动角落作为选项
            corners = extend({}, corners, opts.corners);

            // 事件监听器
            var self = this;
            EventManager.on(element, events.start, function(e) {
                var data = DataManager.get(element);
                for (var page in data.pages) {
                    if (has(page, data.pages) && flipMethods._eventStart.call(data.pages[page], e) === false) {
                        return false;
                    }
                }
            });

            EventManager.on(document, events.move, function(e) {
                var data = DataManager.get(element);
                for (var page in data.pages) {
                    if (has(page, data.pages)) {
                        flipMethods._eventMove.call(data.pages[page], e);
                    }
                }
            });

            EventManager.on(document, events.end, function(e) {
                var data = DataManager.get(element);
                for (var page in data.pages) {
                    if (has(page, data.pages)) {
                        flipMethods._eventEnd.call(data.pages[page], e);
                    }
                }
            });

            data.done = true;
            DataManager.set(element, data);
            return element;
        },

        /**
         * 从外部数据添加页面
         * @param {Element} element - 容器元素
         * @param {Element} pageElement - 页面元素
         * @param {number} page - 页面编号
         */
        addPage: function(element, pageElement, page) {
            var incPages = false,
                data = DataManager.get(element),
                lastPage = data.totalPages + 1;

            if (page) {
                if (page === lastPage) {
                    page = lastPage;
                    incPages = true;
                } else if (page > lastPage) {
                    throw new Error('It is impossible to add the page "' + page + '", the maximum value is: "' + lastPage + '"');
                }
            } else {
                page = lastPage;
                incPages = true;
            }

            if (page >= 1 && page <= lastPage) {
                // 停止动画
                if (data.done) turnMethods.stop(element);

                // 如果需要，移动页面
                if (page in data.pageObjs) {
                    turnMethods._movePages(element, page, 1);
                }

                // 更新页面数量
                if (incPages) {
                    data.totalPages = lastPage;
                }

                // 添加元素
                pageElement.className = (pageElement.className || '') + ' turn-page p' + page;
                data.pageObjs[page] = pageElement;

                // 添加页面
                turnMethods._addPage(element, page);

                // 更新视图
                if (data.done) {
                    turnMethods.update(element);
                }

                turnMethods._removeFromDOM(element);
            }

            return element;
        },

        /**
         * 设置或获取显示模式
         * @param {Element} element - 容器元素
         * @param {string} display - 显示模式
         */
        display: function(element, display) {
            var data = DataManager.get(element),
                currentDisplay = data.display;

            if (display) {
                if (displays.indexOf(display) === -1) {
                    throw new Error('"' + display + '" is not a value for display');
                }

                if (display === 'single') {
                    if (!data.pageObjs[0]) {
                        this.stop(element);
                        setStyles(element, {'overflow': 'hidden'});
                        data.pageObjs[0] = createElement('div', {
                            'class': 'turn-page p-temporal',
                            css: {
                                width: getWidth(element) + 'px',
                                height: getHeight(element) + 'px'
                            }
                        });
                        element.appendChild(data.pageObjs[0]);
                    }
                } else {
                    if (data.pageObjs[0]) {
                        this.stop(element);
                        setStyles(element, {'overflow': ''});
                        if (data.pageObjs[0].parentNode) {
                            data.pageObjs[0].parentNode.removeChild(data.pageObjs[0]);
                        }
                        delete data.pageObjs[0];
                    }
                }

                data.display = display;

                if (currentDisplay) {
                    var size = this.size(element);
                    this._movePages(element, 1, 0);
                    this.size(element, size.width, size.height);
                    this.update(element);
                }

                DataManager.set(element, data);
                return element;
            } else {
                return currentDisplay;
            }
        },

        /**
         * 检测页面是否正在动画
         * @param {Element} element - 容器元素
         * @returns {boolean} 是否正在动画
         */
        animating: function(element) {
            var data = DataManager.get(element);
            return data.pageMv.length > 0;
        },

        /**
         * 禁用和启用效果
         * @param {Element} element - 容器元素
         * @param {boolean} bool - 是否禁用
         */
        disable: function(element, bool) {
            var page,
                data = DataManager.get(element),
                view = this.view(element);

            data.disabled = bool === undefined || bool === true;

            for (page in data.pages) {
                if (has(page, data.pages)) {
                    flipMethods.disable(data.pages[page], bool ? view.indexOf(parseInt(page)) === -1 : false);
                }
            }

            DataManager.set(element, data);
            return element;
        },

        /**
         * 获取和设置大小
         * @param {Element} element - 容器元素
         * @param {number} width - 宽度
         * @param {number} height - 高度
         */
        size: function(element, width, height) {
            if (width && height) {
                var data = DataManager.get(element),
                    pageWidth = (data.display === 'double') ? width / 2 : width,
                    page;

                setStyles(element, {width: width + 'px', height: height + 'px'});

                if (data.pageObjs[0]) {
                    setStyles(data.pageObjs[0], {width: pageWidth + 'px', height: height + 'px'});
                }

                for (page in data.pageWrap) {
                    if (!has(page, data.pageWrap)) continue;
                    setStyles(data.pageObjs[page], {width: pageWidth + 'px', height: height + 'px'});
                    setStyles(data.pageWrap[page], {width: pageWidth + 'px', height: height + 'px'});
                    if (data.pages[page]) {
                        setStyles(data.pages[page], {width: pageWidth + 'px', height: height + 'px'});
                    }
                }

                this.resize(element);
                DataManager.set(element, data);
                return element;
            } else {
                return {width: getWidth(element), height: getHeight(element)};
            }
        },

        /**
         * 调整每个页面的大小
         * @param {Element} element - 容器元素
         */
        resize: function(element) {
            var page, data = DataManager.get(element);

            if (data.pages[0]) {
                setStyles(data.pageWrap[0], {left: (-getWidth(element)) + 'px'});
                flipMethods.resize(data.pages[0], true);
            }

            for (page = 1; page <= data.totalPages; page++) {
                if (data.pages[page]) {
                    flipMethods.resize(data.pages[page], true);
                }
            }
        },

        /**
         * 获取和设置页面
         * @param {Element} element - 容器元素
         * @param {number} page - 页面编号
         */
        page: function(element, page) {
            if (page !== undefined) {
                page = parseInt(page, 10);
                var data = DataManager.get(element);

                if (page > 0 && page <= data.totalPages) {
                    if (!data.done || this.view(element).indexOf(page) !== -1) {
                        this._fitPage(element, page);
                    } else {
                        this._turnPage(element, page);
                    }
                    return element;
                } else {
                    return data.page;
                }
            } else {
                var data = DataManager.get(element);
                return data.page;
            }
        },

        /**
         * 转到下一个视图
         * @param {Element} element - 容器元素
         */
        next: function(element) {
            var data = DataManager.get(element);
            return this.page(element, this._view(element, data.page).pop() + 1);
        },

        /**
         * 转到上一个视图
         * @param {Element} element - 容器元素
         */
        previous: function(element) {
            var data = DataManager.get(element);
            return this.page(element, this._view(element, data.page).shift() - 1);
        },

        /**
         * 从内部数据添加页面
         * @param {Element} element - 容器元素
         * @param {number} page - 页面编号
         */
        _addPage: function(element, page) {
            var data = DataManager.get(element),
                pageElement = data.pageObjs[page];

            if (pageElement) {
                if (turnMethods._necessPage(element, page)) {
                    if (!data.pageWrap[page]) {
                        var pageWidth = (data.display === 'double') ? getWidth(element) / 2 : getWidth(element),
                            pageHeight = getHeight(element);

                        setStyles(pageElement, {
                            width: pageWidth + 'px',
                            height: pageHeight + 'px'
                        });

                        // 位置
                        data.pagePlace[page] = page;

                        // 包装器
                        var wrapperStyles = extend({
                            position: 'absolute',
                            overflow: 'hidden',
                            width: pageWidth + 'px',
                            height: pageHeight + 'px'
                        }, pagePosition[(data.display === 'double') ? page % 2 : 0]);

                        data.pageWrap[page] = createElement('div', {
                            'class': 'turn-page-wrapper',
                            page: page,
                            css: wrapperStyles
                        });

                        // 添加到容器
                        element.appendChild(data.pageWrap[page]);

                        // 将页面元素移动到包装器
                        data.pageWrap[page].appendChild(pageElement);
                    }

                    // 如果页面在当前视图中，创建翻页效果
                    if (!page || turnMethods._setPageLoc(element, page) === 1) {
                        turnMethods._makeFlip(element, page);
                    }
                } else {
                    // 位置
                    data.pagePlace[page] = 0;

                    // 从DOM中移除元素
                    if (data.pageObjs[page] && data.pageObjs[page].parentNode) {
                        data.pageObjs[page].parentNode.removeChild(data.pageObjs[page]);
                    }
                }
            }
            DataManager.set(element, data);
        },

        /**
         * 检查页面是否在内存中
         * @param {Element} element - 容器元素
         * @param {number} page - 页面编号
         * @returns {boolean} 是否存在
         */
        hasPage: function(element, page) {
            var data = DataManager.get(element);
            return page in data.pageObjs;
        },

        /**
         * 为页面准备翻页效果
         * @param {Element} element - 容器元素
         * @param {number} page - 页面编号
         */
        _makeFlip: function(element, page) {
            var data = DataManager.get(element);

            if (!data.pages[page] && data.pagePlace[page] === page) {
                var single = data.display === 'single',
                    even = page % 2;

                var flipOptions = {
                    page: page,
                    next: (single && page === data.totalPages) ? page - 1 : ((even || single) ? page + 1 : page - 1),
                    turn: element,
                    duration: data.opts.duration,
                    acceleration: data.opts.acceleration,
                    corners: (single) ? 'all' : ((even) ? 'forward' : 'backward'),
                    backGradient: data.opts.gradients,
                    frontGradient: data.opts.gradients
                };

                setStyles(data.pageObjs[page], {
                    width: (single ? getWidth(element) : getWidth(element) / 2) + 'px',
                    height: getHeight(element) + 'px'
                });

                data.pages[page] = data.pageObjs[page];
                flipMethods.init(data.pages[page], flipOptions);
                flipMethods.disable(data.pages[page], data.disabled);

                // 绑定事件
                EventManager.on(data.pages[page], 'pressed', this._pressed.bind(this));
                EventManager.on(data.pages[page], 'released', this._released.bind(this));
                EventManager.on(data.pages[page], 'start', this._start.bind(this));
                EventManager.on(data.pages[page], 'end', this._end.bind(this));
                EventManager.on(data.pages[page], 'flip', this._flip.bind(this));
            }
            return data.pages[page];
        },

        /**
         * 在范围内创建页面
         * @param {Element} element - 容器元素
         */
        _makeRange: function(element) {
            var page,
                range = this.range(element);

            for (page = range[0]; page <= range[1]; page++) {
                this._addPage(element, page);
            }
        },

        /**
         * 返回应该在DOM中的页面范围
         * @param {Element} element - 容器元素
         * @param {number} page - 页面编号
         * @returns {Array} 页面范围数组
         */
        range: function(element, page) {
            var remainingPages, left, right,
                data = DataManager.get(element);
            page = page || data.tpage || data.page;
            var view = this._view(element, page);

            if (page < 1 || page > data.totalPages) {
                throw new Error('"' + page + '" is not a page for range');
            }

            view[1] = view[1] || view[0];

            if (view[0] >= 1 && view[1] <= data.totalPages) {
                remainingPages = Math.floor((pagesInDOM - 2) / 2);

                if (data.totalPages - view[1] > view[0]) {
                    left = Math.min(view[0] - 1, remainingPages);
                    right = 2 * remainingPages - left;
                } else {
                    right = Math.min(data.totalPages - view[1], remainingPages);
                    left = 2 * remainingPages - right;
                }
            } else {
                left = pagesInDOM - 1;
                right = pagesInDOM - 1;
            }

            return [Math.max(1, view[0] - left), Math.min(data.totalPages, view[1] + right)];
        },

        /**
         * 检测页面是否在当前视图的pagesInDOM范围内
         * @param {Element} element - 容器元素
         * @param {number} page - 页面编号
         * @returns {boolean} 是否需要
         */
        _necessPage: function(element, page) {
            if (page === 0) {
                return true;
            }

            var range = this.range(element);
            return page >= range[0] && page <= range[1];
        },

        /**
         * 通过从DOM中移除页面来释放内存
         * @param {Element} element - 容器元素
         */
        _removeFromDOM: function(element) {
            var page, data = DataManager.get(element);

            for (page in data.pageWrap) {
                if (has(page, data.pageWrap) && !this._necessPage(element, page)) {
                    this._removePageFromDOM(element, page);
                }
            }
        },

        /**
         * 从DOM中移除页面及其内部引用
         * @param {Element} element - 容器元素
         * @param {number} page - 页面编号
         */
        _removePageFromDOM: function(element, page) {
            var data = DataManager.get(element);

            if (data.pages[page]) {
                var dd = DataManager.get(data.pages[page]);
                if (dd.f && dd.f.fwrapper && dd.f.fwrapper.parentNode) {
                    dd.f.fwrapper.parentNode.removeChild(dd.f.fwrapper);
                }
                if (data.pages[page].parentNode) {
                    data.pages[page].parentNode.removeChild(data.pages[page]);
                }
                delete data.pages[page];
            }

            if (data.pageObjs[page] && data.pageObjs[page].parentNode) {
                data.pageObjs[page].parentNode.removeChild(data.pageObjs[page]);
            }

            if (data.pageWrap[page]) {
                if (data.pageWrap[page].parentNode) {
                    data.pageWrap[page].parentNode.removeChild(data.pageWrap[page]);
                }
                delete data.pageWrap[page];
            }

            delete data.pagePlace[page];
            DataManager.set(element, data);
        },

        /**
         * 移除页面
         * @param {Element} element - 容器元素
         * @param {number} page - 页面编号
         */
        removePage: function(element, page) {
            var data = DataManager.get(element);

            if (data.pageObjs[page]) {
                // 停止动画
                this.stop(element);

                // 移除页面
                this._removePageFromDOM(element, page);
                delete data.pageObjs[page];

                // 移动页面后面的页面
                this._movePages(element, page, -1);

                // 调整杂志大小
                data.totalPages = data.totalPages - 1;
                this._makeRange(element);

                // 检查当前视图
                if (data.page > data.totalPages) {
                    this.page(element, data.totalPages);
                }
                DataManager.set(element, data);
            }

            return element;
        },

        /**
         * 获取视图索引
         * @param {Element} element - 容器元素
         * @param {number} page - 页面编号
         * @returns {Array} 视图数组
         */
        _view: function(element, page) {
            var data = DataManager.get(element);
            page = page || data.page;

            if (data.display === 'double') {
                return (page % 2) ? [page - 1, page] : [page, page + 1];
            } else {
                return [page];
            }
        },

        /**
         * 获取视图
         * @param {Element} element - 容器元素
         * @param {number} page - 页面编号
         * @returns {Array} 视图数组
         */
        view: function(element, page) {
            var data = DataManager.get(element),
                view = this._view(element, page);

            return (data.display === 'double') ?
                [(view[0] > 0) ? view[0] : 0, (view[1] <= data.totalPages) ? view[1] : 0] :
                [(view[0] > 0 && view[0] <= data.totalPages) ? view[0] : 0];
        },

        /**
         * 停止动画
         * @param {Element} element - 容器元素
         * @param {boolean} ok - 是否确认停止
         */
        stop: function(element, ok) {
            var i, opts, data = DataManager.get(element), pages = data.pageMv;

            data.pageMv = [];

            if (data.tpage) {
                data.page = data.tpage;
                delete data['tpage'];
            }

            for (i in pages) {
                if (!has(i, pages)) continue;
                opts = DataManager.get(data.pages[pages[i]]).f.opts;
                flipMethods._moveFoldingPage(data.pages[pages[i]], null);
                flipMethods.hideFoldedPage(data.pages[pages[i]]);
                data.pagePlace[opts.next] = opts.next;

                if (opts.force) {
                    opts.next = (opts.page % 2 === 0) ? opts.page - 1 : opts.page + 1;
                    delete opts['force'];
                }
            }

            this.update(element);
            DataManager.set(element, data);
            return element;
        },

        /**
         * 设置页面位置和显示属性
         * @param {Element} element - 容器元素
         * @param {number} page - 页面编号
         * @returns {number} 位置状态
         */
        _setPageLoc: function(element, page) {
            var data = DataManager.get(element),
                view = this.view(element);

            if (page === view[0] || page === view[1]) {
                setStyles(data.pageWrap[page], {
                    'z-index': data.totalPages,
                    display: ''
                });
                return 1;
            } else if ((data.display === 'single' && page === view[0] + 1) ||
                      (data.display === 'double' && (page === view[0] - 2 || page === view[1] + 2))) {
                setStyles(data.pageWrap[page], {
                    'z-index': data.totalPages - 1,
                    display: ''
                });
                return 2;
            } else {
                setStyles(data.pageWrap[page], {
                    'z-index': 0,
                    display: 'none'
                });
                return 0;
            }
        },

        /**
         * 更新页面的z-index和显示属性
         * @param {Element} element - 容器元素
         */
        update: function(element) {
            var page, data = DataManager.get(element);

            if (data.pageMv.length && data.pageMv[0] !== 0) {
                // 更新动画
                var apage,
                    pos = this.calculateZ(element, data.pageMv),
                    view = this.view(element, data.tpage);

                if (data.pagePlace[view[0]] === view[0]) apage = view[0];
                else if (data.pagePlace[view[1]] === view[1]) apage = view[1];

                for (page in data.pageWrap) {
                    if (!has(page, data.pageWrap)) continue;

                    setStyles(data.pageWrap[page], {
                        display: (pos.pageV[page]) ? '' : 'none',
                        'z-index': pos.pageZ[page] || 0
                    });

                    if (data.pages[page]) {
                        flipMethods.z(data.pages[page], pos.partZ[page] || null);

                        if (pos.pageV[page]) {
                            flipMethods.resize(data.pages[page]);
                        }

                        if (data.tpage) {
                            flipMethods.disable(data.pages[page], true);
                        }
                    }
                }
            } else {
                // 更新静态页面
                for (page in data.pageWrap) {
                    if (!has(page, data.pageWrap)) continue;
                    var pageLocation = this._setPageLoc(element, page);
                    if (data.pages[page]) {
                        flipMethods.disable(data.pages[page], data.disabled || pageLocation !== 1);
                        flipMethods.z(data.pages[page], null);
                    }
                }
            }
            DataManager.set(element, data);
        },

        /**
         * 计算动画期间页面的z-index值
         * @param {Element} element - 容器元素
         * @param {Array} mv - 移动页面数组
         * @returns {object} z-index配置对象
         */
        calculateZ: function(element, mv) {
            var i, page, nextPage, placePage, dpage,
                data = DataManager.get(element),
                view = this.view(element),
                currentPage = view[0] || view[1],
                r = {pageZ: {}, partZ: {}, pageV: {}},
                self = this,

                addView = function(page) {
                    var view = self.view(element, page);
                    if (view[0]) r.pageV[view[0]] = true;
                    if (view[1]) r.pageV[view[1]] = true;
                };

            for (i = 0; i < mv.length; i++) {
                page = mv[i];
                nextPage = DataManager.get(data.pages[page]).f.opts.next;
                placePage = data.pagePlace[page];
                addView(page);
                addView(nextPage);
                dpage = (data.pagePlace[nextPage] === nextPage) ? nextPage : page;
                r.pageZ[dpage] = data.totalPages - Math.abs(currentPage - dpage);
                r.partZ[placePage] = data.totalPages * 2 + Math.abs(currentPage - dpage);
            }

            return r;
        },

        /**
         * 设置页面而不产生效果
         * @param {Element} element - 容器元素
         * @param {number} page - 页面编号
         * @param {boolean} ok - 是否确认
         */
        _fitPage: function(element, page, ok) {
            var data = DataManager.get(element),
                newView = this.view(element, page);

            if (data.page !== page) {
                EventManager.trigger(element, 'turning', [page, newView]);
                if (newView.indexOf(1) !== -1) EventManager.trigger(element, 'first');
                if (newView.indexOf(data.totalPages) !== -1) EventManager.trigger(element, 'last');
            }

            if (!data.pageObjs[page]) {
                return;
            }

            data.tpage = page;

            this.stop(element, ok);
            this._removeFromDOM(element);
            this._makeRange(element);
            EventManager.trigger(element, 'turned', [page, newView]);
            DataManager.set(element, data);
        },

        /**
         * 翻转到页面
         * @param {Element} element - 容器元素
         * @param {number} page - 页面编号
         */
        _turnPage: function(element, page) {
            var current, next,
                data = DataManager.get(element),
                view = this.view(element),
                newView = this.view(element, page);

            if (data.page !== page) {
                EventManager.trigger(element, 'turning', [page, newView]);
                if (newView.indexOf(1) !== -1) EventManager.trigger(element, 'first');
                if (newView.indexOf(data.totalPages) !== -1) EventManager.trigger(element, 'last');
            }

            if (!data.pageObjs[page]) {
                return;
            }

            data.tpage = page;

            this.stop(element);
            this._makeRange(element);

            if (data.display === 'single') {
                current = view[0];
                next = newView[0];
            } else if (view[1] && page > view[1]) {
                current = view[1];
                next = newView[0];
            } else if (view[0] && page < view[0]) {
                current = view[0];
                next = newView[1];
            }

            if (data.pages[current]) {
                var opts = DataManager.get(data.pages[current]).f.opts;
                data.tpage = next;

                if (opts.next !== next) {
                    opts.next = next;
                    data.pagePlace[next] = opts.page;
                    opts.force = true;
                }

                if (data.display === 'single') {
                    flipMethods.turnPage(data.pages[current], (newView[0] > view[0]) ? 'br' : 'bl');
                } else {
                    flipMethods.turnPage(data.pages[current]);
                }
            }
            DataManager.set(element, data);
        },

        /**
         * 移动页面
         * @param {Element} element - 容器元素
         * @param {number} from - 起始页面
         * @param {number} change - 变化量
         */
        _movePages: function(element, from, change) {
            var page,
                data = DataManager.get(element),
                single = data.display === 'single',
                move = function(page) {
                    var next = page + change,
                        odd = next % 2;

                    if (data.pageObjs[page]) {
                        data.pageObjs[next] = data.pageObjs[page];
                        data.pageObjs[next].className = data.pageObjs[next].className
                            .replace('page' + page, 'page' + next);
                    }

                    if (data.pagePlace[page] && data.pageWrap[page]) {
                        data.pagePlace[next] = next;
                        data.pageWrap[next] = data.pageWrap[page];

                        var position = pagePosition[(single) ? 0 : odd];
                        setStyles(data.pageWrap[next], position);
                        data.pageWrap[next].setAttribute('page', next);

                        if (data.pages[page]) {
                            data.pages[next] = data.pages[page];
                            flipMethods.options(data.pages[next], {
                                page: next,
                                next: (single || odd) ? next + 1 : next - 1,
                                corners: (single) ? 'all' : ((odd) ? 'forward' : 'backward')
                            });
                        }

                        if (change) {
                            delete data.pages[page];
                            delete data.pagePlace[page];
                            delete data.pageObjs[page];
                            delete data.pageWrap[page];
                        }
                    }
                };

            if (change > 0) {
                for (page = data.totalPages; page >= from; page--) move(page);
            } else {
                for (page = from; page <= data.totalPages; page++) move(page);
            }
            DataManager.set(element, data);
        },

        /**
         * 获取和设置页面数量
         * @param {Element} element - 容器元素
         * @param {number} pages - 页面数量
         */
        pages: function(element, pages) {
            var data = DataManager.get(element);

            if (pages) {
                if (pages < data.totalPages) {
                    for (var page = pages + 1; page <= data.totalPages; page++) {
                        this.removePage(element, page);
                    }

                    if (this.page(element) > pages) {
                        this.page(element, pages);
                    }
                }

                data.totalPages = pages;
                DataManager.set(element, data);
                return element;
            } else {
                return data.totalPages;
            }
        },

        /**
         * 添加运动到内部列表
         * @param {Element} pageElement - 页面元素
         */
        _addMotionPage: function(pageElement) {
            var opts = DataManager.get(pageElement).f.opts,
                turn = opts.turn,
                dd = DataManager.get(turn);

            opts.pageMv = opts.page;
            turnMethods._addMv(turn, opts.pageMv);
            dd.pagePlace[opts.next] = opts.page;
            turnMethods.update(turn);
        },

        /**
         * 从缓存中移除动画
         * @param {Element} element - 容器元素
         * @param {number} page - 页面编号
         */
        _removeMv: function(element, page) {
            var i, data = DataManager.get(element);

            for (i = 0; i < data.pageMv.length; i++) {
                if (data.pageMv[i] === page) {
                    data.pageMv.splice(i, 1);
                    DataManager.set(element, data);
                    return true;
                }
            }
            return false;
        },

        /**
         * 向缓存添加动画
         * @param {Element} element - 容器元素
         * @param {number} page - 页面编号
         */
        _addMv: function(element, page) {
            var data = DataManager.get(element);
            this._removeMv(element, page);
            data.pageMv.push(page);
            DataManager.set(element, data);
        },

        /**
         * 在flip上下文中调用的事件处理
         * @param {Event} e - 事件对象
         * @param {object} opts - 选项
         * @param {string} corner - 角落
         */
        _start: function(e, opts, corner) {
            var data = DataManager.get(opts.turn),
                event = new CustomEvent('start', {detail: [opts, corner]});

            e.stopPropagation();
            EventManager.trigger(opts.turn, 'start', [opts, corner]);

            if (event.defaultPrevented) {
                e.preventDefault();
                return;
            }

            if (data.display === 'single') {
                var left = corner.charAt(1) === 'l';
                if ((opts.page === 1 && left) || (opts.page === data.totalPages && !left)) {
                    e.preventDefault();
                } else {
                    if (left) {
                        opts.next = (opts.next < opts.page) ? opts.next : opts.page - 1;
                        opts.force = true;
                    } else {
                        opts.next = (opts.next > opts.page) ? opts.next : opts.page + 1;
                    }
                }
            }

            turnMethods._addMotionPage.call(this);
        },

        /**
         * 在flip上下文中调用的事件处理
         * @param {Event} e - 事件对象
         * @param {boolean} turned - 是否翻转
         */
        _end: function(e, turned) {
            var that = this,
                data = DataManager.get(that).f,
                opts = data.opts,
                turn = opts.turn,
                dd = DataManager.get(turn);

            e.stopPropagation();

            if (turned || dd.tpage) {
                if (dd.tpage === opts.next || dd.tpage === opts.page) {
                    delete dd['tpage'];
                    turnMethods._fitPage.call(turn, dd.tpage || opts.next, true);
                }
            } else {
                turnMethods._removeMv.call(turn, opts.pageMv);
                turnMethods.update.call(turnMethods, turn);
            }
        },

        /**
         * 在flip上下文中调用的事件处理
         */
        _pressed: function() {
            var page,
                that = this,
                data = DataManager.get(that).f,
                turn = data.opts.turn,
                pages = DataManager.get(turn).pages;

            for (page in pages) {
                if (page !== data.opts.page) {
                    flipMethods.disable(pages[page], true);
                }
            }

            return data.time = new Date().getTime();
        },

        /**
         * 在flip上下文中调用的事件处理
         * @param {Event} e - 事件对象
         * @param {object} point - 点坐标
         */
        _released: function(e, point) {
            var that = this,
                data = DataManager.get(that).f;

            e.stopPropagation();

            if ((new Date().getTime()) - data.time < 200 || point.x < 0 || point.x > getWidth(that)) {
                e.preventDefault();
                DataManager.get(data.opts.turn).tpage = data.opts.next;
                turnMethods.update.call(turnMethods, data.opts.turn);
                flipMethods.turnPage(that);
            }
        },

        /**
         * 在flip上下文中调用的事件处理
         */
        _flip: function() {
            var opts = DataManager.get(this).f.opts;
            EventManager.trigger(opts.turn, 'turn', [opts.next]);
        }
    };

    /**
     * Flip方法集合
     * 实现页面折叠效果的核心功能
     */
    var flipMethods = {
        /**
         * 初始化Flip效果
         * @param {Element} element - 页面元素
         * @param {object} opts - 选项配置
         */
        init: function(element, opts) {
            if (opts.gradients) {
                opts.frontGradient = true;
                opts.backGradient = true;
            }

            DataManager.set(element, 'f', {});
            this.options(element, opts);
            this._addPageWrapper(element);

            return element;
        },

        /**
         * 设置数据
         * @param {Element} element - 页面元素
         * @param {object} d - 数据对象
         */
        setData: function(element, d) {
            var data = DataManager.get(element);
            data.f = extend(data.f, d);
            DataManager.set(element, data);
            return element;
        },

        /**
         * 设置或获取选项
         * @param {Element} element - 页面元素
         * @param {object} opts - 选项配置
         */
        options: function(element, opts) {
            var data = DataManager.get(element).f;

            if (opts) {
                this.setData(element, {
                    opts: extend({}, data.opts || flipOptions, opts)
                });
                return element;
            } else {
                return data.opts;
            }
        },

        /**
         * 设置z-index
         * @param {Element} element - 页面元素
         * @param {number} z - z-index值
         */
        z: function(element, z) {
            var data = DataManager.get(element).f;
            data.opts['z-index'] = z;

            if (data.fwrapper) {
                var parentZIndex = data.parent ? parseInt(getComputedStyleValue(data.parent, 'z-index'), 10) || 0 : 0;
                setStyles(data.fwrapper, {
                    'z-index': z || parentZIndex
                });
            }

            return element;
        },

        /**
         * 获取允许的角落
         * @param {Element} element - 页面元素
         * @returns {Array} 允许的角落数组
         */
        _cAllowed: function(element) {
            var data = DataManager.get(element).f;
            return corners[data.opts.corners] || data.opts.corners;
        },

        /**
         * 检查角落是否被激活
         * @param {Element} element - 页面元素
         * @param {Event} e - 事件对象
         * @returns {object|boolean} 角落信息或false
         */
        _cornerActivated: function(element, e) {
            if (e.originalEvent === undefined) {
                return false;
            }

            e = (isTouch) ? e.originalEvent.touches : [e];

            var data = DataManager.get(element).f,
                pos = getOffset(data.parent),
                width = getWidth(element),
                height = getHeight(element),
                c = {
                    x: Math.max(0, e[0].pageX - pos.left),
                    y: Math.max(0, e[0].pageY - pos.top)
                },
                csz = data.opts.cornerSize,
                allowedCorners = this._cAllowed(element);

            if (c.x <= 0 || c.y <= 0 || c.x >= width || c.y >= height) return false;

            if (c.y < csz) c.corner = 't';
            else if (c.y >= height - csz) c.corner = 'b';
            else return false;

            if (c.x <= csz) c.corner += 'l';
            else if (c.x >= width - csz) c.corner += 'r';
            else return false;

            return (allowedCorners.indexOf(c.corner) === -1) ? false : c;
        },

        /**
         * 获取角落坐标
         * @param {Element} element - 页面元素
         * @param {string} corner - 角落名称
         * @param {number} opts - 偏移量
         * @returns {object} 坐标点
         */
        _c: function(element, corner, opts) {
            opts = opts || 0;
            var width = getWidth(element),
                height = getHeight(element);

            return ({
                tl: point2D(opts, opts),
                tr: point2D(width - opts, opts),
                bl: point2D(opts, height - opts),
                br: point2D(width - opts, height - opts)
            })[corner];
        },

        /**
         * 获取角落的第二个坐标
         * @param {Element} element - 页面元素
         * @param {string} corner - 角落名称
         * @returns {object} 坐标点
         */
        _c2: function(element, corner) {
            var width = getWidth(element),
                height = getHeight(element);

            return {
                tl: point2D(width * 2, 0),
                tr: point2D(-width, 0),
                bl: point2D(width * 2, height),
                br: point2D(-width, height)
            }[corner];
        },

        /**
         * 获取折叠页面
         * @param {Element} element - 页面元素
         * @param {string} corner - 角落名称
         * @returns {Element|null} 折叠页面元素
         */
        _foldingPage: function(element, corner) {
            var opts = DataManager.get(element).f.opts;

            if (opts.folding) return opts.folding;
            else if (opts.turn) {
                var data = DataManager.get(opts.turn);
                if (data.display === 'single') {
                    return (data.pageObjs[opts.next]) ? data.pageObjs[0] : null;
                } else {
                    return data.pageObjs[opts.next];
                }
            }
            return null;
        },

        /**
         * 检查是否需要背景渐变
         * @param {Element} element - 页面元素
         * @returns {boolean} 是否需要背景渐变
         */
        _backGradient: function(element) {
            var data = DataManager.get(element).f,
                turn = data.opts.turn,
                gradient = data.opts.backGradient &&
                          (!turn || DataManager.get(turn).display === 'single' ||
                           (data.opts.page !== 2 && data.opts.page !== DataManager.get(turn).totalPages - 1));

            if (gradient && !data.bshadow) {
                data.bshadow = createElement('div', divAtt(0, 0, 1));
                setStyles(data.bshadow, {
                    position: '',
                    width: getWidth(element) + 'px',
                    height: getHeight(element) + 'px'
                });
                data.parent.appendChild(data.bshadow);
            }

            return gradient;
        },

        /**
         * 调整大小
         * @param {Element} element - 页面元素
         * @param {boolean} full - 是否完全调整
         */
        resize: function(element, full) {
            var data = DataManager.get(element).f,
                width = getWidth(element),
                height = getHeight(element),
                size = Math.round(Math.sqrt(Math.pow(width, 2) + Math.pow(height, 2)));

            if (full && data.wrapper && data.fwrapper) {
                setStyles(data.wrapper, {width: size + 'px', height: size + 'px'});
                setStyles(data.fwrapper, {width: size + 'px', height: size + 'px'});

                var firstChild = data.fwrapper.children[0];
                if (firstChild) {
                    setStyles(firstChild, {width: width + 'px', height: height + 'px'});
                }

                if (data.fpage) {
                    setStyles(data.fpage, {width: height + 'px', height: width + 'px'});
                }

                if (data.opts.frontGradient && data.ashadow) {
                    setStyles(data.ashadow, {width: height + 'px', height: width + 'px'});
                }

                if (this._backGradient(element) && data.bshadow) {
                    setStyles(data.bshadow, {width: width + 'px', height: height + 'px'});
                }
            }

            if (data.parent && isVisible(data.parent)) {
                var parentOffset = getOffset(data.parent);
                setStyles(data.fwrapper, {
                    top: parentOffset.top + 'px',
                    left: parentOffset.left + 'px'
                });

                if (data.opts.turn) {
                    var turnOffset = getOffset(data.opts.turn);
                    setStyles(data.fparent, {
                        top: (-turnOffset.top) + 'px',
                        left: (-turnOffset.left) + 'px'
                    });
                }
            }

            this.z(element, data.opts['z-index']);
        },

        /**
         * 添加页面包装器
         * @param {Element} element - 页面元素
         */
        _addPageWrapper: function(element) {
            var data = DataManager.get(element).f,
                parent = element.parentNode;

            if (!data.wrapper) {
                var left = getComputedStyleValue(element, 'left'),
                    top = getComputedStyleValue(element, 'top'),
                    width = getWidth(element),
                    height = getHeight(element),
                    size = Math.round(Math.sqrt(Math.pow(width, 2) + Math.pow(height, 2)));

                data.parent = parent;
                data.fparent = (data.opts.turn) ? DataManager.get(data.opts.turn).fparent : document.getElementById('turn-fwrappers');

                if (!data.fparent) {
                    var fparent = createElement('div', {
                        css: {'pointer-events': 'none', display: 'none'}
                    });
                    DataManager.set(fparent, 'flips', 0);

                    if (data.opts.turn) {
                        var turnOffset = getOffset(data.opts.turn);
                        setStyles(fparent, extend(divAtt(-turnOffset.top, -turnOffset.left, 'auto', 'visible').css));
                        data.opts.turn.appendChild(fparent);
                        DataManager.set(data.opts.turn, 'fparent', fparent);
                    } else {
                        setStyles(fparent, extend(divAtt(0, 0, 'auto', 'visible').css));
                        fparent.id = 'turn-fwrappers';
                        document.body.appendChild(fparent);
                    }

                    data.fparent = fparent;
                }

                setStyles(element, {
                    position: 'absolute',
                    top: '0px',
                    left: '0px',
                    bottom: 'auto',
                    right: 'auto'
                });

                data.wrapper = createElement('div', divAtt(0, 0, getComputedStyleValue(element, 'z-index')));
                parent.appendChild(data.wrapper);
                data.wrapper.appendChild(element);

                var parentOffset = getOffset(parent);
                data.fwrapper = createElement('div', divAtt(parentOffset.top, parentOffset.left));
                setStyles(data.fwrapper, {display: 'none'});
                data.fparent.appendChild(data.fwrapper);

                var innerDiv = createElement('div', divAtt(0, 0, 0, 'visible'));
                data.fwrapper.appendChild(innerDiv);

                data.fpage = createElement('div', {css: {cursor: 'default'}});
                innerDiv.appendChild(data.fpage);

                if (data.opts.frontGradient) {
                    data.ashadow = createElement('div', divAtt(0, 0, 1));
                    data.fpage.appendChild(data.ashadow);
                }

                // 保存数据
                this.setData(element, data);

                // 设置大小
                this.resize(element, true);
            }
        },

        /**
         * 禁用或启用效果
         * @param {Element} element - 页面元素
         * @param {boolean} disable - 是否禁用
         */
        disable: function(element, disable) {
            this.setData(element, {'disabled': disable});
            return element;
        },

        /**
         * 隐藏折叠页面
         * @param {Element} element - 页面元素
         * @param {boolean} animate - 是否动画
         */
        hideFoldedPage: function(element, animate) {
            var data = DataManager.get(element).f;

            if (!data.point) return;

            var p1 = data.point,
                self = this,
                hide = function() {
                    data.point = null;
                    self.hide(element);
                    EventManager.trigger(element, 'end', [false]);
                };

            if (animate) {
                var p4 = this._c(element, p1.corner),
                    top = (p1.corner.substr(0,1) === 't'),
                    delta = (top) ? Math.min(0, p1.y - p4.y) / 2 : Math.max(0, p1.y - p4.y) / 2,
                    p2 = point2D(p1.x, p1.y + delta),
                    p3 = point2D(p4.x, p4.y - delta);

                AnimationManager.animate(element, {
                    from: 0,
                    to: 1,
                    frame: function(v) {
                        var np = bezier(p1, p2, p3, p4, v);
                        p1.x = np.x;
                        p1.y = np.y;
                        self._fold(element, p1);
                    },
                    complete: hide,
                    duration: 800,
                    hiding: true
                });
            } else {
                AnimationManager.animate(element, false);
                hide();
            }
        },

        /**
         * 隐藏效果
         * @param {Element} element - 页面元素
         */
        hide: function(element) {
            var data = DataManager.get(element).f,
                folding = this._foldingPage(element);

            var flipsData = DataManager.get(data.fparent);
            flipsData.flips = (flipsData.flips || 1) - 1;
            if (flipsData.flips === 0) {
                setStyles(data.fparent, {display: 'none'});
            }
            DataManager.set(data.fparent, flipsData);

            setStyles(element, {
                left: '0px',
                top: '0px',
                right: 'auto',
                bottom: 'auto'
            });
            setTransform(element, '', '0% 100%');

            if (data.wrapper) {
                setTransform(data.wrapper, '', '0% 100%');
            }

            if (data.fwrapper) {
                setStyles(data.fwrapper, {display: 'none'});
            }

            if (data.bshadow) {
                setStyles(data.bshadow, {display: 'none'});
            }

            if (folding) {
                setTransform(folding, '', '0% 0%');
            }

            return element;
        },

        /**
         * 移动折叠页面
         * @param {Element} element - 页面元素
         * @param {boolean} bool - 是否移动
         */
        _moveFoldingPage: function(element, bool) {
            var data = DataManager.get(element).f,
                folding = this._foldingPage(element);

            if (folding) {
                if (bool) {
                    var children = data.fpage.children;
                    var targetIndex = data.ashadow ? 1 : 0;
                    if (!children[targetIndex]) {
                        this.setData(element, {backParent: folding.parentNode});
                        data.fpage.insertBefore(folding, data.fpage.firstChild);
                    }
                } else {
                    if (data.backParent) {
                        data.backParent.insertBefore(folding, data.backParent.firstChild);
                    }
                }
            }
        },

        /**
         * 翻页
         * @param {Element} element - 页面元素
         * @param {string} corner - 角落
         */
        turnPage: function(element, corner) {
            var that = element,
                data = DataManager.get(element).f;

            corner = {corner: (data.corner) ? data.corner.corner : corner || this._cAllowed(element)[0]};

            var p1 = data.point || this._c(element, corner.corner, (data.opts.turn) ? DataManager.get(data.opts.turn).opts.elevation : 0),
                p4 = this._c2(element, corner.corner);

            EventManager.trigger(element, 'flip');
            AnimationManager.animate(element, {
                from: 0,
                to: 1,
                frame: function(v) {
                    var np = bezier(p1, p1, p4, p4, v);
                    corner.x = np.x;
                    corner.y = np.y;
                    flipMethods._showFoldedPage.call(that, corner);
                },

                complete: function() {
                    EventManager.trigger(that, 'end', [true]);
                },
                duration: data.opts.duration,
                turning: true
            });

            data.corner = null;
        },

        /**
         * 检查是否正在移动
         * @param {Element} element - 页面元素
         * @returns {boolean} 是否正在移动
         */
        moving: function(element) {
            return DataManager.get(element).effect !== undefined;
        },

        /**
         * 检查是否正在翻转
         * @param {Element} element - 页面元素
         * @returns {boolean} 是否正在翻转
         */
        isTurning: function(element) {
            return this.moving(element) && DataManager.get(element).effect.turning;
        },

        /**
         * 事件开始处理
         * @param {Event} e - 事件对象
         */
        _eventStart: function(e) {
            var data = DataManager.get(this).f;

            if (!data.disabled && !this.isTurning(this)) {
                data.corner = this._cornerActivated(this, e);
                if (data.corner && this._foldingPage(this, data.corner)) {
                    this._moveFoldingPage(this, true);
                    EventManager.trigger(this, 'pressed', [data.point]);
                    return false;
                } else {
                    data.corner = null;
                }
            }
        },

        /**
         * 事件移动处理
         * @param {Event} e - 事件对象
         */
        _eventMove: function(e) {
            var data = DataManager.get(this).f;

            if (!data.disabled) {
                e = (isTouch) ? e.originalEvent.touches : [e];

                if (data.corner) {
                    var pos = getOffset(data.parent);

                    data.corner.x = e[0].pageX - pos.left;
                    data.corner.y = e[0].pageY - pos.top;

                    this._showFoldedPage(this, data.corner);

                } else if (!DataManager.get(this).effect && isVisible(this)) { // roll over
                    var corner = this._cornerActivated(this, e[0]);
                    if (corner) {
                        var origin = this._c(this, corner.corner, data.opts.cornerSize / 2);
                        corner.x = origin.x;
                        corner.y = origin.y;
                        this._showFoldedPage(this, corner, true);
                    } else {
                        this.hideFoldedPage(this, true);
                    }
                }
            }
        },

        /**
         * 事件结束处理
         */
        _eventEnd: function() {
            var data = DataManager.get(this).f;

            if (!data.disabled && data.point) {
                var event = new CustomEvent('released', {detail: [data.point]});
                EventManager.trigger(this, 'released', [data.point]);
                if (!event.defaultPrevented) {
                    this.hideFoldedPage(this, true);
                }
            }

            data.corner = null;
        },

        /**
         * 显示折叠页面
         * @param {Element} element - 页面元素
         * @param {object} c - 角落信息
         * @param {boolean} animate - 是否动画
         */
        _showFoldedPage: function(element, c, animate) {
            var folding = this._foldingPage(element),
                dd = DataManager.get(element),
                data = dd.f;

            if (!data.point || data.point.corner !== c.corner) {
                var event = new CustomEvent('start', {detail: [data.opts, c.corner]});
                EventManager.trigger(element, 'start', [data.opts, c.corner]);

                if (event.defaultPrevented) {
                    return false;
                }
            }

            if (folding) {
                if (animate) {
                    var that = element,
                        point = (data.point && data.point.corner === c.corner) ? data.point : this._c(element, c.corner, 1);

                    AnimationManager.animate(element, {
                        from: [point.x, point.y],
                        to: [c.x, c.y],
                        duration: 500,
                        frame: function(v) {
                            c.x = Math.round(v[0]);
                            c.y = Math.round(v[1]);
                            flipMethods._fold.call(that, c);
                        }
                    });
                } else {
                    this._fold(element, c);
                    if (dd.effect && !dd.effect.turning) {
                        AnimationManager.animate(element, false);
                    }
                }

                if (!isVisible(data.fwrapper)) {
                    setStyles(data.fparent, {display: 'block'});
                    var flipsData = DataManager.get(data.fparent);
                    flipsData.flips = (flipsData.flips || 0) + 1;
                    DataManager.set(data.fparent, flipsData);

                    this._moveFoldingPage(element, true);
                    setStyles(data.fwrapper, {display: 'block'});

                    if (data.bshadow) {
                        setStyles(data.bshadow, {display: 'block'});
                    }
                }

                return true;
            }

            return false;
        },

        /**
         * 折叠处理（简化版本，实际实现会更复杂）
         * @param {Element} element - 页面元素
         * @param {object} point - 点坐标
         */
        _fold: function(element, point) {
            // 这里是折叠效果的核心算法
            // 由于算法复杂，这里提供简化版本
            var data = DataManager.get(element).f;
            data.point = point;

            // 应用基本的CSS变换
            var angle = Math.atan2(point.y - getHeight(element)/2, point.x - getWidth(element)/2);
            var transform = 'rotateY(' + (angle * 180 / Math.PI) + 'deg)';
            setTransform(element, transform);
        }
    };

    /**
     * 通用调用函数
     * @param {Element} element - DOM元素
     * @param {object} methods - 方法对象
     * @param {Array} args - 参数数组
     * @returns {*} 方法执行结果
     */
    function callMethod(element, methods, args) {
        if (!args[0] || typeof(args[0]) === 'object') {
            return methods.init.call(methods, element, args[0]);
        } else if (methods[args[0]] && args[0].toString().substr(0, 1) !== '_') {
            var methodArgs = Array.prototype.slice.call(args, 1);
            methodArgs.unshift(element);
            return methods[args[0]].apply(methods, methodArgs);
        } else {
            throw new Error(args[0] + ' is an invalid value');
        }
    }

    /**
     * 设置CSS变换
     * @param {Element} element - DOM元素
     * @param {string} transform - 变换字符串
     * @param {string} origin - 变换原点
     */
    function setTransform(element, transform, origin) {
        if (origin) {
            element.style[vendor + 'transform-origin'] = origin;
        }
        element.style[vendor + 'transform'] = transform;
    }

    /**
     * 公共API - Turn效果
     * @param {string|object} req - 方法名或选项对象
     * @returns {Element} DOM元素（支持链式调用）
     */
    function Turn(req) {
        var args = Array.prototype.slice.call(arguments);
        return callMethod(this, turnMethods, args);
    }

    /**
     * 公共API - Flip效果
     * @param {string|object} req - 方法名或选项对象
     * @returns {Element} DOM元素（支持链式调用）
     */
    function Flip(req) {
        var args = Array.prototype.slice.call(arguments);
        return callMethod(this, flipMethods, args);
    }

    /**
     * 为Element原型添加turn方法
     */
    Element.prototype.turn = function() {
        return Turn.apply(this, arguments);
    };

    /**
     * 为Element原型添加flip方法
     */
    Element.prototype.flip = function() {
        return Flip.apply(this, arguments);
    };

    /**
     * 为Element原型添加transform方法
     */
    Element.prototype.transform = function(transform, origin) {
        setTransform(this, transform, origin);
        return this;
    };

    /**
     * 为Element原型添加animatef方法
     */
    Element.prototype.animatef = function(options) {
        AnimationManager.animate(this, options);
        return this;
    };

    /**
     * 全局对象暴露
     */
    window.TurnJS = {
        Turn: Turn,
        Flip: Flip,
        isTouch: isTouch,
        version: '1.0.0'
    };

    // 如果支持模块化，则导出
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = window.TurnJS;
    }

    // AMD支持
    if (typeof define === 'function' && define.amd) {
        define(function() {
            return window.TurnJS;
        });
    }

})();
