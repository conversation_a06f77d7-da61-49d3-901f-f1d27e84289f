<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PDFLayerViewer - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#constants">常量</a>
    <a href="#properties">属性</a>
    <a href="#methods">方法</a>
    <a href="#flowcharts">流程图</a>
    <a href="#examples">示例</a>
    <a href="#notes">注意事项</a>
  </div>

  <h1>PDFLayerViewer 模块文档</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>PDFLayerViewer 是 PDF.js 库中负责管理和控制 PDF 文档中的可选内容图层（Optional Content Groups, OCG）的组件。它允许用户查看和切换文档中的不同图层，从而控制特定内容的显示或隐藏。这在包含多层信息的技术图纸、地图或复杂图表的 PDF 文档中特别有用。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 常量 -->
  <div id="constants">
    <h2>常量</h2>
    
    <p>PDFLayerViewer 使用以下常量：</p>
    
    <ul>
      <li>
        <code>LayerState.INITIAL_STATE</code>: 初始状态对象
        <ul>
          <li><code>layersCount</code>: 0</li>
          <li><code>prevOCGIdKey</code>: null</li>
        </ul>
      </li>
      <li><code>LAYER_TYPE</code>: 图层类型枚举
        <ul>
          <li><code>PARENT</code>: 父级图层</li>
          <li><code>CHILD</code>: 子级图层</li>
          <li><code>GROUP</code>: 组图层</li>
        </ul>
      </li>
    </ul>
  </div>

  <!-- 属性 -->
  <div id="properties">
    <h2>属性</h2>
    
    <h3>公共属性</h3>
    <ul>
      <li><code>optionalContentConfig</code>: 可选内容配置对象</li>
      <li><code>container</code>: 图层容器元素</li>
      <li><code>eventBus</code>: 事件总线</li>
      <li><code>l10n</code>: 本地化对象</li>
      <li><code>pdfDocument</code>: 当前 PDF 文档对象</li>
    </ul>

    <h3>私有属性</h3>
    <ul>
      <li><code>#layersMap</code>: 图层映射对象</li>
      <li><code>#renderedCapability</code>: 渲染完成的延迟对象</li>
      <li><code>#checkboxes</code>: 复选框元素集合</li>
      <li><code>#layerChangedListener</code>: 图层变化事件监听器</li>
      <li><code>#groupsIdKeys</code>: 组 ID 键集合</li>
      <li><code>#groupsBaseName</code>: 组基本名称</li>
      <li><code>#hasActiveSearch</code>: 是否有活动搜索的标志</li>
    </ul>
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法</h2>
    
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">构造函数</h3>
      </div>
      <div class="method-content">
        <p>创建一个新的 PDFLayerViewer 实例。</p>
        <pre><code class="language-javascript">
constructor({ container, eventBus, l10n = undefined })
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>container</code>: 图层容器元素</li>
          <li><code>eventBus</code>: 事件总线</li>
          <li><code>l10n</code>: 本地化对象 (可选)</li>
        </ul>
      </div>
    </div>

    <h3>核心方法</h3>
    <ul>
      <li><code>reset()</code>: 重置图层视图到初始状态</li>
      <li><code>async render({ optionalContentConfig, pdfDocument })</code>: 渲染 PDF 文档的图层内容</li>
      <li><code>_dispatchEvent(layersCount)</code>: 分发图层已加载事件</li>
      <li><code>_bindCheckboxes(container, { name, uniqueKey, isRoot, configs })</code>: 绑定复选框交互事件</li>
      <li><code>_addToggleButton(div, { group })</code>: 添加切换按钮到图层组</li>
      <li><code>_collectLayers(optionalContentConfig, { startIdx = 0, groupIds = [], skipVisibilityCheck = false } = {})</code>: 收集文档图层信息</li>
      <li><code>_createLayer({ name, id, configIdKey, parent, depth, checked, uncheckedGrandChildren = null, children = [], uniqueKey })</code>: 创建图层项</li>
      <li><code>_restoreLayerVisibility()</code>: 恢复图层可见性状态</li>
      <li><code>updateState()</code>: 更新图层状态</li>
    </ul>
    
    <h3>私有方法</h3>
    <ul>
      <li><code>#handleEvent()</code>: 处理事件</li>
      <li><code>#toggleLayer(event)</code>: 切换图层显示状态</li>
      <li><code>#setLayerVisibility(id, visible = true)</code>: 设置图层可见性</li>
      <li><code>#toggleAllLayers(checked = false)</code>: 切换所有图层的显示状态</li>
      <li><code>#removeMarkedLayers()</code>: 移除标记的图层</li>
      <li><code>#storeRestoreDetails()</code>: 存储和恢复图层详细信息</li>
    </ul>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <h3>图层初始化与渲染流程</h3>
    <p>1. 创建 PDFLayerViewer 实例<br>
    2. 调用 <code>render()</code> 方法设置文档图层数据<br>
    3. 解析图层结构<br>
    4. 生成 DOM 元素并添加到容器<br>
    5. 绑定交互事件<br>
    6. 分发图层加载完成事件</p>
    
    <div class="mermaid">
      graph TD
          A[创建 PDFLayerViewer 实例] --> B[调用 render 方法]
          B --> C[解析图层结构]
          C --> D[生成图层项 DOM 元素]
          D --> E[为每个图层添加复选框事件]
          D --> F[为图层组添加展开/折叠按钮]
          E --> G[分发图层加载完成事件]
          F --> G
    </div>
    
    <h3>图层切换流程</h3>
    <p>1. 用户点击图层复选框<br>
    2. 触发 <code>#toggleLayer</code> 方法处理点击事件<br>
    3. 更新可选内容配置<br>
    4. 分发图层变化事件<br>
    5. PDF 查看器重新渲染页面</p>
    
    <div class="mermaid">
      graph TD
          A[用户点击图层复选框] --> B[调用 #toggleLayer 方法]
          B --> C[获取图层ID]
          C --> D[更新可选内容配置]
          D --> E[分发 optionalcontentconfigchanged 事件]
          E --> F[PDF 查看器重新渲染页面]
    </div>
    
    <h3>图层组展开/折叠流程</h3>
    <p>1. 用户点击图层组展开/折叠按钮<br>
    2. 切换子图层的显示状态<br>
    3. 更新按钮图标</p>
    
    <div class="mermaid">
      graph TD
          A[用户点击图层组展开/折叠按钮] --> B[获取图层组元素]
          B --> C{当前状态是否为折叠?}
          C -->|是| D[显示子图层]
          C -->|否| E[隐藏子图层]
          D --> F[更新按钮图标]
          E --> F
    </div>
  </div>

  <!-- 示例 -->
  <div id="examples">
    <h2>使用示例</h2>
    
    <h3>基本用法</h3>
    <pre><code class="language-javascript">
// 创建 PDFLayerViewer 实例
const layerViewer = new PDFLayerViewer({
  container: document.getElementById('layerView'),
  eventBus: eventBus
});

// 加载文档时渲染图层
const loadingTask = pdfjsLib.getDocument('document.pdf');
loadingTask.promise.then(function(pdfDocument) {
  pdfDocument.getOptionalContentConfig().then(function(optionalContentConfig) {
    layerViewer.render({ optionalContentConfig, pdfDocument });
  });
});
    </code></pre>
    
    <h3>监听图层变化事件</h3>
    <pre><code class="language-javascript">
// 监听图层变化事件
eventBus.on("optionalcontentconfigchanged", function(evt) {
  console.log("图层配置已更改");
  
  // 更新渲染
  pdfViewer.optionalContentConfigPromise = Promise.resolve(evt.optionalContentConfig);
  pdfViewer.update();
});

// 监听图层加载事件
eventBus.on("layersloaded", function(evt) {
  console.log(`文档包含 ${evt.layersCount} 个图层`);
  
  // 根据是否有图层更新 UI
  if (evt.layersCount > 0) {
    layerButton.classList.remove("hidden");
  } else {
    layerButton.classList.add("hidden");
  }
});
    </code></pre>
    
    <h3>批量操作图层</h3>
    <pre><code class="language-javascript">
// 显示所有图层
document.getElementById('showAllLayers').addEventListener('click', function() {
  layerViewer.#toggleAllLayers(true);
});

// 隐藏所有图层
document.getElementById('hideAllLayers').addEventListener('click', function() {
  layerViewer.#toggleAllLayers(false);
});
    </code></pre>
  </div>

  <!-- 注意事项 -->
  <div id="notes">
    <h2>注意事项</h2>
    
    <ul>
      <li>图层功能依赖于 PDF 文档本身包含的可选内容组（OCG）。如果 PDF 文档没有定义图层，则渲染方法不会生成任何内容。</li>
      <li>可选内容配置的更改需要触发页面重新渲染，这可能在大型文档中导致性能问题。</li>
      <li>图层的层级结构可能很复杂，包含多级嵌套的组和子图层，需要适当的 UI 设计来展示这种层级关系。</li>
      <li>某些 PDF 文档可能包含大量图层（如 CAD 转换的 PDF），这可能会导致图层面板变得非常长，需要考虑可用性和性能优化。</li>
      <li>图层的可见性状态可能受到文档初始设置或阅读器应用的默认设置影响，需要适当处理初始状态。</li>
      <li>在某些复杂的 PDF 文档中，图层的变化可能会影响交互式功能（如注释或表单），需要综合考虑这些影响。</li>
    </ul>
  </div>

  <script>
    // 在页面加载完成后初始化 Mermaid
    document.addEventListener('DOMContentLoaded', function() {
      mermaid.initialize({ startOnLoad: true });
      
      // 生成目录
      generateTOC();
    });
  </script>
</body>
</html> 