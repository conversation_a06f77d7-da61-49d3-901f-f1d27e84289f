<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PDFCursorTools - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#constants">常量</a>
    <a href="#properties">属性</a>
    <a href="#methods">方法</a>
    <a href="#flowcharts">流程图</a>
    <a href="#examples">示例</a>
    <a href="#notes">注意事项</a>
  </div>

  <h1>PDFCursorTools 模块文档</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>PDFCursorTools 是 PDF.js 库中提供不同交互模式的组件，负责管理用户与 PDF 文档交互时的光标行为和工具选择。它支持多种交互模式，如选择工具、手形工具和放大镜工具，使用户能够根据需要选择不同的文档浏览和交互方式。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 常量 -->
  <div id="constants">
    <h2>常量</h2>
    
    <p>PDFCursorTools 使用以下常量：</p>
    
    <ul>
      <li>
        <code>CursorTool</code>: 光标工具枚举
        <ul>
          <li><code>SELECT</code>: 选择工具，用于文本选择和一般交互</li>
          <li><code>HAND</code>: 手形工具，用于拖动和平移文档</li>
          <li><code>ZOOM</code>: 缩放工具，用于放大和缩小文档视图</li>
        </ul>
      </li>
      <li>
        <code>PanOnlyMode</code>: 仅平移模式枚举
        <ul>
          <li><code>DISABLED</code>: 禁用仅平移模式</li>
          <li><code>SIMPLE</code>: 简单仅平移模式</li>
          <li><code>SMART</code>: 智能仅平移模式</li>
        </ul>
      </li>
    </ul>
  </div>

  <!-- 属性 -->
  <div id="properties">
    <h2>属性</h2>
    
    <h3>公共属性</h3>
    <ul>
      <li><code>activeTool</code>: 当前激活的工具</li>
      <li><code>handTool</code>: 手形工具实例</li>
    </ul>

    <h3>私有属性</h3>
    <ul>
      <li><code>#container</code>: 容器元素</li>
      <li><code>#eventBus</code>: 事件总线</li>
      <li><code>#activeTool</code>: 内部当前激活的工具</li>
      <li><code>#defaultCursor</code>: 默认光标样式</li>
      <li><code>#handTool</code>: 内部手形工具实例</li>
      <li><code>#panOnlyMode</code>: 仅平移模式设置</li>
      <li><code>#shadowSelectionModeEnabled</code>: 阴影选择模式是否启用</li>
      <li><code>#allowTextSelection</code>: 是否允许文本选择</li>
    </ul>
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法</h2>
    
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">构造函数</h3>
      </div>
      <div class="method-content">
        <p>创建一个新的 PDFCursorTools 实例。</p>
        <pre><code class="language-javascript">
constructor({
  container,
  eventBus,
  cursorToolOnLoad = CursorTool.SELECT,
  panOnlyMode = PanOnlyMode.DISABLED,
  disableTextLayer = false,
})
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>container</code>: 容器元素</li>
          <li><code>eventBus</code>: 事件总线</li>
          <li><code>cursorToolOnLoad</code>: 加载时使用的光标工具 (可选，默认为 CursorTool.SELECT)</li>
          <li><code>panOnlyMode</code>: 仅平移模式设置 (可选，默认为 PanOnlyMode.DISABLED)</li>
          <li><code>disableTextLayer</code>: 是否禁用文本层 (可选，默认为 false)</li>
        </ul>
      </div>
    </div>

    <h3>核心方法</h3>
    <ul>
      <li><code>switchTool(tool)</code>: 切换当前工具</li>
      <li><code>switchCursor(cursor)</code>: 切换光标样式</li>
      <li><code>setPanOnlyMode(mode)</code>: 设置仅平移模式</li>
      <li><code>toggleShadowSelectionMode(enabled)</code>: 切换阴影选择模式</li>
      <li><code>setDisableTextSelection(disabled)</code>: 设置是否禁用文本选择</li>
      <li><code>destroy()</code>: 销毁光标工具对象</li>
    </ul>
    
    <h3>私有方法</h3>
    <ul>
      <li><code>#addEventListeners()</code>: 添加事件监听器</li>
      <li><code>#removeEventListeners()</code>: 移除事件监听器</li>
      <li><code>#handleSwitchTool(event)</code>: 处理切换工具事件</li>
      <li><code>#selectAllText(event)</code>: 选择所有文本</li>
      <li><code>#applyCursor()</code>: 应用光标样式</li>
      <li><code>#setActiveTool(tool)</code>: 设置当前激活的工具</li>
      <li><code>#enableTextSelection()</code>: 启用文本选择</li>
      <li><code>#disableTextSelection()</code>: 禁用文本选择</li>
      <li><code>#shouldEnablePanning(event)</code>: 判断是否应启用平移</li>
      <li><code>#onPointerDown(event)</code>: 处理指针按下事件</li>
      <li><code>#onPointerUp(event)</code>: 处理指针抬起事件</li>
      <li><code>#onWheel(event)</code>: 处理滚轮事件</li>
    </ul>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <h3>工具切换流程</h3>
    <p>1. 用户选择工具或触发工具切换事件<br>
    2. 调用 <code>switchTool()</code> 方法<br>
    3. 设置活动工具<br>
    4. 更新光标样式<br>
    5. 根据工具启用或禁用相关功能</p>
    
    <div class="mermaid">
      graph TD
          A[用户选择工具] --> B[调用 switchTool 方法]
          B --> C[设置活动工具]
          C --> D{工具类型是什么?}
          D -->|SELECT| E[启用文本选择]
          D -->|HAND| F[禁用文本选择并启用拖动]
          D -->|ZOOM| G[设置缩放光标]
          E --> H[更新光标样式]
          F --> H
          G --> H
          H --> I[触发 cursortoolchanged 事件]
    </div>
    
    <h3>平移模式处理流程</h3>
    <p>1. 用户交互（鼠标或触摸）<br>
    2. 触发指针事件<br>
    3. 检查平移模式设置<br>
    4. 根据模式决定是否启用临时平移<br>
    5. 处理平移操作</p>
    
    <div class="mermaid">
      graph TD
          A[用户交互] --> B[触发指针事件]
          B --> C[调用 #onPointerDown 方法]
          C --> D{当前平移模式?}
          D -->|DISABLED| E[使用当前工具处理]
          D -->|SIMPLE| F[始终启用平移]
          D -->|SMART| G[调用 #shouldEnablePanning 方法]
          G --> H{是否应启用平移?}
          H -->|是| I[临时启用平移]
          H -->|否| J[使用当前工具处理]
          F --> I
          I --> K[处理平移操作]
          J --> L[处理标准交互]
          E --> L
    </div>
    
    <h3>文本选择控制流程</h3>
    <p>1. 设置文本选择状态<br>
    2. 根据状态启用或禁用文本选择<br>
    3. 更新 CSS 类和事件处理<br>
    4. 应用相应的交互行为</p>
    
    <div class="mermaid">
      graph TD
          A[设置文本选择状态] --> B{是否禁用文本选择?}
          B -->|是| C[调用 #disableTextSelection 方法]
          B -->|否| D[调用 #enableTextSelection 方法]
          C --> E[添加 .textLayer--disable 类]
          D --> F[移除 .textLayer--disable 类]
          E --> G[更新事件处理]
          F --> G
          G --> H[应用相应的交互行为]
    </div>
  </div>

  <!-- 示例 -->
  <div id="examples">
    <h2>使用示例</h2>
    
    <h3>基本用法</h3>
    <pre><code class="language-javascript">
// 创建 PDFCursorTools 实例
const cursorTools = new PDFCursorTools({
  container: document.getElementById('viewerContainer'),
  eventBus: eventBus,
  // 默认使用选择工具
  cursorToolOnLoad: CursorTool.SELECT
});

// 添加工具切换按钮事件处理
document.getElementById('selectTool').addEventListener('click', function() {
  cursorTools.switchTool(CursorTool.SELECT);
});

document.getElementById('handTool').addEventListener('click', function() {
  cursorTools.switchTool(CursorTool.HAND);
});

document.getElementById('zoomTool').addEventListener('click', function() {
  cursorTools.switchTool(CursorTool.ZOOM);
});

// 监听工具变化事件
eventBus.on('cursortoolchanged', function(evt) {
  // 更新 UI 显示当前选中的工具
  const toolButtons = {
    [CursorTool.SELECT]: document.getElementById('selectTool'),
    [CursorTool.HAND]: document.getElementById('handTool'),
    [CursorTool.ZOOM]: document.getElementById('zoomTool')
  };
  
  // 移除所有按钮的活动状态
  Object.values(toolButtons).forEach(button => {
    button.classList.remove('active');
  });
  
  // 设置当前工具按钮为活动状态
  if (toolButtons[evt.tool]) {
    toolButtons[evt.tool].classList.add('active');
  }
});
    </code></pre>
    
    <h3>平移模式控制</h3>
    <pre><code class="language-javascript">
// 设置平移模式
const panModeSelect = document.getElementById('panModeSelect');
panModeSelect.addEventListener('change', function() {
  const mode = parseInt(this.value, 10);
  cursorTools.setPanOnlyMode(mode);
  
  // 根据模式更新 UI 提示
  const modeDescriptions = {
    [PanOnlyMode.DISABLED]: "标准模式: 工具行为正常",
    [PanOnlyMode.SIMPLE]: "简单平移模式: 总是使用手形工具",
    [PanOnlyMode.SMART]: "智能平移模式: 在适当时自动切换到手形工具"
  };
  
  document.getElementById('panModeDescription').textContent = modeDescriptions[mode];
});

// 初始化选择框值
panModeSelect.value = PanOnlyMode.DISABLED;
panModeSelect.dispatchEvent(new Event('change'));

// 智能平移模式示例 - 自定义何时启用平移
// 通过扩展 PDFCursorTools 类
class CustomPDFCursorTools extends PDFCursorTools {
  constructor(options) {
    super(options);
  }
  
  // 覆盖 shouldEnablePanning 方法以提供自定义逻辑
  _shouldEnablePanning(event) {
    // 在 Alt 键按下时始终启用平移，无论当前工具是什么
    if (event.altKey) {
      return true;
    }
    
    // 在选择工具模式下，仅当按下中键或右键时启用平移
    if (this.activeTool === CursorTool.SELECT) {
      return event.button === 1 || event.button === 2;
    }
    
    // 对于其他工具，使用默认行为
    return super._shouldEnablePanning(event);
  }
}

// 使用自定义光标工具类
const customCursorTools = new CustomPDFCursorTools({
  container: document.getElementById('viewerContainer'),
  eventBus: eventBus,
  panOnlyMode: PanOnlyMode.SMART
});
    </code></pre>
    
    <h3>文本选择控制</h3>
    <pre><code class="language-javascript">
// 添加文本选择控制
const textSelectionCheckbox = document.getElementById('enableTextSelection');

textSelectionCheckbox.addEventListener('change', function() {
  // 设置是否禁用文本选择
  cursorTools.setDisableTextSelection(!this.checked);
  
  // 更新 UI 状态
  if (this.checked) {
    console.log('文本选择已启用');
    // 可以在这里执行其他相关操作，如显示文本相关工具栏等
  } else {
    console.log('文本选择已禁用');
    // 可以在这里执行其他相关操作，如隐藏文本相关工具栏等
  }
});

// 阴影选择模式控制
const shadowSelectionCheckbox = document.getElementById('enableShadowSelection');

shadowSelectionCheckbox.addEventListener('change', function() {
  cursorTools.toggleShadowSelectionMode(this.checked);
  
  if (this.checked) {
    console.log('阴影选择模式已启用');
  } else {
    console.log('阴影选择模式已禁用');
  }
});

// 根据当前工具自动更新文本选择控制状态
eventBus.on('cursortoolchanged', function(evt) {
  // 当工具为手形工具或缩放工具时，文本选择通常会被禁用
  const isTextSelectionDisabled = (evt.tool !== CursorTool.SELECT);
  
  // 更新复选框状态，但不触发 change 事件
  textSelectionCheckbox.checked = !isTextSelectionDisabled;
});
    </code></pre>
  </div>

  <!-- 注意事项 -->
  <div id="notes">
    <h2>注意事项</h2>
    
    <ul>
      <li>不同浏览器和操作系统对自定义光标的支持可能有所不同，特别是自定义缩放光标的显示可能会有差异。</li>
      <li>在触摸设备上，光标工具的行为可能需要调整，以适应触摸交互的特性。例如，在触摸设备上通常没有明确的"悬停"状态。</li>
      <li>平移模式的智能检测（PanOnlyMode.SMART）依赖于事件处理逻辑，可能需要根据具体的用户场景进行调整和优化。</li>
      <li>在禁用文本选择时，应确保用户仍然可以通过其他方式访问文档内容，以保持可访问性。</li>
      <li>当用户从一个工具切换到另一个工具时，应确保之前工具的状态被正确清理，以避免交互冲突。</li>
      <li>对于具有复杂布局的 PDF 文档，文本选择和工具交互可能不如简单文档那样直观。考虑提供额外的视觉反馈或帮助提示。</li>
      <li>当使用缩放工具时，应考虑性能问题，特别是在大型文档或低性能设备上。可能需要实现节流或防抖动技术来优化性能。</li>
      <li>手形工具在使用时可能会与浏览器的默认拖动行为冲突。确保正确阻止默认事件以获得预期的行为。</li>
    </ul>
  </div>

  <script>
    // 在页面加载完成后初始化 Mermaid
    document.addEventListener('DOMContentLoaded', function() {
      mermaid.initialize({ startOnLoad: true });
      
      // 生成目录
      generateTOC();
    });
  </script>
</body>
</html> 