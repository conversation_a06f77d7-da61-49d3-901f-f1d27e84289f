<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PDF.js API 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
</head>
<body>
  <header>
    <h1>PDF.js API 文档</h1>
    <p class="module-description">PDF.js核心API，用于PDF文档的加载、渲染和操作</p>
  </header>

  <main>
    <section class="module-intro">
      <h2>模块介绍</h2>
      <p>PDF.js API是与PDF文档交互的核心接口集合，提供了加载PDF文件、访问文档内容、渲染页面和操作文档结构的能力。这套API被PDF.js的所有其他组件所依赖，是构建PDF查看器和处理PDF文件的基础。</p>
      
      <div class="info-box">
        <p>PDF.js API可以分为几个主要部分：</p>
        <ul>
          <li>文档加载与管理</li>
          <li>页面渲染与操作</li>
          <li>文档内容访问</li>
          <li>工作线程管理</li>
          <li>辅助工具与类型</li>
        </ul>
      </div>
    </section>

    <section class="module-architecture">
      <h2>架构图</h2>
      <div class="architecture-diagram">
        <!-- 流程图将由JavaScript动态生成 -->
      </div>
    </section>

    <section class="module-api">
      <h2>API文档</h2>
      
      <div class="api-section">
        <h3>文档加载与管理</h3>
        
        <div class="api-item">
          <h4>getDocument(params)</h4>
          <p>加载PDF文档并返回一个PDFDocumentLoadingTask对象</p>
          <div class="code-example">
            <pre><code>const loadingTask = pdfjsLib.getDocument({
  url: 'document.pdf',
  cMapUrl: 'cmaps/',
  cMapPacked: true
});

loadingTask.promise.then(pdfDocument => {
  // 文档加载成功，pdfDocument是PDFDocumentProxy实例
}).catch(error => {
  // 处理加载错误
});</code></pre>
          </div>
          <div class="param-list">
            <h5>参数</h5>
            <table>
              <tr>
                <th>名称</th>
                <th>类型</th>
                <th>描述</th>
              </tr>
              <tr>
                <td>url</td>
                <td>string</td>
                <td>PDF文档的URL</td>
              </tr>
              <tr>
                <td>data</td>
                <td>TypedArray</td>
                <td>包含PDF数据的类型化数组</td>
              </tr>
              <tr>
                <td>cMapUrl</td>
                <td>string</td>
                <td>字符映射表目录的URL</td>
              </tr>
              <tr>
                <td>cMapPacked</td>
                <td>boolean</td>
                <td>是否使用压缩的字符映射表</td>
              </tr>
              <tr>
                <td>worker</td>
                <td>PDFWorker</td>
                <td>用于处理PDF的工作线程</td>
              </tr>
              <tr>
                <td>范围</td>
                <td>PDFDataRangeTransport</td>
                <td>用于PDF数据范围传输的对象</td>
              </tr>
              <tr>
                <td>withCredentials</td>
                <td>boolean</td>
                <td>是否使用凭据进行跨域请求</td>
              </tr>
              <tr>
                <td>password</td>
                <td>string</td>
                <td>用于加密文档的密码</td>
              </tr>
            </table>
          </div>
        </div>

        <div class="api-item">
          <h4>PDFDocumentProxy</h4>
          <p>表示已加载的PDF文档，提供访问文档属性和页面的方法</p>
          <div class="method-list">
            <h5>方法</h5>
            <ul>
              <li><strong>getPage(pageNumber)</strong> - 获取指定页码的页面对象</li>
              <li><strong>getPageIndex(ref)</strong> - 根据引用获取页面索引</li>
              <li><strong>getPageLabels()</strong> - 获取页面标签</li>
              <li><strong>getMetadata()</strong> - 获取文档元数据</li>
              <li><strong>getDestinations()</strong> - 获取文档中的目标位置</li>
              <li><strong>getAttachments()</strong> - 获取文档附件</li>
              <li><strong>getJavaScript()</strong> - 获取文档中的JavaScript</li>
              <li><strong>getOutline()</strong> - 获取文档大纲</li>
              <li><strong>getPermissions()</strong> - 获取文档权限</li>
              <li><strong>getDownloadInfo()</strong> - 获取下载信息</li>
              <li><strong>getStats()</strong> - 获取文档统计信息</li>
              <li><strong>getData()</strong> - 获取文档数据</li>
              <li><strong>saveDocument()</strong> - 保存文档</li>
            </ul>
          </div>
          <div class="property-list">
            <h5>属性</h5>
            <ul>
              <li><strong>numPages</strong> - 文档页数</li>
              <li><strong>fingerprints</strong> - 文档指纹</li>
              <li><strong>annotationStorage</strong> - 注释存储</li>
              <li><strong>isPureXfa</strong> - 是否为纯XFA文档</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="api-section">
        <h3>页面渲染与操作</h3>
        
        <div class="api-item">
          <h4>PDFPageProxy</h4>
          <p>表示PDF文档中的单个页面，提供渲染和获取页面内容的方法</p>
          <div class="method-list">
            <h5>方法</h5>
            <ul>
              <li><strong>getViewport(options)</strong> - 获取页面视口</li>
              <li><strong>render(options)</strong> - 渲染页面</li>
              <li><strong>getTextContent(options)</strong> - 获取页面文本内容</li>
              <li><strong>getAnnotations(options)</strong> - 获取页面注释</li>
              <li><strong>getOperatorList()</strong> - 获取页面操作符列表</li>
              <li><strong>getJSActions()</strong> - 获取页面JavaScript动作</li>
            </ul>
          </div>
          <div class="property-list">
            <h5>属性</h5>
            <ul>
              <li><strong>pageNumber</strong> - 页码</li>
              <li><strong>rotate</strong> - 页面旋转角度</li>
              <li><strong>ref</strong> - 页面引用</li>
              <li><strong>userUnit</strong> - 用户单位</li>
              <li><strong>view</strong> - 页面视图</li>
            </ul>
          </div>
        </div>
        
        <div class="api-item">
          <h4>TextLayer</h4>
          <p>用于在PDF页面上渲染文本层</p>
          <div class="method-list">
            <h5>方法</h5>
            <ul>
              <li><strong>render(options)</strong> - 渲染文本层</li>
              <li><strong>hide()</strong> - 隐藏文本层</li>
              <li><strong>show()</strong> - 显示文本层</li>
            </ul>
          </div>
        </div>
        
        <div class="api-item">
          <h4>XfaLayer</h4>
          <p>用于在PDF页面上渲染XFA表单层</p>
          <div class="method-list">
            <h5>方法</h5>
            <ul>
              <li><strong>render(options)</strong> - 渲染XFA层</li>
              <li><strong>update(options)</strong> - 更新XFA层</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="api-section">
        <h3>工作线程管理</h3>
        
        <div class="api-item">
          <h4>GlobalWorkerOptions</h4>
          <p>配置PDF.js工作线程的全局选项</p>
          <div class="property-list">
            <h5>属性</h5>
            <ul>
              <li><strong>workerSrc</strong> - 工作线程脚本的URL</li>
              <li><strong>workerPort</strong> - 与工作线程通信的端口</li>
            </ul>
          </div>
          <div class="code-example">
            <pre><code>pdfjsLib.GlobalWorkerOptions.workerSrc = 'pdf.worker.js';</code></pre>
          </div>
        </div>
        
        <div class="api-item">
          <h4>PDFWorker</h4>
          <p>用于处理PDF数据的工作线程</p>
          <div class="method-list">
            <h5>方法</h5>
            <ul>
              <li><strong>constructor(options)</strong> - 创建新的工作线程</li>
              <li><strong>destroy()</strong> - 销毁工作线程</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="api-section">
        <h3>辅助工具与类型</h3>
        
        <div class="api-item">
          <h4>PermissionFlag</h4>
          <p>表示PDF文档权限的常量</p>
          <div class="property-list">
            <h5>属性</h5>
            <ul>
              <li><strong>PRINT</strong> - 打印权限</li>
              <li><strong>MODIFY_CONTENTS</strong> - 修改内容权限</li>
              <li><strong>COPY</strong> - 复制权限</li>
              <li><strong>MODIFY_ANNOTATIONS</strong> - 修改注释权限</li>
              <li><strong>FILL_INTERACTIVE_FORMS</strong> - 填写表单权限</li>
              <li><strong>COPY_FOR_ACCESSIBILITY</strong> - 无障碍复制权限</li>
              <li><strong>ASSEMBLE</strong> - 组装文档权限</li>
              <li><strong>PRINT_HIGH_QUALITY</strong> - 高质量打印权限</li>
            </ul>
          </div>
        </div>
        
        <div class="api-item">
          <h4>InvalidPDFException</h4>
          <p>表示无效PDF文件的异常</p>
        </div>
        
        <div class="api-item">
          <h4>PasswordResponses</h4>
          <p>密码响应常量</p>
          <div class="property-list">
            <h5>属性</h5>
            <ul>
              <li><strong>NEED_PASSWORD</strong> - 需要密码</li>
              <li><strong>INCORRECT_PASSWORD</strong> - 密码不正确</li>
            </ul>
          </div>
        </div>
        
        <div class="api-item">
          <h4>PDFDateString</h4>
          <p>用于解析和格式化PDF日期字符串</p>
        </div>
        
        <div class="api-item">
          <h4>OutputScale</h4>
          <p>用于处理屏幕与PDF缩放比例的转换</p>
        </div>
      </div>
    </section>

    <section class="module-usage">
      <h2>使用示例</h2>
      
      <div class="example-section">
        <h3>基本使用</h3>
        <div class="code-example">
          <pre><code>// 设置工作线程路径
pdfjsLib.GlobalWorkerOptions.workerSrc = 'pdf.worker.js';

// 加载PDF文档
const loadingTask = pdfjsLib.getDocument('document.pdf');

// 处理加载过程
loadingTask.onProgress = ({ loaded, total }) => {
  const percent = (loaded / total * 100).toFixed(2);
  console.log(`加载进度: ${percent}%`);
};

// 处理加载完成
loadingTask.promise.then(pdfDocument => {
  console.log(`PDF加载完成，共${pdfDocument.numPages}页`);
  
  // 获取第一页
  return pdfDocument.getPage(1);
}).then(pdfPage => {
  // 创建渲染上下文
  const viewport = pdfPage.getViewport({ scale: 1.5 });
  const canvas = document.getElementById('pdf-canvas');
  const context = canvas.getContext('2d');
  
  // 设置画布尺寸
  canvas.height = viewport.height;
  canvas.width = viewport.width;
  
  // 渲染页面
  const renderTask = pdfPage.render({
    canvasContext: context,
    viewport: viewport
  });
  
  return renderTask.promise;
}).catch(error => {
  console.error('PDF处理错误:', error);
});</code></pre>
        </div>
      </div>
      
      <div class="example-section">
        <h3>文本提取</h3>
        <div class="code-example">
          <pre><code>// 获取页面后提取文本
pdfDocument.getPage(1).then(pdfPage => {
  return pdfPage.getTextContent();
}).then(textContent => {
  // 处理提取的文本内容
  const textItems = textContent.items;
  let text = '';
  
  for (const item of textItems) {
    text += item.str + ' ';
  }
  
  console.log('提取的文本:', text);
}).catch(error => {
  console.error('文本提取错误:', error);
});</code></pre>
        </div>
      </div>
      
      <div class="example-section">
        <h3>离线使用</h3>
        <div class="code-example">
          <pre><code>// 预加载PDF数据
fetch('document.pdf')
  .then(response => response.arrayBuffer())
  .then(arrayBuffer => {
    // 使用ArrayBuffer加载PDF
    return pdfjsLib.getDocument({ data: new Uint8Array(arrayBuffer) }).promise;
  })
  .then(pdfDocument => {
    // 使用加载的文档
    console.log('PDF已加载，可以离线使用');
  });</code></pre>
        </div>
      </div>
    </section>

    <section class="module-execution-flow">
      <h2>执行流程</h2>
      
      <div class="flow-section">
        <h3>PDF文档加载到渲染的执行顺序</h3>
        <p>PDF.js在viewer.mjs中的文档加载到渲染的完整流程如下：</p>
        
        <div class="flow-diagram">
          <div class="mermaid">
          sequenceDiagram
            participant App as PDFViewerApplication
            participant Task as LoadingTask
            participant Doc as PDFDocumentProxy
            participant Page as PDFPageProxy
            participant View as PDFPageView
            participant Worker as PDFWorker
            
            App->>App: 初始化应用程序
            App->>Worker: 设置GlobalWorkerOptions.workerSrc
            App->>Task: 调用getDocument创建加载任务
            Note over Task: 创建PDF加载任务
            
            App->>Task: 设置onProgress回调
            App->>Task: 设置onPassword回调
            
            Task->>Worker: 启动文档加载
            Worker-->>Task: 处理文档数据
            
            Task-->>App: 通过promise返回PDFDocumentProxy
            App->>Doc: 获取文档属性(numPages等)
            App->>Doc: 调用getPage获取第一页
            
            Doc->>Page: 创建PDFPageProxy
            App->>View: 创建PDFPageView
            App->>View: 设置视口(viewport)
            View->>Page: 调用render方法
            
            Page->>Worker: 获取页面操作符列表
            Worker-->>Page: 返回渲染指令
            
            Page->>View: 渲染到canvas
            Note over View: 创建和添加文本层
            Note over View: 创建和添加注释层
            
            View-->>App: 分发pagerendered事件
            App->>App: 处理页面渲染完成
          </div>
        </div>
        
        <div class="execution-details">
          <h4>详细步骤说明</h4>
          <ol>
            <li><strong>初始化</strong>
              <ul>
                <li>设置PDF.js工作线程路径: <code>GlobalWorkerOptions.workerSrc</code></li>
                <li>配置应用参数: <code>AppOptions.set()</code></li>
              </ul>
            </li>
            <li><strong>文档加载</strong>
              <ul>
                <li>创建加载任务: <code>getDocument()</code></li>
                <li>设置回调函数: <code>onProgress</code>, <code>onPassword</code></li>
                <li>加载文档: <code>loadingTask.promise</code></li>
              </ul>
            </li>
            <li><strong>文档准备</strong>
              <ul>
                <li>获取文档属性: <code>pdfDocument.numPages</code>, <code>pdfDocument.getDownloadInfo()</code></li>
                <li>获取页面布局: <code>pdfDocument.getPageLayout()</code></li>
                <li>获取页面模式: <code>pdfDocument.getPageMode()</code></li>
                <li>获取打开动作: <code>pdfDocument.getOpenAction()</code></li>
              </ul>
            </li>
            <li><strong>视图初始化</strong>
              <ul>
                <li>设置页数: <code>toolbar.setPagesCount()</code></li>
                <li>设置文档链接: <code>pdfLinkService.setDocument()</code></li>
                <li>初始化查看器: <code>pdfViewer.setDocument()</code></li>
                <li>初始化缩略图: <code>pdfThumbnailViewer.setDocument()</code></li>
              </ul>
            </li>
            <li><strong>页面渲染</strong>
              <ul>
                <li>获取页面对象: <code>pdfDocument.getPage()</code></li>
                <li>创建视口: <code>page.getViewport()</code></li>
                <li>渲染页面: <code>page.render()</code></li>
                <li>添加文本层: <code>TextLayer.render()</code></li>
                <li>添加注释层: <code>AnnotationLayer.render()</code></li>
              </ul>
            </li>
            <li><strong>完成与交互</strong>
              <ul>
                <li>发送渲染完成事件: <code>eventBus.dispatch("pagerendered")</code></li>
                <li>隐藏加载UI: <code>loadingBar.hide()</code></li>
                <li>发送文档加载完成事件: <code>eventBus.dispatch("documentloaded")</code></li>
              </ul>
            </li>
          </ol>
        </div>
      </div>
    </section>
    
    <section class="module-frameworks">
      <h2>在前端框架中使用</h2>
      
      <div class="framework-section">
        <h3>Vue2/Vue3中集成PDF.js</h3>
        <p>以下是在Vue项目中集成PDF.js，实现与viewer.mjs类似功能的步骤指南：</p>
        
        <div class="step-section">
          <h4>1. 安装依赖</h4>
          <div class="code-example">
            <pre><code>npm install pdfjs-dist</code></pre>
          </div>
          <p>或者使用CDN直接引入：</p>
          <div class="code-example">
            <pre><code>&lt;script src="https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/build/pdf.min.js"&gt;&lt;/script&gt;
&lt;script src="https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/build/pdf.worker.min.js"&gt;&lt;/script&gt;</code></pre>
          </div>
        </div>
        
        <div class="step-section">
          <h4>2. 创建PDF查看器组件 (Vue3 Composition API)</h4>
          <div class="code-example">
            <pre><code>&lt;template&gt;
  &lt;div class="pdf-container"&gt;
    &lt;div class="toolbar"&gt;
      &lt;button @click="prevPage" :disabled="currentPage <= 1"&gt;上一页&lt;/button&gt;
      &lt;span&gt;{{ currentPage }} / {{ totalPages }}&lt;/span&gt;
      &lt;button @click="nextPage" :disabled="currentPage >= totalPages"&gt;下一页&lt;/button&gt;
      &lt;select v-model="scale"&gt;
        &lt;option value="0.5"&gt;50%&lt;/option&gt;
        &lt;option value="1"&gt;100%&lt;/option&gt;
        &lt;option value="1.5"&gt;150%&lt;/option&gt;
        &lt;option value="2"&gt;200%&lt;/option&gt;
      &lt;/select&gt;
    &lt;/div&gt;
    &lt;div class="pdf-viewer"&gt;
      &lt;div v-for="page in visiblePages" :key="page" class="pdf-page-container"&gt;
        &lt;canvas :id="'pdf-page-' + page" class="pdf-page"&gt;&lt;/canvas&gt;
        &lt;div :id="'pdf-text-layer-' + page" class="pdf-text-layer"&gt;&lt;/div&gt;
        &lt;div :id="'pdf-annotation-layer-' + page" class="pdf-annotation-layer"&gt;&lt;/div&gt;
      &lt;/div&gt;
      &lt;div v-if="loading" class="loading-indicator"&gt;加载中...&lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script&gt;
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue';
import * as pdfjsLib from 'pdfjs-dist';
import { TextLayerBuilder } from 'pdfjs-dist/web/pdf_viewer';

export default {
  name: 'PDFViewer',
  props: {
    pdfUrl: {
      type: String,
      required: true
    }
  },
  setup(props) {
    // 设置PDF.js工作线程路径
    pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/build/pdf.worker.min.js';

    // 状态管理
    const pdfDocument = ref(null);
    const currentPage = ref(1);
    const totalPages = ref(0);
    const scale = ref(1);
    const loading = ref(true);
    const visiblePages = ref([1]);
    const pageRendering = reactive({});
    
    // 加载PDF文档
    const loadPdf = async () => {
      try {
        loading.value = true;
        
        // 创建PDF加载任务
        const loadingTask = pdfjsLib.getDocument(props.pdfUrl);
        
        // 设置加载进度回调
        loadingTask.onProgress = ({ loaded, total }) => {
          console.log(`加载进度: ${Math.round(loaded / total * 100)}%`);
        };
        
        // 加载文档
        pdfDocument.value = await loadingTask.promise;
        totalPages.value = pdfDocument.value.numPages;
        
        // 渲染当前页
        renderPage(currentPage.value);
      } catch (error) {
        console.error('加载PDF错误:', error);
      } finally {
        loading.value = false;
      }
    };
    
    // 渲染页面
    const renderPage = async (pageNumber) => {
      if (pageRendering[pageNumber]) {
        return;
      }
      
      pageRendering[pageNumber] = true;
      
      try {
        // 获取页面
        const page = await pdfDocument.value.getPage(pageNumber);
        
        // 设置视口
        const viewport = page.getViewport({ scale: parseFloat(scale.value) });
        
        // 获取画布和上下文
        const canvas = document.getElementById(`pdf-page-${pageNumber}`);
        const context = canvas.getContext('2d');
        
        // 设置画布尺寸
        canvas.height = viewport.height;
        canvas.width = viewport.width;
        
        // 渲染页面
        const renderContext = {
          canvasContext: context,
          viewport: viewport
        };
        
        const renderTask = page.render(renderContext);
        
        // 渲染文本层
        const textLayerDiv = document.getElementById(`pdf-text-layer-${pageNumber}`);
        textLayerDiv.style.height = viewport.height + 'px';
        textLayerDiv.style.width = viewport.width + 'px';
        
        const textContent = await page.getTextContent();
        const textLayer = new TextLayerBuilder({
          textLayerDiv: textLayerDiv,
          pageIndex: page.pageNumber - 1,
          viewport: viewport
        });
        
        textLayer.setTextContent(textContent);
        textLayer.render();
        
        // 渲染注释层
        const annotationLayerDiv = document.getElementById(`pdf-annotation-layer-${pageNumber}`);
        annotationLayerDiv.style.height = viewport.height + 'px';
        annotationLayerDiv.style.width = viewport.width + 'px';
        
        const annotations = await page.getAnnotations();
        pdfjsLib.AnnotationLayer.render({
          viewport: viewport.clone({ dontFlip: true }),
          div: annotationLayerDiv,
          annotations: annotations,
          page: page
        });
        
        // 等待渲染完成
        await renderTask.promise;
        
        console.log(`页面 ${pageNumber} 渲染完成`);
      } catch (error) {
        console.error(`渲染页面 ${pageNumber} 错误:`, error);
      } finally {
        pageRendering[pageNumber] = false;
      }
    };
    
    // 页面导航
    const prevPage = () => {
      if (currentPage.value > 1) {
        currentPage.value--;
        updateVisiblePages();
      }
    };
    
    const nextPage = () => {
      if (currentPage.value < totalPages.value) {
        currentPage.value++;
        updateVisiblePages();
      }
    };
    
    // 更新可见页面
    const updateVisiblePages = () => {
      // 简单实现，只显示当前页
      visiblePages.value = [currentPage.value];
      renderPage(currentPage.value);
    };
    
    // 观察缩放变化
    watch(scale, () => {
      // 重新渲染当前可见页面
      visiblePages.value.forEach(pageNumber => {
        renderPage(pageNumber);
      });
    });
    
    // 生命周期钩子
    onMounted(() => {
      loadPdf();
    });
    
    onUnmounted(() => {
      // 清理资源
      if (pdfDocument.value) {
        pdfDocument.value.destroy();
      }
    });
    
    return {
      currentPage,
      totalPages,
      scale,
      loading,
      visiblePages,
      prevPage,
      nextPage
    };
  }
}
&lt;/script&gt;

&lt;style scoped&gt;
.pdf-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.toolbar {
  padding: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
  background-color: #f5f5f5;
}

.pdf-viewer {
  flex: 1;
  overflow: auto;
  position: relative;
}

.pdf-page-container {
  position: relative;
  margin: 10px auto;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.pdf-page {
  display: block;
}

.pdf-text-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  opacity: 0.2;
  line-height: 1.0;
}

.pdf-text-layer > span {
  color: transparent;
  position: absolute;
  white-space: pre;
  cursor: text;
  transform-origin: 0% 0%;
}

.pdf-annotation-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.loading-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.8);
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}
&lt;/style&gt;</code></pre>
          </div>
        </div>
        
        <div class="step-section">
          <h4>3. Vue2 选项式 API 版本</h4>
          <div class="code-example">
            <pre><code>&lt;template&gt;
  &lt;!-- 模板与Vue3版本相同 --&gt;
&lt;/template&gt;

&lt;script&gt;
import * as pdfjsLib from 'pdfjs-dist';
import { TextLayerBuilder } from 'pdfjs-dist/web/pdf_viewer';

export default {
  name: 'PDFViewer',
  props: {
    pdfUrl: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      pdfDocument: null,
      currentPage: 1,
      totalPages: 0,
      scale: 1,
      loading: true,
      visiblePages: [1],
      pageRendering: {}
    };
  },
  mounted() {
    // 设置PDF.js工作线程路径
    pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/build/pdf.worker.min.js';
    this.loadPdf();
  },
  beforeDestroy() {
    // 清理资源
    if (this.pdfDocument) {
      this.pdfDocument.destroy();
    }
  },
  watch: {
    scale: function() {
      // 重新渲染当前可见页面
      this.visiblePages.forEach(pageNumber => {
        this.renderPage(pageNumber);
      });
    }
  },
  methods: {
    async loadPdf() {
      try {
        this.loading = true;
        
        // 创建PDF加载任务
        const loadingTask = pdfjsLib.getDocument(this.pdfUrl);
        
        // 设置加载进度回调
        loadingTask.onProgress = ({ loaded, total }) => {
          console.log(`加载进度: ${Math.round(loaded / total * 100)}%`);
        };
        
        // 加载文档
        this.pdfDocument = await loadingTask.promise;
        this.totalPages = this.pdfDocument.numPages;
        
        // 渲染当前页
        this.renderPage(this.currentPage);
      } catch (error) {
        console.error('加载PDF错误:', error);
      } finally {
        this.loading = false;
      }
    },
    
    async renderPage(pageNumber) {
      if (this.pageRendering[pageNumber]) {
        return;
      }
      
      this.pageRendering[pageNumber] = true;
      
      try {
        // 获取页面
        const page = await this.pdfDocument.getPage(pageNumber);
        
        // 设置视口
        const viewport = page.getViewport({ scale: parseFloat(this.scale) });
        
        // 获取画布和上下文
        const canvas = document.getElementById(`pdf-page-${pageNumber}`);
        const context = canvas.getContext('2d');
        
        // 设置画布尺寸
        canvas.height = viewport.height;
        canvas.width = viewport.width;
        
        // 渲染页面
        const renderContext = {
          canvasContext: context,
          viewport: viewport
        };
        
        const renderTask = page.render(renderContext);
        
        // 渲染文本层
        const textLayerDiv = document.getElementById(`pdf-text-layer-${pageNumber}`);
        textLayerDiv.style.height = viewport.height + 'px';
        textLayerDiv.style.width = viewport.width + 'px';
        
        const textContent = await page.getTextContent();
        const textLayer = new TextLayerBuilder({
          textLayerDiv: textLayerDiv,
          pageIndex: page.pageNumber - 1,
          viewport: viewport
        });
        
        textLayer.setTextContent(textContent);
        textLayer.render();
        
        // 渲染注释层
        const annotationLayerDiv = document.getElementById(`pdf-annotation-layer-${pageNumber}`);
        annotationLayerDiv.style.height = viewport.height + 'px';
        annotationLayerDiv.style.width = viewport.width + 'px';
        
        const annotations = await page.getAnnotations();
        pdfjsLib.AnnotationLayer.render({
          viewport: viewport.clone({ dontFlip: true }),
          div: annotationLayerDiv,
          annotations: annotations,
          page: page
        });
        
        // 等待渲染完成
        await renderTask.promise;
        
        console.log(`页面 ${pageNumber} 渲染完成`);
      } catch (error) {
        console.error(`渲染页面 ${pageNumber} 错误:`, error);
      } finally {
        this.pageRendering[pageNumber] = false;
      }
    },
    
    prevPage() {
      if (this.currentPage > 1) {
        this.currentPage--;
        this.updateVisiblePages();
      }
    },
    
    nextPage() {
      if (this.currentPage < this.totalPages) {
        this.currentPage++;
        this.updateVisiblePages();
      }
    },
    
    updateVisiblePages() {
      // 简单实现，只显示当前页
      this.visiblePages = [this.currentPage];
      this.renderPage(this.currentPage);
    }
  }
};
&lt;/script&gt;

&lt;!-- 样式与Vue3版本相同 --&gt;</code></pre>
          </div>
        </div>
        
        <div class="step-section">
          <h4>4. 在父组件中使用</h4>
          <div class="code-example">
            <pre><code>&lt;template&gt;
  &lt;div class="app"&gt;
    &lt;h1&gt;PDF查看器示例&lt;/h1&gt;
    &lt;PDFViewer :pdfUrl="pdfUrl" /&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script&gt;
import PDFViewer from './components/PDFViewer.vue';

export default {
  components: {
    PDFViewer
  },
  data() {
    return {
      pdfUrl: '/sample.pdf' // PDF文件URL
    };
  }
};
&lt;/script&gt;

&lt;style&gt;
.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

h1 {
  margin: 0;
  padding: 16px;
}
&lt;/style&gt;</code></pre>
          </div>
        </div>
        
        <div class="step-section">
          <h4>5. 高级功能实现</h4>
          <p>要实现更完整的功能，类似viewer.mjs，可以添加以下功能：</p>
          <ul>
            <li><strong>缩略图导航</strong> - 创建单独的缩略图组件</li>
            <li><strong>大纲视图</strong> - 使用<code>pdfDocument.getOutline()</code>获取文档大纲</li>
            <li><strong>文本搜索</strong> - 实现类似PDFFindController的功能</li>
            <li><strong>虚拟滚动</strong> - 只渲染视口中可见的页面，提高性能</li>
            <li><strong>注释工具</strong> - 添加注释编辑功能</li>
            <li><strong>打印支持</strong> - 实现PDF打印功能</li>
          </ul>
          <p>这些功能的实现可以参考PDF.js的源代码，逐步添加到您的Vue组件中。</p>
        </div>
        
        <div class="step-section">
          <h4>6. 最佳实践</h4>
          <ul>
            <li>使用Vuex/Pinia管理PDF状态，特别是对于大型应用</li>
            <li>使用Web Worker处理PDF解析，避免阻塞主线程</li>
            <li>实现虚拟滚动，只渲染可见页面，提高性能</li>
            <li>添加错误处理和加载状态提示</li>
            <li>缓存已渲染的页面，避免重复渲染</li>
            <li>考虑使用一些现成的库如vue-pdf或vue-pdf-embed作为起点</li>
          </ul>
        </div>
      </div>
    </section>

    <section class="module-implementation">
      <h2>实现细节</h2>
      
      <div class="implementation-section">
        <h3>工作线程架构</h3>
        <p>PDF.js使用Web Worker架构来处理PDF解析和渲染，以避免阻塞主线程。工作线程负责处理PDF数据的解码、页面操作符解析等计算密集型任务，而主线程则负责UI渲染和用户交互。</p>
        <p>这种架构确保了即使在处理大型或复杂的PDF文件时，用户界面仍然保持响应。</p>
      </div>
      
      <div class="implementation-section">
        <h3>渲染管道</h3>
        <p>PDF.js的渲染过程包括多个步骤：</p>
        <ol>
          <li>解析PDF页面内容流，生成操作符列表</li>
          <li>解释操作符列表，创建页面视觉表示</li>
          <li>将页面内容渲染到Canvas或SVG</li>
          <li>根据需要添加文本层、注释层等</li>
        </ol>
        <p>每个步骤都可以被单独优化和控制，提供了高度的灵活性。</p>
      </div>
      
      <div class="implementation-section">
        <h3>缓存策略</h3>
        <p>PDF.js实现了多级缓存策略，以提高渲染性能：</p>
        <ul>
          <li>页面对象缓存 - 避免重复解析同一页面</li>
          <li>操作符列表缓存 - 存储已解析的页面指令</li>
          <li>字形缓存 - 重用常用字体字形</li>
          <li>图像缓存 - 存储解码后的图像数据</li>
        </ul>
      </div>
    </section>
    
    <section class="module-notes">
      <h2>使用注意</h2>
      
      <div class="note-item">
        <h3>内存管理</h3>
        <p>处理大型PDF文件时，应注意内存使用。以下策略可以帮助减少内存消耗：</p>
        <ul>
          <li>使用<code>cleanup()</code>方法释放不再需要的页面资源</li>
          <li>限制同时加载和渲染的页面数量</li>
          <li>适当降低渲染比例以减少内存使用</li>
        </ul>
      </div>
      
      <div class="note-item">
        <h3>跨域限制</h3>
        <p>从不同域加载PDF文件和工作线程时，需要考虑CORS（跨源资源共享）策略：</p>
        <ul>
          <li>确保服务器配置了适当的CORS头</li>
          <li>对跨域请求使用<code>withCredentials</code>选项</li>
          <li>工作线程必须与主应用遵循相同的源策略限制</li>
        </ul>
      </div>
      
      <div class="note-item">
        <h3>兼容性考虑</h3>
        <p>PDF.js依赖于多个现代Web技术，在使用时需要注意：</p>
        <ul>
          <li>确保目标浏览器支持Promises、TypedArrays和Canvas</li>
          <li>考虑为旧版浏览器提供polyfills</li>
          <li>某些功能（如流式加载）在不同浏览器中可能有不同的性能特性</li>
        </ul>
      </div>
    </section>
  </main>

  <footer>
    <p>PDF.js API文档 - PDF.js版本参考</p>
  </footer>

  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 初始化mermaid
      mermaid.initialize({ startOnLoad: true });
      
      // 绘制架构图
      const architectureDiagram = document.querySelector('.architecture-diagram');
      architectureDiagram.innerHTML = `
        <div class="mermaid">
        graph TB
          A[PDF文件] --> B[getDocument]
          B --> C[PDFDocumentProxy]
          C --> D[PDFPageProxy]
          D --> E1[渲染]
          D --> E2[文本提取]
          D --> E3[注释处理]
          
          C --> F1[元数据]
          C --> F2[大纲]
          C --> F3[附件]
          
          B <--> G[PDFWorker]
          G <--> H[GlobalWorkerOptions]
          
          E1 --> I1[Canvas渲染]
          E1 --> I2[SVG渲染]
          E2 --> J[TextLayer]
          E3 --> K[AnnotationLayer]
        </div>
      `;
      
      // 重新渲染mermaid图表
      mermaid.init(undefined, '.mermaid');
    });
  </script>
</body>
</html> 