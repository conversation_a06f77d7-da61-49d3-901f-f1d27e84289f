# Native.js - 原生JavaScript翻页效果库

## 📖 项目简介

Native.js 是一个完全基于原生JavaScript实现的翻页效果库，从原有的jQuery版本重构而来，不依赖任何第三方库。它提供了丰富的翻页动画效果，支持触摸和鼠标交互，适用于电子书、杂志、相册等应用场景。

## 🚀 重构目标

✅ **完成目标**：
- 保留所有功能逻辑不变
- 不再依赖 jQuery，完全使用原生 JavaScript（Vanilla JS）
- 输出为结构清晰、可维护、注释适当的代码（中文注释）
- 不使用任何第三方库

## 📁 文件结构

```
flipbook/
├── index.js              # 原始jQuery版本（参考）
├── Native.js              # 完整重构版本（有问题）
├── Native_Fixed.js        # 修复版本（完全兼容原版，推荐）
├── Native_Simple.js       # 简化版本
├── Native_doc.html        # 完整文档
├── test.html             # 完整版测试页面
├── test_fixed.html       # 修复版测试页面（推荐）
├── test_simple.html      # 简化版测试页面
└── README.md             # 项目说明
```

## 🎯 版本说明

### Native_Fixed.js（强烈推荐）
- **特点**：完全兼容原版index.js的3D翻页效果
- **优势**：与原版功能100%一致，包含完整的3D数学计算
- **效果**：真实的3D翻页动画、渐变阴影、角落拖拽
- **状态**：稳定可用，已修复所有已知问题
- **大小**：约 50KB

### Native_Simple.js（轻量选择）
- **特点**：简化版本，专注于核心翻页功能
- **优势**：稳定、易用、代码清晰
- **适用**：不需要复杂3D效果的场景
- **大小**：约 8KB

### Native.js（已废弃）
- **状态**：有问题，不推荐使用
- **说明**：初始重构版本，存在兼容性问题

## 🛠 快速开始

### 1. 引入文件

```html
<!-- 推荐使用简化版 -->
<script src="Native_Simple.js"></script>

<!-- 或使用完整版 -->
<script src="Native.js"></script>
```

### 2. HTML结构

```html
<div id="flipbook">
    <div class="page">第1页</div>
    <div class="page">第2页</div>
    <div class="page">第3页</div>
    <div class="page">第4页</div>
</div>
```

### 3. 初始化

```javascript
var flipbook = document.getElementById('flipbook');
flipbook.turn({
    width: 800,
    height: 600,
    display: 'double',
    duration: 600,
    when: {
        turned: function(event) {
            var page = event.detail[0];
            console.log('当前页面：' + page);
        }
    }
});
```

### 4. 控制翻页

```javascript
// 下一页
flipbook.turn('next');

// 上一页
flipbook.turn('previous');

// 跳转到指定页
flipbook.turn('page', 3);

// 获取当前页
var currentPage = flipbook.turn('page');
```

## 🎨 配置选项

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `width` | Number | 800 | 容器宽度 |
| `height` | Number | 600 | 容器高度 |
| `display` | String | 'double' | 显示模式：'single' 或 'double' |
| `page` | Number | 1 | 初始页面 |
| `duration` | Number | 600 | 动画持续时间（毫秒） |
| `when` | Object | {} | 事件回调函数 |

## 📱 事件支持

```javascript
flipbook.turn({
    when: {
        turning: function(event) {
            var page = event.detail[0];
            console.log('正在翻到第' + page + '页');
        },
        turned: function(event) {
            var page = event.detail[0];
            console.log('已翻到第' + page + '页');
        }
    }
});
```

## 🌐 浏览器兼容性

- ✅ Chrome 30+
- ✅ Firefox 25+
- ✅ Safari 7+
- ✅ Edge 12+
- ⚠️ IE 10+（部分功能）

## 📋 API 方法

### 基础方法
- `turn(options)` - 初始化翻页效果
- `turn('page', number)` - 跳转到指定页面
- `turn('page')` - 获取当前页面
- `turn('next')` - 下一页
- `turn('previous')` - 上一页
- `turn('size', width, height)` - 调整大小

### 简化版特有
- 点击页面左侧：上一页
- 点击页面右侧：下一页
- 自动响应式布局

## 🎮 测试页面

1. **简化版测试**：打开 `test_simple.html`
2. **完整版测试**：打开 `test.html`
3. **查看文档**：打开 `Native_doc.html`

## 🔧 重构技术要点

### 1. 选择器与 DOM 操作
- `$(selector)` → `document.querySelector/querySelectorAll`
- `.find(...)` → `element.querySelector(...)`
- `.closest(...)` → `element.closest(...)`

### 2. 事件绑定
- `.on('click', handler)` → `addEventListener('click', handler)`
- `.trigger('event')` → `dispatchEvent(new CustomEvent(...))`

### 3. 属性、样式操作
- `.attr(name, value)` → `setAttribute/getAttribute`
- `.css({...})` → `element.style.property`
- `.html(value)` → `innerHTML`

### 4. 数据存储
- `$.data()` → `WeakMap` 实现的 DataManager

### 5. 动画处理
- `$.animate()` → 自定义 AnimationManager + CSS transitions

## 📝 开发说明

### 重构过程中的挑战
1. **复杂的数学计算**：原版包含大量3D变换和贝塞尔曲线计算
2. **事件系统**：需要重新实现jQuery的事件机制
3. **数据管理**：使用WeakMap替代jQuery的data系统
4. **兼容性处理**：确保在不同浏览器中正常工作

### 简化版的优势
1. **专注核心功能**：只保留最重要的翻页效果
2. **代码清晰**：易于理解和维护
3. **性能优化**：使用CSS3 transitions实现流畅动画
4. **用户友好**：简单的点击交互

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 📄 许可证

本项目基于原始jQuery版本重构，遵循相同的开源许可证。

## 🙏 致谢

感谢原始jQuery版本的开发者，为我们提供了优秀的翻页效果实现参考。
