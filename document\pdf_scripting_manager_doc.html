<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PDFScriptingManager - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#constants">常量</a>
    <a href="#properties">属性</a>
    <a href="#methods">方法</a>
    <a href="#flowcharts">流程图</a>
    <a href="#examples">示例</a>
    <a href="#notes">注意事项</a>
  </div>

  <h1>PDFScriptingManager 模块文档</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>PDFScriptingManager 是 PDF.js 库中负责处理 PDF 文档中的 JavaScript 脚本的组件。PDF 文档可以包含 JavaScript 代码，用于增强文档的交互性和功能，如表单验证、动态内容和自定义行为。PDFScriptingManager 提供了安全地执行这些脚本的能力，同时管理脚本的生命周期和与文档的交互。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 常量 -->
  <div id="constants">
    <h2>常量</h2>
    
    <p>PDFScriptingManager 使用以下常量：</p>
    
    <ul>
      <li>
        <code>PDF_SCRIPTING_DOCPROPERTIES</code>: PDF 脚本文档属性
      </li>
      <li>
        <code>PDFScriptingError</code>: PDF 脚本错误类型
      </li>
      <li>
        <code>ScriptingEvents</code>: 脚本事件类型
        <ul>
          <li><code>DOCUMENT_OPEN</code>: 文档打开事件</li>
          <li><code>DOCUMENT_CLOSE</code>: 文档关闭事件</li>
          <li><code>PAGE_OPEN</code>: 页面打开事件</li>
          <li><code>PAGE_CLOSE</code>: 页面关闭事件</li>
          <li><code>FIELD_CALCULATE</code>: 字段计算事件</li>
          <li><code>FIELD_FORMAT</code>: 字段格式化事件</li>
          <li><code>FIELD_KEYSTROKE</code>: 字段按键事件</li>
          <li><code>FIELD_VALIDATE</code>: 字段验证事件</li>
          <li><code>FIELD_VALUE_CHANGE</code>: 字段值变更事件</li>
          <li><code>MOUSE_DOWN</code>: 鼠标按下事件</li>
          <li><code>MOUSE_UP</code>: 鼠标抬起事件</li>
          <li><code>MOUSE_ENTER</code>: 鼠标进入事件</li>
          <li><code>MOUSE_EXIT</code>: 鼠标离开事件</li>
          <li><code>FOCUS</code>: 获取焦点事件</li>
          <li><code>BLUR</code>: 失去焦点事件</li>
        </ul>
      </li>
    </ul>
  </div>

  <!-- 属性 -->
  <div id="properties">
    <h2>属性</h2>
    
    <h3>公共属性</h3>
    <ul>
      <li><code>initialized</code>: 是否已初始化</li>
      <li><code>sandbox</code>: 脚本沙箱环境</li>
      <li><code>ready</code>: 脚本管理器是否就绪</li>
    </ul>

    <h3>私有属性</h3>
    <ul>
      <li><code>#initialized</code>: 内部初始化标志</li>
      <li><code>#initializedPromise</code>: 初始化 Promise</li>
      <li><code>#pdfDocument</code>: PDF 文档实例</li>
      <li><code>#sandboxBundleSrc</code>: 沙箱包源路径</li>
      <li><code>#scriptingFactory</code>: 脚本工厂实例</li>
      <li><code>#internalEvents</code>: 内部事件映射</li>
      <li><code>#domEvents</code>: DOM 事件映射</li>
      <li><code>#pageOpenScriptEvents</code>: 页面打开脚本事件集合</li>
      <li><code>#pageCloseScriptEvents</code>: 页面关闭脚本事件集合</li>
      <li><code>#documentScriptEvents</code>: 文档脚本事件集合</li>
      <li><code>#mouseOverElements</code>: 鼠标悬停元素集合</li>
      <li><code>#enabled</code>: 脚本是否启用</li>
      <li><code>#data</code>: 脚本数据</li>
      <li><code>#eventBus</code>: 事件总线</li>
      <li><code>#sandboxEvaluator</code>: 沙箱求值器</li>
      <li><code>#hasJSActionsPromise</code>: 是否有 JS 动作 Promise</li>
      <li><code>#hasJSActions</code>: 是否有 JS 动作</li>
      <li><code>#documentActions</code>: 文档动作集合</li>
      <li><code>#calculationOrder</code>: 计算顺序数组</li>
      <li><code>#loader</code>: 加载器实例</li>
      <li><code>#worker</code>: 工作线程实例</li>
      <li><code>#annotationStorage</code>: 注释存储实例</li>
    </ul>
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法</h2>
    
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">构造函数</h3>
      </div>
      <div class="method-content">
        <p>创建一个新的 PDFScriptingManager 实例。</p>
        <pre><code class="language-javascript">
constructor({
  sandboxBundleSrc = null,
  scriptingFactory = null,
  docPropertiesLookup = null,
  annotationStorage = null,
  enableScripting = false,
  eventBus,
})
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>sandboxBundleSrc</code>: 沙箱包源路径 (可选，默认为 null)</li>
          <li><code>scriptingFactory</code>: 脚本工厂实例 (可选，默认为 null)</li>
          <li><code>docPropertiesLookup</code>: 文档属性查找函数 (可选，默认为 null)</li>
          <li><code>annotationStorage</code>: 注释存储实例 (可选，默认为 null)</li>
          <li><code>enableScripting</code>: 是否启用脚本 (可选，默认为 false)</li>
          <li><code>eventBus</code>: 事件总线</li>
        </ul>
      </div>
    </div>

    <h3>核心方法</h3>
    <ul>
      <li><code>setDocument(pdfDocument)</code>: 设置 PDF 文档</li>
      <li><code>setViewer(pdfViewer)</code>: 设置 PDF 查看器</li>
      <li><code>setDomEvents(domEvents)</code>: 设置 DOM 事件</li>
      <li><code>setAnnotationStorage(annotationStorage)</code>: 设置注释存储</li>
      <li><code>async dispatchEventInSandbox(event)</code>: 在沙箱中分发事件</li>
      <li><code>async dispatchWillSave(detail)</code>: 分发即将保存事件</li>
      <li><code>async dispatchDidSave(detail)</code>: 分发已保存事件</li>
      <li><code>async dispatchWillPrint(detail)</code>: 分发即将打印事件</li>
      <li><code>async dispatchDidPrint(detail)</code>: 分发已打印事件</li>
      <li><code>async destroy()</code>: 销毁脚本管理器</li>
    </ul>
    
    <h3>私有方法</h3>
    <ul>
      <li><code>#collectCalculationOrder()</code>: 收集计算顺序</li>
      <li><code>#closeDoc()</code>: 关闭文档</li>
      <li><code>#registerDocumentActions()</code>: 注册文档动作</li>
      <li><code>#addPageScriptEvents(eventType, pageNumber)</code>: 添加页面脚本事件</li>
      <li><code>#addDocumentScriptEvents(eventType)</code>: 添加文档脚本事件</li>
      <li><code>#addEventListeners()</code>: 添加事件监听器</li>
      <li><code>#removeEventListeners()</code>: 移除事件监听器</li>
      <li><code>#handleEvent(event)</code>: 处理事件</li>
      <li><code>#mouseOver(event)</code>: 处理鼠标悬停事件</li>
      <li><code>#mouseOut(event)</code>: 处理鼠标离开事件</li>
      <li><code>#createScripting()</code>: 创建脚本环境</li>
      <li><code>#dispatchPageOpen(pageNumber, scratchPage = false)</code>: 分发页面打开事件</li>
      <li><code>#dispatchPageClose(pageNumber)</code>: 分发页面关闭事件</li>
      <li><code>#initScripting()</code>: 初始化脚本环境</li>
      <li><code>#updateObjects(objects)</code>: 更新对象</li>
      <li><code>#getField(fieldName)</code>: 获取表单字段</li>
      <li><code>#setField(fieldName, value)</code>: 设置表单字段值</li>
      <li><code>#calculate(event)</code>: 计算表单字段</li>
      <li><code>#jsCall(data)</code>: 执行 JS 调用</li>
      <li><code>#getDocProperties()</code>: 获取文档属性</li>
    </ul>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <h3>脚本初始化流程</h3>
    <p>1. 创建 PDFScriptingManager 实例<br>
    2. 设置 PDF 文档<br>
    3. 检查文档是否包含 JavaScript<br>
    4. 创建脚本环境<br>
    5. 初始化沙箱<br>
    6. 注册文档动作<br>
    7. 触发文档打开事件</p>
    
    <div class="mermaid">
      graph TD
          A[创建 PDFScriptingManager 实例] --> B[调用 setDocument 方法]
          B --> C[检查文档是否包含 JavaScript]
          C -->|否| D[初始化完成]
          C -->|是| E[创建脚本环境]
          E --> F[初始化沙箱]
          F --> G[注册文档动作]
          G --> H[添加事件监听器]
          H --> I[触发文档打开事件]
          I --> J[初始化完成]
    </div>
    
    <h3>事件分发流程</h3>
    <p>1. 用户触发事件（如点击表单字段）<br>
    2. 事件通过 DOM 事件系统传递<br>
    3. PDFScriptingManager 捕获事件<br>
    4. 确定对应的 PDF 脚本事件类型<br>
    5. 在沙箱中分发事件<br>
    6. 执行对应的脚本处理程序<br>
    7. 将结果应用回文档</p>
    
    <div class="mermaid">
      graph TD
          A[用户触发事件] --> B[DOM 事件系统捕获]
          B --> C[PDFScriptingManager 处理事件]
          C --> D[确定 PDF 脚本事件类型]
          D --> E[调用 dispatchEventInSandbox 方法]
          E --> F[在沙箱中执行脚本]
          F --> G{脚本是否修改了文档?}
          G -->|是| H[应用更改到文档]
          G -->|否| I[完成事件处理]
          H --> I
    </div>
    
    <h3>表单计算流程</h3>
    <p>1. 表单字段值变更<br>
    2. 触发计算事件<br>
    3. 收集依赖此字段的计算字段<br>
    4. 按计算顺序执行计算脚本<br>
    5. 更新表单字段值<br>
    6. 触发格式化事件</p>
    
    <div class="mermaid">
      graph TD
          A[表单字段值变更] --> B[触发 FIELD_VALUE_CHANGE 事件]
          B --> C[调用 #calculate 方法]
          C --> D[获取计算顺序]
          D --> E[按顺序执行字段计算脚本]
          E --> F[更新表单字段值]
          F --> G[触发 FIELD_FORMAT 事件]
          G --> H[应用格式化结果]
    </div>
  </div>

  <!-- 示例 -->
  <div id="examples">
    <h2>使用示例</h2>
    
    <h3>基本用法</h3>
    <pre><code class="language-javascript">
// 创建 PDFScriptingManager 实例
const scriptingManager = new PDFScriptingManager({
  eventBus: eventBus,
  sandboxBundleSrc: "pdf.sandbox.js", // 沙箱脚本的路径
  scriptingFactory: PDFJSLib.DefaultScriptingFactory,
  docPropertiesLookup: async () => {
    return {
      title: pdfDocument.title,
      author: pdfDocument.author,
      creator: pdfDocument.creator,
      producer: pdfDocument.producer,
      docId: pdfDocument.fingerprint,
      // 其他属性...
    };
  },
  enableScripting: true // 启用脚本执行
});

// 设置 PDF 文档
pdfDocument.getPage(1).then(function() {
  scriptingManager.setDocument(pdfDocument);
});

// 设置 PDF 查看器
scriptingManager.setViewer(pdfViewer);

// 监听脚本初始化完成
scriptingManager.initializedPromise.then(() => {
  console.log('PDF 脚本环境已初始化');
  
  if (scriptingManager.hasJSActions) {
    console.log('文档包含 JavaScript 动作');
  }
});

// 设置注释存储
scriptingManager.setAnnotationStorage(pdfDocument.annotationStorage);
    </code></pre>
    
    <h3>处理表单交互</h3>
    <pre><code class="language-javascript">
// 创建 DOM 事件管理器
const domEvents = new PDFJSLib.DefaultDOMEvents({
  eventBus: eventBus,
  container: document.getElementById('viewerContainer'),
  annotationStorage: pdfDocument.annotationStorage
});

// 设置 DOM 事件
scriptingManager.setDomEvents(domEvents);

// 监听表单提交事件
eventBus.on('dispatcheventinsandbox', function(event) {
  // 检查是否为表单提交事件
  if (event.detail.name === 'App' && event.detail.command === 'SubmitForm') {
    console.log('表单提交:', event.detail);
    
    // 可以在这里处理表单提交逻辑
    const formData = event.detail.data;
    
    // 示例：阻止默认的表单提交行为并使用自定义逻辑
    event.preventDefault();
    
    // 使用 fetch API 提交表单数据
    fetch('/submit-form', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
      console.log('表单提交成功:', data);
      // 通知用户表单已提交成功
    })
    .catch(error => {
      console.error('表单提交失败:', error);
      // 通知用户表单提交失败
    });
  }
});

// 处理表单字段值变更
function updateFormField(fieldName, value) {
  // 如果脚本已初始化，使用脚本管理器设置字段值
  if (scriptingManager.ready) {
    scriptingManager.dispatchEventInSandbox({
      id: 'updateField',
      name: 'Form',
      type: 'FieldValue',
      value: {
        name: fieldName,
        value: value
      }
    }).then(() => {
      console.log(`字段 ${fieldName} 已更新为 ${value}`);
    });
  } else {
    console.warn('脚本管理器尚未准备好');
  }
}
    </code></pre>
    
    <h3>处理文档事件</h3>
    <pre><code class="language-javascript">
// 处理打印事件
async function printDocument() {
  // 在打印前通知脚本
  await scriptingManager.dispatchWillPrint();
  
  // 执行打印操作
  window.print();
  
  // 在打印后通知脚本
  await scriptingManager.dispatchDidPrint();
}

// 处理保存事件
async function saveDocument() {
  // 在保存前通知脚本
  await scriptingManager.dispatchWillSave();
  
  // 执行保存操作 (示例)
  const data = await pdfDocument.saveDocument();
  const blob = new Blob([data], { type: 'application/pdf' });
  const url = URL.createObjectURL(blob);
  
  const a = document.createElement('a');
  a.href = url;
  a.download = 'document.pdf';
  a.click();
  
  URL.revokeObjectURL(url);
  
  // 在保存后通知脚本
  await scriptingManager.dispatchDidSave();
}

// 添加打印和保存按钮事件处理
document.getElementById('printButton').addEventListener('click', printDocument);
document.getElementById('saveButton').addEventListener('click', saveDocument);

// 在关闭文档时清理资源
async function closeDocument() {
  // 销毁脚本管理器
  await scriptingManager.destroy();
  
  // 清理其他资源
  // ...
}
    </code></pre>
  </div>

  <!-- 注意事项 -->
  <div id="notes">
    <h2>注意事项</h2>
    
    <ul>
      <li>PDF 文档中的 JavaScript 可能会带来安全风险。PDFScriptingManager 使用沙箱机制来隔离脚本执行环境，但为了最大限度地保证安全，应该谨慎处理来自不受信任来源的 PDF 文件。</li>
      <li>默认情况下，脚本执行是禁用的。只有在明确传递 <code>enableScripting: true</code> 选项时才会启用脚本执行。</li>
      <li>PDF 中的 JavaScript 基于 Acrobat JavaScript API，与标准 Web JavaScript 有所不同。某些 PDF 文档可能使用特定于 Acrobat 的功能，这些功能在 PDF.js 中可能不完全支持。</li>
      <li>脚本执行可能会影响性能，特别是对于包含大量复杂脚本的文档。在性能敏感的应用中，可能需要考虑禁用脚本或限制脚本执行的范围。</li>
      <li>如果文档包含依赖于特定浏览器或操作系统特性的脚本，这些脚本可能在跨平台环境中无法正常工作。</li>
      <li>某些 PDF 脚本可能依赖于外部资源或网络请求，这些请求可能会被浏览器的安全策略（如同源策略）阻止。</li>
      <li>当处理表单数据时，应确保敏感数据得到适当保护，特别是在将数据提交到服务器时。</li>
      <li>PDFScriptingManager 需要 <code>sandboxBundleSrc</code> 参数来加载沙箱脚本。确保此脚本可用且正确配置，否则脚本功能将无法工作。</li>
    </ul>
  </div>

  <script>
    // 在页面加载完成后初始化 Mermaid
    document.addEventListener('DOMContentLoaded', function() {
      mermaid.initialize({ startOnLoad: true });
      
      // 生成目录
      generateTOC();
    });
  </script>
</body>
</html> 