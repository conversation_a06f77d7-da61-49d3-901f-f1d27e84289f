<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PDFPageViewBuffer 文档 - PDF.js</title>
  <link rel="stylesheet" href="doc_styles.css">
</head>
<body>
  <header>
    <h1>PDFPageViewBuffer</h1>
    <p class="module-description">页面视图缓冲区，管理PDF页面视图的缓存和重用</p>
    <a href="index.html" class="back-link">返回模块列表</a>
  </header>

  <div class="content">
    <section class="module-intro">
      <h2>模块介绍</h2>
      <p>PDFPageViewBuffer 是 PDF.js 中用于管理 PDF 页面视图（PDFPageView）缓存的组件。它实现了一个高效的缓存机制，确保只有当前可见和即将可见的页面保留在内存中，而将不可见的页面从内存中清除，以优化内存使用和提高性能。该组件使用 Set 数据结构存储页面视图，并实现了最近使用优先的缓存替换策略。</p>
      
      <div class="module-diagram">
        <h3>组件位置</h3>
        <div class="mermaid">
          graph TD
            PDFViewer[PDFViewer] --> PDFPageViewBuffer[PDFPageViewBuffer]
            PDFPageViewBuffer -->|缓存管理| PDFPageView[PDFPageView]
            PDFViewer -->|创建| PDFPageView
            PDFPageView -->|渲染| PDF页面
        </div>
      </div>
    </section>

    <section class="properties">
      <h2>属性</h2>
      
      <h3>私有属性</h3>
      <div class="property">
        <h4>#buf</h4>
        <p>私有属性，使用 Set 数据结构存储页面视图对象。</p>
      </div>

      <div class="property">
        <h4>#size</h4>
        <p>私有属性，表示缓冲区的最大容量（可存储的最大页面视图数量）。</p>
      </div>
    </section>

    <section class="methods">
      <h2>方法</h2>

      <div class="method">
        <h3>constructor(size)</h3>
        <p>创建 PDFPageViewBuffer 实例。</p>
        <h4>参数：</h4>
        <ul>
          <li><code>size</code> - 缓冲区大小，表示可以存储的最大页面视图数量</li>
        </ul>
        
        <pre><code>// 创建一个可存储10个页面视图的缓冲区
const pageViewBuffer = new PDFPageViewBuffer(10);</code></pre>
      </div>

      <div class="method">
        <h3>push(view)</h3>
        <p>将页面视图添加到缓冲区。如果缓冲区已满，则会销毁最旧的页面视图。如果视图已在缓冲区中，会将其移到最新的位置（重置使用时间）。</p>
        <h4>参数：</h4>
        <ul>
          <li><code>view</code> - 要添加的页面视图（PDFPageView 实例）</li>
        </ul>
        
        <pre><code>// 将页面视图添加到缓冲区
pageViewBuffer.push(pageView);</code></pre>

        <div class="mermaid">
          flowchart TD
            A[开始] --> B{缓冲区中是否已有该视图?}
            B -->|是| C[从缓冲区中删除该视图]
            B -->|否| D[跳过删除步骤]
            C --> E[将视图添加到缓冲区]
            D --> E
            E --> F{缓冲区大小是否超过限制?}
            F -->|是| G[销毁最旧的视图]
            F -->|否| H[结束]
            G --> H
        </div>
      </div>

      <div class="method">
        <h3>resize(newSize, idsToKeep = null)</h3>
        <p>调整缓冲区大小。如果提供了 idsToKeep 参数，会确保这些 ID 对应的页面视图保留在缓冲区中。</p>
        <h4>参数：</h4>
        <ul>
          <li><code>newSize</code> - 新的缓冲区大小</li>
          <li><code>idsToKeep</code> - 要保留的页面视图 ID 集合（可选，默认为 null）</li>
        </ul>
        
        <pre><code>// 调整缓冲区大小为 20
pageViewBuffer.resize(20);

// 调整缓冲区大小并指定要保留的页面 ID
const visibleIds = new Set([1, 2, 3, 4]);
pageViewBuffer.resize(15, visibleIds);</code></pre>

        <div class="mermaid">
          flowchart TD
            A[开始] --> B[设置新的缓冲区大小]
            B --> C{是否提供了要保留的ID?}
            C -->|是| D[遍历缓冲区中的视图]
            C -->|否| G{缓冲区大小是否超过新限制?}
            D --> E{当前视图ID是否在要保留的ID集合中?}
            E -->|是| F[移动视图到缓冲区末尾]
            E -->|否| D
            F --> D
            D --> G
            G -->|是| H[销毁最旧的视图]
            G -->|否| I[结束]
            H --> G
        </div>
      </div>

      <div class="method">
        <h3>has(view)</h3>
        <p>检查缓冲区中是否包含指定的页面视图。</p>
        <h4>参数：</h4>
        <ul>
          <li><code>view</code> - 要检查的页面视图</li>
        </ul>
        <h4>返回：</h4>
        <p>布尔值，表示缓冲区中是否包含该页面视图</p>
        
        <pre><code>// 检查缓冲区是否包含特定页面视图
const isInBuffer = pageViewBuffer.has(pageView);</code></pre>
      </div>

      <div class="method">
        <h3>[Symbol.iterator]()</h3>
        <p>实现迭代器接口，允许通过 for...of 循环遍历缓冲区中的页面视图。</p>
        <h4>返回：</h4>
        <p>缓冲区的迭代器</p>
        
        <pre><code>// 遍历缓冲区中的所有页面视图
for (const pageView of pageViewBuffer) {
  console.log(`Page ${pageView.id} in buffer`);
}</code></pre>
      </div>

      <div class="method">
        <h3>#destroyFirstView()</h3>
        <p>私有方法，销毁缓冲区中最旧的（最先添加的）页面视图，并从缓冲区中移除。</p>
      </div>
    </section>

    <section class="usage">
      <h2>使用示例</h2>

      <div class="example">
        <h3>基本用法</h3>
        <pre><code>// 创建页面视图缓冲区
const pageViewBuffer = new PDFPageViewBuffer(10);

// 添加页面视图到缓冲区
pageViewBuffer.push(pageView1);
pageViewBuffer.push(pageView2);
pageViewBuffer.push(pageView3);

// 检查缓冲区是否包含特定页面视图
const isPage2InBuffer = pageViewBuffer.has(pageView2);

// 调整缓冲区大小
pageViewBuffer.resize(5);

// 遍历缓冲区中的所有页面视图
for (const view of pageViewBuffer) {
  console.log(`Page ${view.id} is in buffer`);
}</code></pre>
      </div>

      <div class="example">
        <h3>在 PDFViewer 中的用法</h3>
        <pre><code>class PDFViewer {
  constructor(options) {
    // ...其他初始化代码...
    
    // 创建页面视图缓冲区
    this.#buffer = new PDFPageViewBuffer(DEFAULT_CACHE_SIZE);
  }
  
  update() {
    // 获取当前可见的页面
    const visible = this._getVisiblePages();
    const visiblePages = visible.views;
    const numVisiblePages = visiblePages.length;
    
    if (numVisiblePages === 0) {
      return;
    }
    
    // 计算新的缓存大小，确保至少能容纳所有可见页面的2倍+1
    const newCacheSize = Math.max(DEFAULT_CACHE_SIZE, 2 * numVisiblePages + 1);
    
    // 调整缓冲区大小，确保可见页面保留在缓冲区中
    this.#buffer.resize(newCacheSize, visible.ids);
    
    // 更新所有可见页面
    for (const { view, visibleArea } of visiblePages) {
      view.updateVisibleArea(visibleArea);
    }
    
    // 更新缓冲区中所有不可见页面
    for (const view of this.#buffer) {
      if (!visible.ids.has(view.id)) {
        view.updateVisibleArea(null);
      }
    }
    
    // 渲染优先级最高的页面
    this.renderingQueue.renderHighestPriority(visible);
  }
}</code></pre>
      </div>
    </section>

    <section class="implementation-notes">
      <h2>实现说明</h2>
      <div class="note">
        <h3>缓存策略</h3>
        <p>PDFPageViewBuffer 实现了一个最近使用优先（LRU-like）的缓存策略：</p>
        <ul>
          <li>新添加的页面视图被放置在缓冲区的"末尾"（最新位置）</li>
          <li>当已有页面被再次访问时，它会被移到缓冲区的"末尾"，表示它是最近使用的</li>
          <li>当缓冲区超过容量时，位于缓冲区"开头"（最早位置）的页面视图被销毁并移除</li>
        </ul>
        <p>这种策略确保最近使用的页面视图保留在缓冲区中，而长时间未使用的页面视图会被销毁，从而优化内存使用。</p>
      </div>
      
      <div class="note">
        <h3>动态缓冲区大小</h3>
        <p>PDFPageViewBuffer 的大小可以通过 resize 方法动态调整。在 PDFViewer 中，缓冲区大小通常根据可见页面的数量动态计算，确保至少能容纳所有可见页面的 2 倍加 1。这种策略可以在不同大小的文档和不同的查看条件下提供最佳性能。</p>
      </div>
      
      <div class="note">
        <h3>页面视图的生命周期管理</h3>
        <p>当页面视图从缓冲区中移除时，PDFPageViewBuffer 会调用该视图的 destroy() 方法释放资源。这确保了不再需要的页面视图能够正确清理它们占用的内存和资源，防止内存泄漏。</p>
      </div>

      <div class="note">
        <h3>可迭代接口</h3>
        <p>PDFPageViewBuffer 实现了迭代器接口（Symbol.iterator），允许使用 for...of 循环遍历缓冲区中的所有页面视图。这使得在 PDFViewer 中可以方便地对缓冲区中的所有页面视图执行批量操作。</p>
      </div>
    </section>

    <section class="best-practices">
      <h2>最佳实践</h2>
      <div class="practice">
        <h3>合理设置缓冲区大小</h3>
        <p>缓冲区大小应根据实际需求设置：</p>
        <ul>
          <li>过小的缓冲区会导致频繁的页面销毁和重建，影响性能</li>
          <li>过大的缓冲区会占用过多内存，尤其是对于大型文档</li>
          <li>一个好的经验法则是：缓冲区大小 = max(默认大小, 2 × 可见页面数 + 1)</li>
        </ul>
      </div>
      
      <div class="practice">
        <h3>优先保留可见页面</h3>
        <p>在调整缓冲区大小时，应确保当前可见的页面始终保留在缓冲区中：</p>
        <pre><code>// 获取当前可见页面的 ID
const visibleIds = new Set(visiblePages.map(page => page.id));

// 调整缓冲区大小，确保保留可见页面
pageViewBuffer.resize(newSize, visibleIds);</code></pre>
      </div>
      
      <div class="practice">
        <h3>避免频繁调整缓冲区大小</h3>
        <p>频繁调整缓冲区大小可能导致不必要的页面销毁和重建。应在以下情况下调整缓冲区大小：</p>
        <ul>
          <li>可见页面数量显著变化时（例如，更改缩放级别或视口大小）</li>
          <li>文档加载或重新加载时</li>
          <li>切换显示模式（如单页模式、双页模式）时</li>
        </ul>
      </div>
      
      <div class="practice">
        <h3>监控内存使用</h3>
        <p>在处理大型 PDF 文档时，应监控内存使用情况，并根据需要调整缓冲区大小：</p>
        <ul>
          <li>对于内存受限的环境，可能需要使用较小的缓冲区</li>
          <li>对于高性能环境，可以使用较大的缓冲区提高响应速度</li>
          <li>考虑实现自适应缓冲区大小策略，根据系统内存和文档大小动态调整</li>
        </ul>
      </div>
    </section>
  </div>

  <script src="doc_script.js"></script>
  <script src="js/mermaid.js"></script>
  <script>
    mermaid.initialize({ startOnLoad: true, theme: 'neutral' });
  </script>
</body>
</html> 