<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PasswordPrompt - PDF.js 文档</title>
  <link rel="stylesheet" href="doc_styles.css">
  <!-- Mermaid流程图库 -->
  <script src="js/mermaid.js"></script>
  <script src="doc_script.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <div class="navbar">
    <a href="index.html">首页</a>
    <a href="#module-info">模块信息</a>
    <a href="#constants">常量</a>
    <a href="#properties">属性</a>
    <a href="#methods">方法</a>
    <a href="#flowcharts">流程图</a>
    <a href="#examples">示例</a>
    <a href="#notes">注意事项</a>
  </div>

  <h1>PasswordPrompt 模块文档</h1>
  
  <!-- 模块信息 -->
  <div id="module-info" class="module-info">
    <h2>模块简介</h2>
    <p>PasswordPrompt 是 PDF.js 库中用于处理加密 PDF 文档的密码输入界面组件。当用户尝试打开受密码保护的 PDF 文档时，此组件会显示一个对话框，提示用户输入密码。它支持不同类型的密码（用户密码和所有者密码），并能够处理密码验证错误和重试逻辑。</p>
  </div>

  <!-- 目录 -->
  <div id="toc">
    <h2>目录</h2>
    <!-- 目录内容将由JavaScript生成 -->
  </div>

  <!-- 常量 -->
  <div id="constants">
    <h2>常量</h2>
    
    <p>PasswordPrompt 使用以下常量：</p>
    
    <ul>
      <li>
        <code>PasswordResponses</code>: 密码响应枚举
        <ul>
          <li><code>NEED_PASSWORD</code>: 需要密码</li>
          <li><code>INCORRECT_PASSWORD</code>: 密码不正确</li>
        </ul>
      </li>
      <li>
        <code>PASSWORD_TYPES</code>: 密码类型枚举
        <ul>
          <li><code>USER</code>: 用户密码，用于打开文档</li>
          <li><code>OWNER</code>: 所有者密码，用于解锁完全访问权限</li>
        </ul>
      </li>
    </ul>
  </div>

  <!-- 属性 -->
  <div id="properties">
    <h2>属性</h2>
    
    <h3>公共属性</h3>
    <ul>
      <li><code>active</code>: 是否处于活动状态</li>
      <li><code>overlayName</code>: 覆盖层名称</li>
      <li><code>dialog</code>: 对话框 DOM 元素</li>
      <li><code>passwordField</code>: 密码输入字段</li>
    </ul>

    <h3>私有属性</h3>
    <ul>
      <li><code>#updateCallback</code>: 更新回调函数</li>
      <li><code>#reason</code>: 提示原因</li>
      <li><code>#passwordField</code>: 密码输入字段</li>
      <li><code>#passwordText</code>: 密码提示文本</li>
      <li><code>#errorMsg</code>: 错误消息元素</li>
      <li><code>#l10n</code>: 本地化对象</li>
      <li><code>#isViewerEmbedded</code>: 查看器是否嵌入</li>
      <li><code>#container</code>: 容器元素</li>
      <li><code>#dialog</code>: 对话框元素</li>
      <li><code>#overlayManager</code>: 覆盖层管理器</li>
      <li><code>#passwordType</code>: 当前密码类型</li>
      <li><code>#pushPinButtonInitialState</code>: 固定按钮初始状态</li>
      <li><code>#pushPinButton</code>: 固定按钮元素</li>
      <li><code>#onClose</code>: 关闭事件处理函数</li>
      <li><code>#submitButton</code>: 提交按钮元素</li>
      <li><code>#pendingPromise</code>: 待处理的 Promise</li>
      <li><code>#pendingPromiseResolve</code>: 待处理 Promise 的解决函数</li>
      <li><code>#pendingPromiseReject</code>: 待处理 Promise 的拒绝函数</li>
    </ul>
  </div>

  <!-- 方法列表 -->
  <div id="methods">
    <h2>方法</h2>
    
    <div class="method-block">
      <div class="method-header">
        <h3 class="method-name">构造函数</h3>
      </div>
      <div class="method-content">
        <p>创建一个新的 PasswordPrompt 实例。</p>
        <pre><code class="language-javascript">
constructor({
  overlayName = null,
  label = null,
  inputName = null,
  isViewerEmbedded = false,
  container = null,
  passwordDialog = null,
  overlayManager = null,
  l10n = undefined,
})
        </code></pre>
        <p><strong>参数:</strong></p>
        <ul>
          <li><code>overlayName</code>: 覆盖层名称 (可选，默认为 null)</li>
          <li><code>label</code>: 输入框标签 (可选，默认为 null)</li>
          <li><code>inputName</code>: 输入框名称 (可选，默认为 null)</li>
          <li><code>isViewerEmbedded</code>: 查看器是否嵌入 (可选，默认为 false)</li>
          <li><code>container</code>: 容器元素 (可选，默认为 null)</li>
          <li><code>passwordDialog</code>: 密码对话框元素 (可选，默认为 null)</li>
          <li><code>overlayManager</code>: 覆盖层管理器 (可选，默认为 null)</li>
          <li><code>l10n</code>: 本地化对象 (可选，默认为 undefined)</li>
        </ul>
      </div>
    </div>

    <h3>核心方法</h3>
    <ul>
      <li><code>init()</code>: 初始化密码提示</li>
      <li><code>async open()</code>: 打开密码对话框</li>
      <li><code>close()</code>: 关闭密码对话框</li>
      <li><code>setUpdateCallback(updateCallback)</code>: 设置更新回调函数</li>
      <li><code>setReason(reason, passwordType)</code>: 设置提示原因和密码类型</li>
      <li><code>output(code, password)</code>: 输出密码处理结果</li>
      <li><code>verify(password)</code>: 验证密码</li>
      <li><code>reset()</code>: 重置密码对话框</li>
      <li><code>toggle()</code>: 切换密码可见性</li>
      <li><code>destroy()</code>: 销毁密码提示</li>
    </ul>
    
    <h3>私有方法</h3>
    <ul>
      <li><code>#createDialog()</code>: 创建密码对话框</li>
      <li><code>#handleSubmit()</code>: 处理密码提交</li>
      <li><code>#handleKeyDown(e)</code>: 处理键盘按键事件</li>
      <li><code>#updateUIState()</code>: 更新界面状态</li>
      <li><code>#togglePasswordVisibility()</code>: 切换密码可见性</li>
      <li><code>#updatePassword()</code>: 更新密码</li>
      <li><code>#setPushPinState(isPinned)</code>: 设置固定按钮状态</li>
      <li><code>#addEventListeners()</code>: 添加事件监听器</li>
      <li><code>#removeEventListeners()</code>: 移除事件监听器</li>
    </ul>
  </div>

  <!-- 流程图 -->
  <div id="flowcharts">
    <h2>流程图</h2>
    
    <h3>密码处理流程</h3>
    <p>1. 打开加密的 PDF 文档<br>
    2. 触发需要密码事件<br>
    3. 显示密码输入对话框<br>
    4. 用户输入密码<br>
    5. 提交密码进行验证<br>
    6. 根据验证结果继续或重试</p>
    
    <div class="mermaid">
      graph TD
          A[打开加密 PDF 文档] --> B[触发需要密码事件]
          B --> C[调用 PasswordPrompt.open 方法]
          C --> D[显示密码输入对话框]
          D --> E[用户输入密码]
          E --> F[用户点击提交按钮]
          F --> G[调用 PasswordPrompt.verify 方法]
          G --> H[将密码传递给 PDF 文档]
          H --> I{密码是否正确?}
          I -->|是| J[关闭对话框并加载文档]
          I -->|否| K[显示错误消息]
          K --> L[清空密码字段]
          L --> E
    </div>
    
    <h3>用户交互流程</h3>
    <p>1. 用户查看密码对话框<br>
    2. 可选择切换密码可见性<br>
    3. 输入密码<br>
    4. 可选择记住密码<br>
    5. 提交或取消</p>
    
    <div class="mermaid">
      graph TD
          A[显示密码对话框] --> B[用户交互选项]
          B --> C[切换密码可见性]
          B --> D[输入密码]
          B --> E[勾选记住密码]
          B --> F[点击提交按钮]
          B --> G[点击取消按钮]
          F --> H[验证密码]
          G --> I[关闭对话框并取消加载]
          C --> B
          D --> B
          E --> B
    </div>
    
    <h3>错误处理流程</h3>
    <p>1. 验证密码<br>
    2. 检测到错误<br>
    3. 更新界面状态<br>
    4. 显示错误消息<br>
    5. 允许用户重试</p>
    
    <div class="mermaid">
      graph TD
          A[验证密码] --> B{验证结果}
          B -->|正确| C[关闭对话框并继续]
          B -->|不正确| D[调用 setReason 方法]
          D --> E[更新界面状态]
          E --> F[显示错误消息]
          F --> G[聚焦密码输入框]
          G --> H[等待用户重新输入]
          H --> A
    </div>
  </div>

  <!-- 示例 -->
  <div id="examples">
    <h2>使用示例</h2>
    
    <h3>基本用法</h3>
    <pre><code class="language-javascript">
// 创建覆盖层管理器
const overlayManager = new OverlayManager();

// 创建 PasswordPrompt 实例
const passwordPrompt = new PasswordPrompt({
  overlayName: 'passwordOverlay',
  container: document.getElementById('viewerContainer'),
  l10n: pdfViewerL10n, // 本地化对象
  isViewerEmbedded: window.parent !== window,
  overlayManager: overlayManager
});

// 初始化密码提示
passwordPrompt.init();

// 设置密码更新回调
passwordPrompt.setUpdateCallback((password) => {
  // 尝试使用密码加载 PDF
  loadingTask.onPassword = function (updatePassword, reason) {
    if (reason === PasswordResponses.INCORRECT_PASSWORD) {
      // 密码不正确，显示错误并请求重新输入
      passwordPrompt.setReason(reason, PASSWORD_TYPES.USER);
      passwordPrompt.open().then((password) => {
        updatePassword(password);
      });
    } else {
      // 首次请求密码
      passwordPrompt.setReason(reason, PASSWORD_TYPES.USER);
      passwordPrompt.open().then((password) => {
        updatePassword(password);
      });
    }
  };
  
  return loadingTask.promise;
});

// 加载加密的 PDF 文档
const loadingTask = pdfjsLib.getDocument({
  url: 'encrypted.pdf'
});

loadingTask.promise.then(function(pdf) {
  // PDF 加载成功，处理文档
  console.log('PDF 加载成功，页数:', pdf.numPages);
}).catch(function(error) {
  // 处理错误
  console.error('PDF 加载失败:', error);
});
    </code></pre>
    
    <h3>自定义密码对话框</h3>
    <pre><code class="language-javascript">
// 创建自定义密码对话框
function createCustomPasswordDialog() {
  const dialog = document.createElement('div');
  dialog.className = 'custom-password-dialog';
  
  dialog.innerHTML = `
    <div class="password-dialog-header">
      <h2 id="passwordHeader">需要密码</h2>
      <button id="passwordClose" class="close-button">×</button>
    </div>
    <div class="password-dialog-body">
      <p id="passwordText">此 PDF 文档受密码保护。请输入密码以访问内容。</p>
      <div class="password-input-container">
        <label for="password">密码:</label>
        <div class="password-input-wrapper">
          <input type="password" id="password" name="password" />
          <button id="passwordToggle" class="password-toggle" aria-label="显示密码">
            <i class="eye-icon"></i>
          </button>
        </div>
      </div>
      <div class="password-error" id="passwordError" hidden>密码不正确，请重试。</div>
      <div class="password-remember">
        <input type="checkbox" id="passwordRemember" />
        <label for="passwordRemember">记住此文档的密码</label>
      </div>
    </div>
    <div class="password-dialog-footer">
      <button id="passwordCancel">取消</button>
      <button id="passwordSubmit" class="primary">确定</button>
    </div>
  `;
  
  return dialog;
}

// 创建 PasswordPrompt 实例，使用自定义对话框
const customPasswordPrompt = new PasswordPrompt({
  overlayName: 'customPasswordOverlay',
  container: document.getElementById('viewerContainer'),
  passwordDialog: createCustomPasswordDialog(),
  l10n: pdfViewerL10n,
  overlayManager: overlayManager
});

// 初始化并使用自定义密码提示
customPasswordPrompt.init();

// 添加自定义样式
function addCustomPasswordStyles() {
  const style = document.createElement('style');
  style.textContent = `
    .custom-password-dialog {
      width: 400px;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
      overflow: hidden;
      font-family: Arial, sans-serif;
    }
    
    .password-dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      background-color: #f5f5f5;
      border-bottom: 1px solid #e0e0e0;
    }
    
    .password-dialog-header h2 {
      margin: 0;
      font-size: 18px;
      color: #333;
    }
    
    .close-button {
      background: none;
      border: none;
      font-size: 20px;
      cursor: pointer;
      color: #666;
    }
    
    .password-dialog-body {
      padding: 16px;
    }
    
    .password-input-container {
      margin: 16px 0;
    }
    
    .password-input-wrapper {
      display: flex;
      position: relative;
      margin-top: 8px;
    }
    
    .password-input-wrapper input {
      flex: 1;
      padding: 8px 40px 8px 12px;
      border: 1px solid #ccc;
      border-radius: 4px;
      font-size: 14px;
    }
    
    .password-toggle {
      position: absolute;
      right: 8px;
      top: 50%;
      transform: translateY(-50%);
      background: none;
      border: none;
      cursor: pointer;
      color: #666;
    }
    
    .password-error {
      color: #d32f2f;
      font-size: 14px;
      margin: 8px 0;
    }
    
    .password-remember {
      display: flex;
      align-items: center;
      margin-top: 16px;
    }
    
    .password-remember input {
      margin-right: 8px;
    }
    
    .password-dialog-footer {
      display: flex;
      justify-content: flex-end;
      padding: 16px;
      background-color: #f5f5f5;
      border-top: 1px solid #e0e0e0;
    }
    
    .password-dialog-footer button {
      padding: 8px 16px;
      margin-left: 8px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
    }
    
    .password-dialog-footer button.primary {
      background-color: #2196f3;
      color: white;
    }
    
    .password-dialog-footer button:not(.primary) {
      background-color: #e0e0e0;
      color: #333;
    }
  `;
  
  document.head.appendChild(style);
}

// 添加自定义样式
addCustomPasswordStyles();
    </code></pre>
    
    <h3>密码存储与自动填充</h3>
    <pre><code class="language-javascript">
// 实现一个简单的密码存储管理器
class PasswordManager {
  constructor() {
    // 使用 localStorage 存储密码（注意：在生产环境中应使用更安全的方式）
    this.storage = window.localStorage;
    this.prefix = 'pdf_password_';
    
    // 加密密钥（在实际应用中应使用更安全的密钥管理）
    this.encryptionKey = 'your-secret-key';
  }
  
  // 存储特定文档的密码
  savePassword(documentId, password) {
    if (!documentId || !password) {
      return false;
    }
    
    try {
      // 简单加密密码（仅作示例，不够安全）
      const encryptedPassword = this._encrypt(password);
      
      // 存储加密后的密码
      this.storage.setItem(this.prefix + documentId, encryptedPassword);
      return true;
    } catch (error) {
      console.error('保存密码失败:', error);
      return false;
    }
  }
  
  // 获取特定文档的密码
  getPassword(documentId) {
    if (!documentId) {
      return null;
    }
    
    try {
      // 获取加密的密码
      const encryptedPassword = this.storage.getItem(this.prefix + documentId);
      
      if (!encryptedPassword) {
        return null;
      }
      
      // 解密密码
      return this._decrypt(encryptedPassword);
    } catch (error) {
      console.error('获取密码失败:', error);
      return null;
    }
  }
  
  // 删除特定文档的密码
  removePassword(documentId) {
    if (!documentId) {
      return false;
    }
    
    try {
      this.storage.removeItem(this.prefix + documentId);
      return true;
    } catch (error) {
      console.error('删除密码失败:', error);
      return false;
    }
  }
  
  // 清除所有存储的密码
  clearAllPasswords() {
    try {
      // 仅删除我们应用存储的密码
      for (let i = 0; i < this.storage.length; i++) {
        const key = this.storage.key(i);
        if (key.startsWith(this.prefix)) {
          this.storage.removeItem(key);
        }
      }
      return true;
    } catch (error) {
      console.error('清除所有密码失败:', error);
      return false;
    }
  }
  
  // 简单的加密函数（示例用途，不足够安全）
  _encrypt(text) {
    // 在实际应用中，应使用更强大的加密算法
    return btoa(text + this.encryptionKey);
  }
  
  // 简单的解密函数（示例用途，不足够安全）
  _decrypt(encryptedText) {
    try {
      // 在实际应用中，应使用更强大的解密算法
      const decodedText = atob(encryptedText);
      return decodedText.substring(0, decodedText.length - this.encryptionKey.length);
    } catch (error) {
      console.error('解密失败:', error);
      return null;
    }
  }
}

// 创建密码管理器实例
const passwordManager = new PasswordManager();

// 扩展密码提示，支持记住密码
function enhancePasswordPrompt(passwordPrompt, passwordManager) {
  // 保存原始验证方法的引用
  const originalVerify = passwordPrompt.verify.bind(passwordPrompt);
  
  // 重写验证方法
  passwordPrompt.verify = function(password) {
    // 调用原始验证方法
    return originalVerify(password).then(success => {
      // 如果验证成功且用户选择了记住密码
      if (success && document.getElementById('passwordRemember').checked) {
        // 获取当前文档的唯一标识符
        const documentId = getDocumentFingerprint();
        // 保存密码
        passwordManager.savePassword(documentId, password);
      }
      return success;
    });
  };
  
  // 扩展打开方法，添加自动填充功能
  const originalOpen = passwordPrompt.open.bind(passwordPrompt);
  
  passwordPrompt.open = function() {
    // 尝试自动填充密码
    const documentId = getDocumentFingerprint();
    const savedPassword = passwordManager.getPassword(documentId);
    
    // 如果找到保存的密码，自动填充并提交
    if (savedPassword) {
      // 调用原始打开方法
      return originalOpen().then(() => {
        // 填充密码字段
        document.getElementById('password').value = savedPassword;
        // 勾选"记住密码"复选框
        document.getElementById('passwordRemember').checked = true;
        // 自动提交
        return this.verify(savedPassword);
      });
    }
    
    // 如果没有保存的密码，正常打开对话框
    return originalOpen();
  };
  
  // 辅助函数：获取当前 PDF 文档的指纹
  function getDocumentFingerprint() {
    // 这里应该返回当前 PDF 文档的唯一标识符
    // 在实际应用中，可以使用 PDF 文档的 fingerprint 属性
    return window.PDFViewerApplication?.pdfDocument?.fingerprint || 'unknown';
  }
  
  return passwordPrompt;
}

// 增强密码提示，添加记住密码功能
enhancePasswordPrompt(customPasswordPrompt, passwordManager);
    </code></pre>
  </div>

  <!-- 注意事项 -->
  <div id="notes">
    <h2>注意事项</h2>
    
    <ul>
      <li>密码提示应在用户界面中明确显示，并提供清晰的指示，说明需要用户密码或所有者密码。</li>
      <li>使用 PasswordPrompt 组件时，应确保提供适当的本地化支持，以便在不同语言环境中正确显示提示信息。</li>
      <li>为了提高安全性，不应将密码明文存储在客户端。如需实现"记住密码"功能，应使用安全的密码存储方式。</li>
      <li>在嵌入式环境中使用时，可能需要调整对话框的大小和位置，以适应容器的尺寸。</li>
      <li>密码输入字段应支持密码可见性切换，以帮助用户确认复杂密码的输入是否正确。</li>
      <li>尽量避免过多的密码尝试，考虑实现输入错误次数限制或延迟增加的机制。</li>
      <li>密码对话框应支持键盘导航和辅助功能，确保所有用户都能够轻松访问。</li>
      <li>在移动设备上使用时，应确保输入字段和按钮足够大，以便于触摸操作。</li>
    </ul>
  </div>

  <script>
    // 在页面加载完成后初始化 Mermaid
    document.addEventListener('DOMContentLoaded', function() {
      mermaid.initialize({ startOnLoad: true });
      
      // 生成目录
      generateTOC();
    });
  </script>
</body>
</html>
 