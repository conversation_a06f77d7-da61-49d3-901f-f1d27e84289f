/* 文档样式表 */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

h1, h2, h3, h4 {
  color: #2c3e50;
}

h1 {
  border-bottom: 2px solid #eee;
  padding-bottom: 10px;
  margin-bottom: 20px;
}

h2 {
  margin-top: 30px;
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
}

/* 导航栏 */
.navbar {
  background-color: #f8f9fa;
  padding: 10px 0;
  margin-bottom: 20px;
  border-radius: 4px;
}

.navbar a {
  margin-right: 15px;
  color: #3498db;
  text-decoration: none;
  font-weight: 500;
}

.navbar a:hover {
  text-decoration: underline;
}

/* 代码块 */
pre {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
  overflow-x: auto;
  font-family: Consolas, Monaco, 'Andale Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
}

code {
  font-family: Consolas, Monaco, 'Andale Mono', monospace;
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 0.9em;
}

/* 方法块 */
.method-block {
  margin-bottom: 30px;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.method-header {
  background-color: #f8f9fa;
  padding: 10px 15px;
  border-bottom: 1px solid #ddd;
}

.method-name {
  font-weight: bold;
  color: #2c3e50;
  margin: 0;
}

.method-content {
  padding: 15px;
}

/* 流程图 */
.mermaid {
  background-color: white;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin: 20px 0;
}

/* 模块信息 */
.module-info {
  background-color: #f9f9f9;
  border-left: 4px solid #3498db;
  padding: 15px;
  margin-bottom: 20px;
}

/* 返回顶部按钮 */
.back-to-top {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  font-size: 20px;
  cursor: pointer;
  display: none;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.back-to-top:hover {
  background-color: #2980b9;
}

/* 响应式调整 */
@media (max-width: 768px) {
  body {
    padding: 10px;
  }
  
  pre {
    padding: 10px;
    font-size: 13px;
  }
  
  .method-header, .method-content {
    padding: 10px;
  }
} 