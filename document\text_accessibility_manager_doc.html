<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>TextAccessibilityManager 文档 - PDF.js</title>
  <link rel="stylesheet" href="doc_styles.css">
</head>
<body>
  <header>
    <h1>TextAccessibilityManager</h1>
    <p class="module-description">文本可访问性管理器，提升PDF文本的可访问性支持</p>
    <a href="index.html" class="back-link">返回模块列表</a>
  </header>

  <div class="content">
    <section class="module-intro">
      <h2>模块介绍</h2>
      <p>TextAccessibilityManager 是 PDF.js 中负责提升文本可访问性的组件。它的主要职责是管理 PDF 文本层的无障碍访问，通过操作 ARIA 属性将文本内容与注释、链接等交互元素关联起来，从而使屏幕阅读器和其他辅助技术能够正确地解释和朗读 PDF 文档内容。它在 PDF 内容层和辅助功能之间搭建桥梁，帮助视障用户获取文本信息和交互元素。</p>
      
      <div class="module-diagram">
        <h3>组件位置与工作流程</h3>
        <div class="mermaid">
          graph TD
            PDFViewer[PDFViewer] --> PDFPageView[PDFPageView]
            PDFPageView --> TextLayer[TextLayer]
            PDFPageView --> AnnotationLayer[AnnotationLayer]
            TextLayer --> TextAccessibilityManager[TextAccessibilityManager]
            AnnotationLayer --> TextAccessibilityManager
            TextAccessibilityManager -->|管理| AriaAttributes[ARIA属性]
        </div>
      </div>
    </section>

    <section class="properties">
      <h2>属性</h2>
      
      <h3>私有属性</h3>
      <div class="property">
        <h4>#enabled</h4>
        <p>标志位，表示文本可访问性管理器是否已启用。初始为 false。</p>
      </div>

      <div class="property">
        <h4>#textChildren</h4>
        <p>存储文本层中的文本元素数组，按照文档中的位置排序。</p>
      </div>

      <div class="property">
        <h4>#textNodes</h4>
        <p>Map 对象，存储元素 ID 到文本子元素索引的映射关系。用于快速查找文本元素。</p>
      </div>

      <div class="property">
        <h4>#waitingElements</h4>
        <p>Map 对象，存储待处理的元素，当可访问性管理器启用时会处理这些元素。</p>
      </div>
    </section>

    <section class="methods">
      <h2>方法</h2>

      <div class="method">
        <h3>setTextMapping(textDivs)</h3>
        <p>设置文本映射，将文本 div 元素数组保存到管理器中，以便后续处理。</p>
        <h4>参数：</h4>
        <ul>
          <li><code>textDivs</code> - 包含文本内容的 div 元素数组</li>
        </ul>
        
        <pre><code>// 设置文本映射
textAccessibilityManager.setTextMapping(textDivs);</code></pre>
      </div>

      <div class="method">
        <h3>enable()</h3>
        <p>启用文本可访问性功能，对文本元素进行排序并处理待处理的元素。</p>
        <pre><code>// 启用文本可访问性
textAccessibilityManager.enable();</code></pre>

        <div class="mermaid">
          sequenceDiagram
            participant App as 应用程序
            participant TAM as TextAccessibilityManager
            
            App->>TAM: enable()
            alt 已经启用
              TAM-->>App: 抛出错误
            else 未设置textChildren
              TAM-->>App: 抛出错误
            else 正常启用
              TAM->>TAM: #enabled = true
              TAM->>TAM: 复制并排序textChildren
              loop 遍历textNodes
                TAM->>TAM: #addIdToAriaOwns(id, textChild)
              end
              loop 遍历waitingElements
                TAM->>TAM: addPointerInTextLayer(element, isRemovable)
              end
              TAM->>TAM: 清空waitingElements
            end
        </div>
      </div>

      <div class="method">
        <h3>disable()</h3>
        <p>禁用文本可访问性功能，清除内部状态。</p>
        <pre><code>// 禁用文本可访问性
textAccessibilityManager.disable();</code></pre>
      </div>

      <div class="method">
        <h3>addPointerInTextLayer(element, isRemovable)</h3>
        <p>在文本层中为元素添加可访问性支持，将元素的 ID 关联到相应的文本元素上。</p>
        <h4>参数：</h4>
        <ul>
          <li><code>element</code> - 要添加到文本层的元素</li>
          <li><code>isRemovable</code> - 是否可移除的标志</li>
        </ul>
        <h4>返回：</h4>
        <p>如果父节点包含 markedContent 类，返回父节点 ID；否则返回 null</p>
        
        <pre><code>// 在文本层添加元素指针
const parentId = textAccessibilityManager.addPointerInTextLayer(annotationElement, true);</code></pre>
      </div>

      <div class="method">
        <h3>removePointerInTextLayer(element)</h3>
        <p>从文本层中移除元素的可访问性关联。</p>
        <h4>参数：</h4>
        <ul>
          <li><code>element</code> - 要从文本层移除的元素</li>
        </ul>
        
        <pre><code>// 从文本层移除元素指针
textAccessibilityManager.removePointerInTextLayer(annotationElement);</code></pre>
      </div>

      <div class="method">
        <h3>moveElementInDOM(container, element, contentElement, isRemovable)</h3>
        <p>将元素移动到 DOM 中的正确位置，根据其内容位置进行排序，并添加可访问性支持。</p>
        <h4>参数：</h4>
        <ul>
          <li><code>container</code> - 容器元素</li>
          <li><code>element</code> - 要移动的元素</li>
          <li><code>contentElement</code> - 用于比较位置的内容元素（可选）</li>
          <li><code>isRemovable</code> - 是否可移除的标志</li>
        </ul>
        <h4>返回：</h4>
        <p>如果元素被添加到文本层，返回其父节点的 ID；否则返回 null</p>
        
        <pre><code>// 移动元素并添加可访问性支持
const parentId = textAccessibilityManager.moveElementInDOM(
  annotationContainer,
  annotationElement,
  contentElement,
  true
);</code></pre>
      </div>

      <div class="method">
        <h3>static #compareElementPositions(e1, e2)</h3>
        <p>静态私有方法，比较两个元素在页面中的位置关系，用于对元素进行排序。</p>
        <h4>参数：</h4>
        <ul>
          <li><code>e1</code> - 第一个元素</li>
          <li><code>e2</code> - 第二个元素</li>
        </ul>
        <h4>返回：</h4>
        <p>如果 e1 在 e2 前面，返回负数；如果 e1 在 e2 后面，返回正数；如果位置相同，根据水平位置决定</p>
      </div>

      <div class="method">
        <h3>#addIdToAriaOwns(id, node)</h3>
        <p>私有方法，将指定 ID 添加到节点的 aria-owns 属性，建立无障碍关联。</p>
        <h4>参数：</h4>
        <ul>
          <li><code>id</code> - 要添加的元素 ID</li>
          <li><code>node</code> - 要添加 aria-owns 属性的节点</li>
        </ul>
      </div>
    </section>

    <section class="usage">
      <h2>使用示例</h2>

      <div class="example">
        <h3>基本用法</h3>
        <pre><code>// 创建文本可访问性管理器
const textAccessibilityManager = new TextAccessibilityManager();

// 设置文本映射（通常由TextLayer调用）
textAccessibilityManager.setTextMapping(textDivs);

// 启用文本可访问性
textAccessibilityManager.enable();

// 在文本层添加注释元素
const parentId = textAccessibilityManager.addPointerInTextLayer(
  annotationElement,
  true
);

// 在DOM中移动元素并添加可访问性支持
const linkElement = document.createElement("a");
textAccessibilityManager.moveElementInDOM(
  annotationContainer,
  linkElement,
  linkElement,
  true
);

// 当不再需要时禁用
textAccessibilityManager.disable();</code></pre>
      </div>

      <div class="example">
        <h3>与 PDFPageView 结合使用</h3>
        <pre><code>// 在 PDFPageView 中使用 TextAccessibilityManager
class PDFPageView {
  constructor(options) {
    // ...其他初始化代码...
    
    this.textAccessibilityManager = options.textAccessibilityManager;
  }
  
  // 创建文本层
  _renderTextLayer() {
    // ...创建文本层的代码...
    
    // 设置文本映射
    if (this.textAccessibilityManager) {
      this.textAccessibilityManager.setTextMapping(textDivs);
    }
    
    // 在文本层渲染完成后启用可访问性
    if (this.textAccessibilityManager) {
      this.textAccessibilityManager.enable();
    }
  }
  
  // 创建注释层
  _renderAnnotationLayer() {
    // ...创建注释层的代码...
    
    // 使用文本可访问性管理器处理注释元素
    if (this.textAccessibilityManager) {
      for (const annotationElement of annotationElements) {
        this.textAccessibilityManager.moveElementInDOM(
          annotationContainer,
          annotationElement,
          annotationElement.firstChild || annotationElement,
          /* isRemovable */ true
        );
      }
    }
  }
}</code></pre>
      </div>
    </section>

    <section class="implementation-notes">
      <h2>实现说明</h2>
      <div class="note">
        <h3>工作原理</h3>
        <p>TextAccessibilityManager 通过以下步骤提升 PDF 文档的可访问性：</p>
        <ol>
          <li>接收并存储文本层的文本元素（通过 setTextMapping 方法）</li>
          <li>对文本元素按照在文档中的位置进行排序</li>
          <li>将注释、链接等交互元素与相应的文本元素关联起来（通过 aria-owns 属性）</li>
          <li>根据内容位置，将交互元素放置在 DOM 中的正确位置，以确保屏幕阅读器按正确顺序访问内容</li>
        </ol>
        <p>这种方法使得屏幕阅读器可以在朗读文本的同时，识别与之关联的交互元素，从而提供完整的文档体验。</p>
      </div>
      
      <div class="note">
        <h3>元素位置比较</h3>
        <p>该类使用 #compareElementPositions 方法来比较元素在页面中的位置关系。比较逻辑如下：</p>
        <ul>
          <li>如果元素没有尺寸（width 和 height 为 0），会被特殊处理</li>
          <li>首先根据垂直位置关系（上下）进行比较</li>
          <li>如果垂直位置关系不明确，则根据水平位置（左右）进行比较</li>
        </ul>
        <p>这种比较方法模拟了阅读顺序（从上到下，从左到右），确保元素按照自然阅读顺序排列。</p>
      </div>
      
      <div class="note">
        <h3>ARIA 属性的使用</h3>
        <p>TextAccessibilityManager 主要使用 aria-owns 属性来建立文本元素与交互元素之间的关联。当一个文本元素通过 aria-owns 引用一个交互元素时，屏幕阅读器会将这两个元素视为一个整体，确保用户能够感知到与文本相关的交互可能性。</p>
        <p>在添加关联时，管理器会：</p>
        <ul>
          <li>将交互元素的 ID 添加到相应文本元素的 aria-owns 属性中</li>
          <li>移除文本元素的 role="presentation" 属性（如果存在），以确保屏幕阅读器不会忽略它</li>
        </ul>
      </div>
    </section>

    <section class="best-practices">
      <h2>最佳实践</h2>
      <div class="practice">
        <h3>生命周期管理</h3>
        <p>TextAccessibilityManager 的生命周期管理非常重要：</p>
        <ul>
          <li>在文本层渲染完成后，先调用 setTextMapping 设置文本映射</li>
          <li>然后调用 enable 启用可访问性功能</li>
          <li>在页面销毁前调用 disable 释放资源</li>
        </ul>
        <p>这样可以确保可访问性功能正常工作，并避免内存泄漏。</p>
      </div>
      
      <div class="practice">
        <h3>元素 ID 管理</h3>
        <p>要使 TextAccessibilityManager 正常工作，所有需要添加到文本层的元素必须具有唯一的 ID 属性。确保：</p>
        <ul>
          <li>每个注释、链接或其他交互元素都有唯一的 ID</li>
          <li>ID 在页面范围内保持唯一性</li>
          <li>不要手动修改已添加到文本层的元素的 ID</li>
        </ul>
      </div>
      
      <div class="practice">
        <h3>性能考虑</h3>
        <p>在处理大型文档时，可访问性处理可能会影响性能，特别是当有大量文本元素和注释时。建议：</p>
        <ul>
          <li>仅在需要可访问性支持时启用 TextAccessibilityManager</li>
          <li>考虑懒加载策略，只处理当前可见页面的可访问性</li>
          <li>在滚动或缩放等频繁操作期间，考虑暂时禁用可访问性处理，然后在操作完成后重新启用</li>
        </ul>
      </div>
      
      <div class="practice">
        <h3>错误处理</h3>
        <p>在使用 TextAccessibilityManager 时，注意以下可能的错误情况：</p>
        <ul>
          <li>在设置文本映射前调用 enable 方法会抛出错误</li>
          <li>重复调用 enable 方法会抛出错误</li>
          <li>如果元素没有 ID，addPointerInTextLayer 方法会返回 null</li>
        </ul>
        <p>确保在适当的时间点调用适当的方法，并处理可能的错误情况。</p>
      </div>
    </section>
  </div>

  <script src="doc_script.js"></script>
  <script src="js/mermaid.js"></script>
  <script>
    mermaid.initialize({ startOnLoad: true, theme: 'neutral' });
  </script>
</body>
</html> 